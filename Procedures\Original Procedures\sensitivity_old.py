"""
This program is intended to test receiver sensitivity for TCAS.

Once the RF_Card is initialized by programming the PLLs, front end switches,
and ADCs, the observation bit FIFO is accessed for an array of index values.

The average data is plotted and compression and MTL calculated on the output 
graph.
"""
# main
import sys
import os
import time
from TXDLib.Procedures import CSVFile
from TXDLib.Procedures import python_observation_point_api as a
#import sensitivity_plot as plot
from TXDLib.Procedures import arrays

def getRxData(aterm, obsValue, rxOffset=[0,0], direct=True):
    aterm.logMessage(1, "Procedure Started: OBSValue %s" % str(obsValue))
    variable_dict = {'dec_format':True,'hex_format':False,
                    'bin_format':False,'power_sweep':False,
                    'freq_sweep':False,'UAT':False,
                    'no_sweep':False, 'start': -120,
                    'end': 0, 'values': []}
    
    variable_dict['dec_csv'] = CSVFile.create_csv()

    variable_dict['value'] = str(obsValue)
    variable_dict['values'].append(variable_dict['value'])

    data = []

    # sample array
    samples = []
    for i in range(16384):
        samples.append(i)
    samples.insert(0,'SAMPLE')
    variable_dict['dec_csv'].tuple_add(samples)
    
    if direct==True:
        variable_dict['csv_data'] = a.request_direct(0, int(variable_dict['value']))
    else:
        variable_dict['csv_data'] = a.request(0, int(variable_dict['value']))

    if obsValue==26:
        (variable_dict['channel_1'], variable_dict['channel_2']) = arrays.initiate(variable_dict)
        data.insert(0,((average(variable_dict['channel_1'][1:]))/44.5 - 116.0)+rxOffset[0])
        data.insert(1,((average(variable_dict['channel_2'][1:]))/44.5 - 116.0)+rxOffset[1])
        aterm.logMessage(1, "data[0]: %s, data[1]: %s" % (str(data[0]), str(data[1])))
    elif obsValue==0:
        print(arrays.initiate(variable_dict))
        (variable_dict['channel_1'], variable_dict['channel_2'], variable_dict['channel_3'], variable_dict['channel_4']) = arrays.initiate(variable_dict)
        data.insert(0,(average(variable_dict['channel_1'][1:])))
        data.insert(1,(average(variable_dict['channel_2'][1:])))
        data.insert(2,(average(variable_dict['channel_3'][1:])))
        data.insert(3,(average(variable_dict['channel_4'][1:])))
        aterm.logMessage(1, "E1 AVG: %s, E2 AVG: %s, E3 AVG: %s, E4 AVG: %s" % (str(data[0]), str(data[1]),str(data[2]), str(data[3])))
    elif obsValue==19:
        (variable_dict['channel_1'], variable_dict['channel_2']) = arrays.initiate(variable_dict)
        data.insert(0,((average(variable_dict['channel_1'][1:])) - 130.0)+rxOffset[0])
        data.insert(1,((average(variable_dict['channel_2'][1:])) - 130.0)+rxOffset[1])
        aterm.logMessage(1, "data[0]: %s, data[1]: %s" % (str(data[0]), str(data[1])))
    else:
        (variable_dict['channel_1'], variable_dict['channel_2']) = arrays.initiate(variable_dict)
        data.insert(0,((average(variable_dict['channel_1'][1:]))/2.75 - 111.0)+rxOffset[0])
        data.insert(1,((average(variable_dict['channel_2'][1:]))/2.75 - 111.0)+rxOffset[1])
        aterm.logMessage(1, "data[0]: %s, data[1]: %s" % (str(data[0]), str(data[1])))
    
    aterm.logMessage(1, "Procedure Ended")
    return data

def plotRxData(aterm, obsValue, PLOT_DATA_E1, PLOT_DATA_E2, power_levels, datadir):
    plot_data = CSVFile.create_csv()
    if "Selectivity" in datadir:
        power_levels.insert(0,'Frequency')
    else:
        power_levels.insert(0,'Power Level')
    plot_data.tuple_add(power_levels)
    
    #if str(obsValue) == '8':
    #    e1_header = 'TCAS PRIMARY ' + datadir.split("\\")[-1]
    #    e2_header = 'TCAS SECONDARY '+ datadir.split("\\")[-1]
    #elif str(obsValue) == '17':
    #    e1_header = 'XPDR TOP '+ datadir.split("\\")[-1]
    #    e2_header = 'XPDR BOTTOM '+ datadir.split("\\")[-1]
    #elif str(obsValue) == '25':
    #    e1_header = 'DME LOG MAG '+ datadir.split("\\")[-1]
    #    e2_header = 'DME LOG AMP '+ datadir.split("\\")[-1]
    #elif str(obsValue) == '19':
    #    e1_header = 'UAT MAG '+ datadir.split("\\")[-1]
    #    e2_header = 'UAT MAG '+ datadir.split("\\")[-1]
    e1_header = datadir.split("\\")[-1]
    PLOT_DATA_E1.insert(0, e1_header)
    plot_data.tuple_add(PLOT_DATA_E1)
    #PLOT_DATA_E2.insert(0, e2_header)
    #plot_data.tuple_add(PLOT_DATA_E2)

    if "Sensitivity" in datadir:
        plot_data.tuple_add(power_levels)

    savedir = plot_data.print_out(obsValue,datadir)
    aterm.logMessage(1, "CSV File Saved: %s" % savedir)


def average(csv_data):
    SUMMATION = 0
    for i in range(len(csv_data)):
        SUMMATION = SUMMATION + int(csv_data[i])
    if (len(csv_data) != 0):
        average = SUMMATION/len(csv_data)
    else:
        average = 0
    # average = average/2.64 # 2.64 counts per dB
    return average

def getNoiseFloor(aterm, data):
    noisefloor = (data[0]+data[1]+data[2]+data[3]+data[4])/5
    aterm.logMessage(1, "Noise floor: %s" % str(noisefloor))
    return noisefloor

def getLinearity(aterm, powerLevels, data, start, end):
    lineartyPL = powerLevels[powerLevels.index(start):powerLevels.index(end+1)] 
    lineartyMeas = data[powerLevels.index(start):powerLevels.index(end+1)]
    linearity = []
    for i in range(len(lineartyPL)):
        linearity.append(lineartyPL[i]-lineartyMeas[i])
    linearityResult = max(abs(min(linearity)), abs(max(linearity)))
    aterm.logMessage(1, "Linearity: %s" % str(linearityResult))
    return linearityResult

def getCompression(aterm, powerLevels, data, start):
    compressionPL = powerLevels[powerLevels.index(start):] 
    compressionMeas = data[powerLevels.index(start):]
    compression = []
    for i in range(len(compressionPL)):
        compression = compressionPL[i]-compressionMeas[i]
        if compression >= 1:
            aterm.logMessage(1, "P1dB at %s: %s" % (str(compressionPL[i]), str(compressionMeas[i])))
            return list((compressionPL[i], compressionMeas[i]))

def searchSelectivitySpectrum(data,carrierIndex,bw,length, freq,limit):
    carrierPwr = data[carrierIndex]
    if length==-1:
        mylength = carrierIndex-bw+1
    else:
        mylength = length
    print("Carrier Pwr: %s @ %s MHz"%(str(carrierPwr),str(freq[carrierIndex])))
    maxPwr = data[carrierIndex-bw]
    for i in range(1,mylength):
        currPwr = data[carrierIndex-bw-i]
        if currPwr>maxPwr:
            maxPwr=currPwr
    print("max pwr from %sMHz to %sMHz: %s"%(str(freq[carrierIndex-bw-mylength+1]),freq[carrierIndex-bw],str(maxPwr)))
    if maxPwr-carrierPwr>limit:
        print("failed %sdB limit %s"%(str(limit),str(maxPwr-carrierPwr)))
    else:
        print("passed %sdB limit %s"%(str(limit),str(maxPwr-carrierPwr)))

    if length==-1:
        mylength = len(data)-(carrierIndex+bw)
    else:
        mylength = length
    maxPwr = data[carrierIndex+bw]
    for i in range(1,mylength):
        currPwr = data[carrierIndex+bw+i]
        if currPwr>maxPwr:
            maxPwr=currPwr
    print("max pwr from %sMHz to %sMHz: %s"%(str(freq[carrierIndex+bw]),str(freq[carrierIndex+bw+mylength-1]),str(maxPwr)))
    if maxPwr-carrierPwr>limit:
        print("failed %sdB limit %s"%(str(limit),str(maxPwr-carrierPwr)))
    else:
        print("passed %sdB limit %s"%(str(limit),str(maxPwr-carrierPwr)))