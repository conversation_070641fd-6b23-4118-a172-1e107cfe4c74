#!/usr/bin/env python3
"""
Mock N6700 DC Power Supply Handler for Testing
Simulates the behavior of Keysight N6700 modular DC power supply
"""

import time
import random
from unittest.mock import Mock

class MockDCPowerSupply:
    """Mock implementation of N6700 DC power supply handler"""
    
    def __init__(self, resource_manager=None):
        self.resourceManager = resource_manager or Mock()
        self.connected = False
        self.channels = {}
        self.num_channels = 4  # Typical 4-channel configuration
        
        # Initialize channels
        self._initialize_channels()
        
    def _initialize_channels(self):
        """Initialize all channels to default states"""
        for channel in range(1, self.num_channels + 1):
            self.channels[channel] = {
                'voltage_set': 0.0,
                'current_set': 0.0,
                'voltage_measured': 0.0,
                'current_measured': 0.0,
                'output_enabled': False,
                'voltage_limit': 20.0,
                'current_limit': 5.0,
                'ovp_enabled': True,
                'ocp_enabled': True,
                'ovp_level': 22.0,
                'ocp_level': 5.5,
                'protection_tripped': False
            }
            
    def connect(self, address="USB0::0x0957::0x8B18::INSTR"):
        """Simulate connection to power supply"""
        time.sleep(0.2)  # Simulate connection time
        self.connected = True
        self.resourceManager.logMessage(1, f"Mock N6700 connected to {address}")
        return True
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        self.resourceManager.logMessage(1, "Mock N6700 disconnected")
        
    def write(self, command):
        """Simulate writing command to power supply"""
        if not self.connected:
            raise Exception("Power supply not connected")
            
        time.sleep(0.01)  # Simulate command processing
        self.resourceManager.logMessage(1, f"Mock N6700 command: {command}")
        
        # Parse and simulate command effects
        self._parse_command(command)
        
    def _parse_command(self, command):
        """Parse SCPI command and update internal state"""
        command = command.upper().strip()
        
        if "VOLT" in command and not "?" in command:
            # Voltage setting command
            parts = command.split()
            voltage = float(parts[-1])
            channel = self._extract_channel(command)
            if channel:
                self.channels[channel]['voltage_set'] = voltage
                if self.channels[channel]['output_enabled']:
                    self.channels[channel]['voltage_measured'] = voltage * (1 + random.uniform(-0.001, 0.001))
                    
        elif "CURR" in command and not "?" in command:
            # Current setting command
            parts = command.split()
            current = float(parts[-1])
            channel = self._extract_channel(command)
            if channel:
                self.channels[channel]['current_set'] = current
                
        elif "OUTP" in command and "ON" in command:
            # Output enable
            channel = self._extract_channel(command)
            if channel:
                self.channels[channel]['output_enabled'] = True
                self._update_measurements(channel)
                
        elif "OUTP" in command and "OFF" in command:
            # Output disable
            channel = self._extract_channel(command)
            if channel:
                self.channels[channel]['output_enabled'] = False
                self.channels[channel]['voltage_measured'] = 0.0
                self.channels[channel]['current_measured'] = 0.0
                
        elif "PROT:CLE" in command:
            # Clear protection
            channel = self._extract_channel(command)
            if channel:
                self.channels[channel]['protection_tripped'] = False
                
    def _extract_channel(self, command):
        """Extract channel number from SCPI command"""
        # Look for (@1), (@2), etc.
        if "@" in command:
            start = command.find("@") + 1
            end = command.find(")", start)
            if end > start:
                try:
                    return int(command[start:end])
                except ValueError:
                    pass
        return 1  # Default to channel 1
        
    def _update_measurements(self, channel):
        """Update measured values for a channel"""
        if self.channels[channel]['output_enabled']:
            # Simulate realistic measurements with small errors
            voltage_error = random.uniform(-0.001, 0.001)
            current_error = random.uniform(-0.001, 0.001)
            
            self.channels[channel]['voltage_measured'] = (
                self.channels[channel]['voltage_set'] * (1 + voltage_error)
            )
            
            # Simulate load current (random between 10% and 90% of set current)
            base_current = self.channels[channel]['current_set'] * random.uniform(0.1, 0.9)
            self.channels[channel]['current_measured'] = base_current * (1 + current_error)
            
    def query(self, command):
        """Simulate querying power supply for data"""
        if not self.connected:
            raise Exception("Power supply not connected")
            
        time.sleep(0.01)
        self.resourceManager.logMessage(1, f"Mock N6700 query: {command}")
        
        command = command.upper().strip()
        channel = self._extract_channel(command)
        
        if "*IDN?" in command:
            return "Keysight Technologies,N6700C,MY12345678,A.01.02"
        elif "VOLT?" in command:
            if channel in self.channels:
                return str(self.channels[channel]['voltage_measured'])
        elif "CURR?" in command:
            if channel in self.channels:
                return str(self.channels[channel]['current_measured'])
        elif "OUTP?" in command:
            if channel in self.channels:
                return "1" if self.channels[channel]['output_enabled'] else "0"
        elif "PROT:TRIP?" in command:
            if channel in self.channels:
                return "1" if self.channels[channel]['protection_tripped'] else "0"
        elif "SYST:ERR?" in command:
            return "0,\"No error\""
            
        return "0"
        
    def setVoltage(self, channel, voltage):
        """Set output voltage for a channel"""
        if 1 <= channel <= self.num_channels:
            if 0 <= voltage <= self.channels[channel]['voltage_limit']:
                self.channels[channel]['voltage_set'] = voltage
                self.write(f"VOLT {voltage},(@{channel})")
                if self.channels[channel]['output_enabled']:
                    self._update_measurements(channel)
                self.resourceManager.logMessage(1, f"Mock N6700 CH{channel} voltage: {voltage}V")
            else:
                raise ValueError(f"Voltage out of range: {voltage}")
        else:
            raise ValueError(f"Invalid channel: {channel}")
            
    def setCurrent(self, channel, current):
        """Set current limit for a channel"""
        if 1 <= channel <= self.num_channels:
            if 0 <= current <= self.channels[channel]['current_limit']:
                self.channels[channel]['current_set'] = current
                self.write(f"CURR {current},(@{channel})")
                self.resourceManager.logMessage(1, f"Mock N6700 CH{channel} current: {current}A")
            else:
                raise ValueError(f"Current out of range: {current}")
        else:
            raise ValueError(f"Invalid channel: {channel}")
            
    def enableOutput(self, channel):
        """Enable output for a channel"""
        if 1 <= channel <= self.num_channels:
            self.channels[channel]['output_enabled'] = True
            self.write(f"OUTP ON,(@{channel})")
            self._update_measurements(channel)
            self.resourceManager.logMessage(1, f"Mock N6700 CH{channel} output enabled")
        else:
            raise ValueError(f"Invalid channel: {channel}")
            
    def disableOutput(self, channel):
        """Disable output for a channel"""
        if 1 <= channel <= self.num_channels:
            self.channels[channel]['output_enabled'] = False
            self.channels[channel]['voltage_measured'] = 0.0
            self.channels[channel]['current_measured'] = 0.0
            self.write(f"OUTP OFF,(@{channel})")
            self.resourceManager.logMessage(1, f"Mock N6700 CH{channel} output disabled")
        else:
            raise ValueError(f"Invalid channel: {channel}")
            
    def measureVoltage(self, channel):
        """Measure output voltage"""
        if 1 <= channel <= self.num_channels:
            self._update_measurements(channel)
            voltage = self.channels[channel]['voltage_measured']
            self.resourceManager.logMessage(1, f"Mock N6700 CH{channel} measured voltage: {voltage}V")
            return voltage
        else:
            raise ValueError(f"Invalid channel: {channel}")
            
    def measureCurrent(self, channel):
        """Measure output current"""
        if 1 <= channel <= self.num_channels:
            self._update_measurements(channel)
            current = self.channels[channel]['current_measured']
            self.resourceManager.logMessage(1, f"Mock N6700 CH{channel} measured current: {current}A")
            return current
        else:
            raise ValueError(f"Invalid channel: {channel}")
            
    def getOutputState(self, channel):
        """Get output enable state"""
        if 1 <= channel <= self.num_channels:
            return self.channels[channel]['output_enabled']
        else:
            raise ValueError(f"Invalid channel: {channel}")
            
    def setOvervoltageProtection(self, channel, level):
        """Set overvoltage protection level"""
        if 1 <= channel <= self.num_channels:
            self.channels[channel]['ovp_level'] = level
            self.resourceManager.logMessage(1, f"Mock N6700 CH{channel} OVP: {level}V")
        else:
            raise ValueError(f"Invalid channel: {channel}")
            
    def setOvercurrentProtection(self, channel, level):
        """Set overcurrent protection level"""
        if 1 <= channel <= self.num_channels:
            self.channels[channel]['ocp_level'] = level
            self.resourceManager.logMessage(1, f"Mock N6700 CH{channel} OCP: {level}A")
        else:
            raise ValueError(f"Invalid channel: {channel}")
            
    def clearProtection(self, channel):
        """Clear protection trip"""
        if 1 <= channel <= self.num_channels:
            self.channels[channel]['protection_tripped'] = False
            self.write(f"PROT:CLE (@{channel})")
            self.resourceManager.logMessage(1, f"Mock N6700 CH{channel} protection cleared")
        else:
            raise ValueError(f"Invalid channel: {channel}")
            
    def getAllChannelStates(self):
        """Get states of all channels"""
        return self.channels.copy()
        
    def reset(self):
        """Reset power supply to default state"""
        self.resourceManager.logMessage(1, "Mock N6700 Reset")
        self._initialize_channels()
        time.sleep(0.2)  # Simulate reset time
        
    def close(self):
        """Close connection to power supply"""
        # Turn off all outputs before closing
        for channel in range(1, self.num_channels + 1):
            if self.channels[channel]['output_enabled']:
                self.disableOutput(channel)
        self.disconnect()
