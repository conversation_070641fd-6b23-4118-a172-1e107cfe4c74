#!/usr/bin/env python3
"""
Unit Tests for B4500C Power Meter Handler
Tests both original functionality and optimization compatibility
"""

import unittest
import time
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from tests.mocks.mock_power_meter import MockPowerMeter
from unittest.mock import Mock, patch


class TestB4500CPowerMeter(unittest.TestCase):
    """Test cases for B4500C Power Meter handler"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_rm = Mock()
        self.power_meter = MockPowerMeter(self.mock_rm)
        self.power_meter.connect("USB0::0x0957::0x0B0B::INSTR")
        
    def tearDown(self):
        """Clean up after tests"""
        if self.power_meter.connected:
            self.power_meter.disconnect()
            
    def test_connection(self):
        """Test power meter connection functionality"""
        # Test connection status
        self.assertTrue(self.power_meter.connected)
        
        # Test disconnection
        self.power_meter.disconnect()
        self.assertFalse(self.power_meter.connected)
        
        # Test reconnection
        result = self.power_meter.connect("USB0::0x0957::0x0B0B::INSTR")
        self.assertTrue(result)
        self.assertTrue(self.power_meter.connected)
        
    def test_basic_commands(self):
        """Test basic SCPI command functionality"""
        # Test write command
        self.power_meter.write("*RST")
        self.assertEqual(self.power_meter.last_command, "*RST")
        
        # Test query command
        idn = self.power_meter.query("*IDN?")
        self.assertIn("B4500C", idn)
        
    def test_power_measurements(self):
        """Test power measurement functions"""
        print("\n=== Testing Power Measurements ===")
        
        # Test single power measurement
        power = self.power_meter.measurePower()
        self.assertIsInstance(power, float)
        self.assertGreater(power, -100.0)  # Reasonable power range
        self.assertLess(power, 50.0)
        
        print(f"Single power measurement: {power:.2f} dBm")
        
        # Test multiple power measurements
        powers = self.power_meter.measurePowerArray(10)
        self.assertEqual(len(powers), 10)
        for p in powers:
            self.assertIsInstance(p, float)
            
        avg_power = sum(powers) / len(powers)
        print(f"Average power (10 samples): {avg_power:.2f} dBm")
        
    def test_frequency_configuration(self):
        """Test frequency configuration"""
        # Test setting frequency
        test_freq = 1030.0e6  # 1030 MHz
        self.power_meter.setFrequency(test_freq)
        
        # Verify frequency was set
        freq = self.power_meter.getFrequency()
        self.assertAlmostEqual(freq, test_freq, delta=1000)  # Within 1 kHz
        
        print(f"Frequency set to: {freq/1e6:.3f} MHz")
        
    def test_measurement_range_configuration(self):
        """Test measurement range configuration"""
        # Test auto range
        self.power_meter.setAutoRange(True)
        self.assertTrue(self.power_meter.auto_range)
        
        # Test manual range
        self.power_meter.setAutoRange(False)
        self.power_meter.setRange(-30.0)  # -30 dBm range
        
        range_val = self.power_meter.getRange()
        self.assertEqual(range_val, -30.0)
        
        print(f"Measurement range: {range_val} dBm")
        
    def test_averaging_configuration(self):
        """Test averaging configuration"""
        # Test setting averaging
        avg_count = 16
        self.power_meter.setAveraging(avg_count)
        
        # Verify averaging was set
        current_avg = self.power_meter.getAveraging()
        self.assertEqual(current_avg, avg_count)
        
        print(f"Averaging count: {current_avg}")
        
    def test_marker_measurements(self):
        """Test marker-based measurements"""
        # Test CH1 marker power
        ch1_markers = self.power_meter.CH1_Marker_Power()
        self.assertIsInstance(ch1_markers, list)
        self.assertGreater(len(ch1_markers), 0)
        
        # Test CH2 marker power
        ch2_markers = self.power_meter.CH2_Marker_Power()
        self.assertIsInstance(ch2_markers, list)
        self.assertGreater(len(ch2_markers), 0)
        
        print(f"CH1 markers: {len(ch1_markers)} points")
        print(f"CH2 markers: {len(ch2_markers)} points")
        
    def test_calibration_functions(self):
        """Test calibration-related functions"""
        # Test zero calibration
        self.power_meter.zeroCalibration()
        
        # Test reference calibration
        ref_power = -20.0  # -20 dBm reference
        self.power_meter.referenceCalibration(ref_power)
        
        print("Calibration functions executed successfully")
        
    def test_measurement_timing_optimization(self):
        """Test measurement timing for optimization compatibility"""
        print("\n=== Testing Measurement Timing ===")
        
        # Test single measurement timing
        start_time = time.time()
        power = self.power_meter.measurePower()
        single_time = time.time() - start_time
        
        # Test array measurement timing
        start_time = time.time()
        powers = self.power_meter.measurePowerArray(5)
        array_time = time.time() - start_time
        
        print(f"Single measurement time: {single_time:.3f}s")
        print(f"Array measurement time (5 samples): {array_time:.3f}s")
        print(f"Average per sample: {array_time/5:.3f}s")
        
        # Verify measurements are reasonably fast
        self.assertLess(single_time, 1.0, "Single measurement should be fast")
        self.assertLess(array_time, 2.0, "Array measurement should be reasonable")
        
    def test_error_handling(self):
        """Test error handling scenarios"""
        # Test disconnected operations
        self.power_meter.disconnect()
        
        with self.assertRaises(Exception):
            self.power_meter.measurePower()
            
        with self.assertRaises(Exception):
            self.power_meter.setFrequency(1000e6)
            
        # Reconnect for other tests
        self.power_meter.connect("USB0::0x0957::0x0B0B::INSTR")
        
        # Test invalid frequency
        with self.assertRaises(ValueError):
            self.power_meter.setFrequency(-1000)  # Negative frequency
            
        # Test invalid range
        with self.assertRaises(ValueError):
            self.power_meter.setRange(100.0)  # Too high range
            
    def test_reset_functionality(self):
        """Test reset functionality"""
        # Change some settings
        self.power_meter.setFrequency(2000e6)
        self.power_meter.setAveraging(32)
        
        # Reset
        self.power_meter.reset()
        
        # Verify reset to defaults
        freq = self.power_meter.getFrequency()
        avg = self.power_meter.getAveraging()
        
        self.assertEqual(freq, 1000e6)  # Default frequency
        self.assertEqual(avg, 1)        # Default averaging
        
        print("Reset functionality verified")
        
    def test_measurement_accuracy_validation(self):
        """Test measurement accuracy and consistency"""
        print("\n=== Testing Measurement Accuracy ===")
        
        # Take multiple measurements to check consistency
        measurements = []
        for i in range(5):
            power = self.power_meter.measurePower()
            measurements.append(power)
            
        # Calculate statistics
        avg_power = sum(measurements) / len(measurements)
        max_dev = max(abs(p - avg_power) for p in measurements)
        
        print(f"Measurements: {[f'{p:.2f}' for p in measurements]} dBm")
        print(f"Average: {avg_power:.2f} dBm")
        print(f"Max deviation: {max_dev:.2f} dB")
        
        # Verify reasonable consistency (within 1 dB for mock)
        self.assertLess(max_dev, 1.0, "Measurements should be consistent")
        
    def test_optimization_compatibility(self):
        """Test compatibility with system optimizations"""
        print("\n=== Testing Optimization Compatibility ===")
        
        # Test that power meter works well with optimized timing
        # This simulates how it would work with optimized ATC timing
        
        # Rapid measurement sequence (simulating optimized test flow)
        start_time = time.time()
        
        # Simulate measurement sequence from optimized test procedure
        self.power_meter.setFrequency(1030e6)
        self.power_meter.setAveraging(4)
        
        # Take measurements quickly
        powers = []
        for i in range(3):
            power = self.power_meter.measurePower()
            powers.append(power)
            
        total_time = time.time() - start_time
        
        print(f"Rapid measurement sequence: {total_time:.3f}s")
        print(f"Powers measured: {[f'{p:.2f}' for p in powers]} dBm")
        
        # Verify all measurements are valid
        for power in powers:
            self.assertIsInstance(power, float)
            self.assertGreater(power, -100.0)
            
        print("✓ Power meter compatible with optimized timing")


class TestB4500CPerformance(unittest.TestCase):
    """Performance tests for B4500C Power Meter"""
    
    def setUp(self):
        """Set up performance test fixtures"""
        self.mock_rm = Mock()
        self.power_meter = MockPowerMeter(self.mock_rm)
        self.power_meter.connect("USB0::0x0957::0x0B0B::INSTR")
        
    def test_measurement_throughput(self):
        """Test measurement throughput performance"""
        print("\n=== Power Meter Throughput Test ===")
        
        # Test single measurement throughput
        num_measurements = 20
        start_time = time.time()
        
        for i in range(num_measurements):
            power = self.power_meter.measurePower()
            
        single_throughput_time = time.time() - start_time
        
        # Test array measurement throughput
        start_time = time.time()
        powers = self.power_meter.measurePowerArray(num_measurements)
        array_throughput_time = time.time() - start_time
        
        print(f"Single measurements ({num_measurements}): {single_throughput_time:.3f}s")
        print(f"Array measurement ({num_measurements}): {array_throughput_time:.3f}s")
        print(f"Single rate: {num_measurements/single_throughput_time:.1f} meas/s")
        print(f"Array rate: {num_measurements/array_throughput_time:.1f} meas/s")
        
        # Array should be more efficient
        self.assertLess(array_throughput_time, single_throughput_time)
        
    def test_configuration_speed(self):
        """Test configuration change speed"""
        print("\n=== Configuration Speed Test ===")
        
        frequencies = [1000e6, 1030e6, 1090e6, 978e6]
        
        start_time = time.time()
        for freq in frequencies:
            self.power_meter.setFrequency(freq)
            
        config_time = time.time() - start_time
        
        print(f"Frequency configuration time: {config_time:.3f}s for {len(frequencies)} changes")
        print(f"Average per change: {config_time/len(frequencies):.3f}s")
        
        # Should be reasonably fast
        self.assertLess(config_time/len(frequencies), 0.1)


if __name__ == '__main__':
    unittest.main(verbosity=2)
