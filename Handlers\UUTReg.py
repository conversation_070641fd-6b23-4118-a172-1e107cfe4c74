''' Perform low-level read and write register operations on RF Board '''

import json, time, os, glob
from datetime import date, datetime

class UUTReg:
    def __init__(self, ate_rm):
        self.log = ate_rm

    ''' Read individual RF FPGA register '''
    def readRFRegister(self, requestId, address):
        today = date.today()
        this_year = str(today)[:4]
        this_month = str(today)[5:7]
        this_day = str(today)[8:10]
        current_directory = os.getcwd()
        os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\request")
        data = {
            "peek_poke_request": {
                "requestId": requestId,
                "peekPokeOperation": "peek",
                "peekPokeAddress": str(hex(address)),
                "peekPokeValue": "0x0"
                }
        }
        filename = "peek_tst_msg_" + str(requestId) + ".json"
        for removable_file in glob.glob("*.json_processed"):
            os.remove(removable_file)
        with open(filename, "w") as write_file:
            json.dump(data, write_file)
        os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\response\\peek_poke_response")
        return_values = []
        output_file = (glob.glob("peek_poke_response_" + str(requestId) + "_" + this_year + "_" + this_month + "_" + this_day +  "_*.json"))
        count_limit = 50 # 5 second timer
        while((len(output_file) == 0) and (count_limit != 0)):
            output_file = (glob.glob("peek_poke_response_" + str(requestId) + "_" + this_year + "_" + this_month + "_" + this_day +  "_*.json"))
            time.sleep(0.1)
            count_limit -= 1
        with open(max(output_file), "r") as read_file:
            json_response = json.load(read_file)
            return_values.append(json_response["peek_poke_response"]["peekPokeValue"])
        os.remove(max(output_file))
        os.chdir(current_directory)
        
        if json_response["peek_poke_response"]["status"] == 0:
            self.log.logMessage(1, "Received " + str(return_values[0]) + " from address: " + str(address))
            return return_values[0]
        else:
            self.log.logMessage(3, "Error code " + str(json_response["peek_poke_response"]["status"]) + " reading from address: " + str(address))
            return json_response["peek_poke_response"]["status"]

    ''' Write a value to an individual RF FPGA Register '''
    def writeRFRegister(self, requestId, address, value):
        today = date.today()
        this_year = str(today)[:4]
        this_month = str(today)[5:7]
        this_day = str(today)[8:10]
        current_directory = os.getcwd()
        os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\request")
        data = {
            "peek_poke_request": {
                "requestId": requestId,
                "peekPokeOperation": "poke",
                "peekPokeAddress": str(hex(address)),
                "peekPokeValue": str(hex(value))
                }
        }
        for removable_file in glob.glob("*.json_processed"):
            os.remove(removable_file)
        filename = "poke_tst_msg_" + str(requestId) + ".json"
        with open(filename, "w") as write_file:
            json.dump(data, write_file)
        os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\response\\peek_poke_response")
        return_values = []
        output_file = (glob.glob("peek_poke_response_" + str(requestId) + "_" + this_year + "_" + this_month + "_" + this_day +  "_*.json"))
        count_limit = 50 # 5 second timer
        while((len(output_file) == 0) and (count_limit != 0)):
            output_file = (glob.glob("peek_poke_response_" + str(requestId) + "_" + this_year + "_" + this_month + "_" + this_day +  "_*.json"))
            time.sleep(0.1)
            count_limit -= 1
        with open(max(output_file), "r") as read_file:
            json_response = json.load(read_file)
            return_values.append(json_response["peek_poke_response"]["status"])
        os.remove(max(output_file))
        os.chdir(current_directory)
        self.log.logMessage(1, "Sent " + str(data) + " to address: " + str(address))
        return return_values[0]