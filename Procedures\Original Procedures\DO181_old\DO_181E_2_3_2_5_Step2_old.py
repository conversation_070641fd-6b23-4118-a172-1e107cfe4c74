# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Pulse Decoder Characterics, Section *******
                         
             Step2: Pulse Level Tolerance, ATCRBS ModeA Only All-Call.
             Vary the level of the P4 pulse between -10 and 0 dB,
             in 1 dB steps with respecto to P3.  Verify the changeover
             to no replies as specifed in *******.2
             
             
             
INPUTS:      RM, ATC, PathLoss
OUTPUTS:     ChangeOver_Pwr - Change Over P4 Power Level,

HISTORY:

04/23/2020   MRS    Initial Release.
05/11/2020   MRS    Cleanup
03/03/2021   MRS    Updates for new Handlers and Lobster.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_5_Step2(rm, atc,PathLoss):
    
    rm.logMessage(2,"*** DO-181E, Pulse Decoder Characterics: Sect *******_Step2 ***")
    
    
    #Results read by TestStand
    ChangeOver_Pwr = 0.0                    # Change Over P4 Power Level,
    
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE A Only -All Call
    atc.transponderModeA_Only()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     
    #Set Up 8usec P4
    atc.gwrite(":ATC:XPDR:PUL:P4WIDTH 0.8")
       
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    atc.waitforstatus()
    

    P4_Power = -10
    for i in range(10):
        #set the P4 power level
        cmd = ":ATC:XPDR:PUL:P4POWER " + str(P4_Power)
        print("CMD: ",cmd)
        atc.gwrite(cmd)
        time.sleep(5)
        atc.waitforstatus()
        
        replyrate = atc.getPercentReply(2)        
        # fix for erroneous reply rate
        count = 0
        while replyrate[1] == -1.0 and count < 10:
            replyrate = atc.getPercentReply(2)
            count = count + 1
        
        
        msg = "Pwr: " + str(P4_Power) + " Reply Rate: " + str(replyrate)
        rm.logMessage(0,"Test_2_3_2_5_Step2" + msg)    

        #check for ModeA to ModeS changeover
        if (replyrate[1] < 10.0):              #ModeA replies < 10%
            break
                    
        P4_Power = P4_Power + 1
     
  
    #Return Last Power Level and % Replies    
    ChangeOver_Pwr = P4_Power
    print("Results: ",ChangeOver_Pwr,str(replyrate))    

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    rm.logMessage(0,"Test_2_3_2_5_Step2 - Done")    

    
    rm.logMessage(2,"Done, closing session")
    
    return ChangeOver_Pwr

##########################################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

     
    res = Test_2_3_2_5_Step2(rm,atc_obj,12.0)
    
    atc_obj.close()

