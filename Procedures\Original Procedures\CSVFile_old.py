import csv
from TXDLib.Procedures import selection_table
import os

class create_csv():
    
    def __init__(self):
        self.final_tuple = []

    def tuple_add(self, given_array):

        if len(self.final_tuple) == 0:
            self.final_tuple = zip(given_array)
            self.final_tuple = list(self.final_tuple)
        else:
            for i in range(len(given_array)):
                self.final_tuple[i] = self.final_tuple[i] + (given_array[i],)

    def print_out(self, value, filename=None):

        if filename==None:
            os.chdir(os.getcwd() + '\\Data')

            self.mkdir(value)
            self.chdir(value)
            filedir = os.getcwd()

            csv_filename = str(selection_table.description[int(value)]) + '_'
            csv_filename_number = self.auto_increment(os.getcwd(),csv_filename)
            csv_filename = csv_filename + csv_filename_number + '.csv'
        
            with open(csv_filename, 'w', newline = '') as csvfile:
                    writer = csv.writer(csvfile,delimiter = ',')
                    writer.writerows(self.final_tuple)
            csvfile.close()
            
            os.chdir("..")
            os.chdir("..")
            return filedir + '\\' + csv_filename
        else:
            with open(filename, 'w', newline = '') as csvfile:
                    writer = csv.writer(csvfile,delimiter = ',')
                    writer.writerows(self.final_tuple)
            csvfile.close()

            return filename

    """

        auto_increment: to auto_increment output filnames
        checks inside output directory and increments output files
        with the same name
        
    """
    def auto_increment(self,output_dir,output_file):
        current_level = -1
        for i in os.listdir(output_dir):
            if os.path.isfile(os.path.join(output_dir,i)):
                if os.path.splitext(i)[1] == '.csv':
                    if i[:-8] == output_file:
                        filenumber = i[-8:-4]
                        try:
                            if int(filenumber) > current_level:
                                current_level = int(filenumber)
                        except ValueError:
                            print('not an integer')
                            pass

        return str(current_level + 1).zfill(4)

    def mkdir(self,value):
        path = os.getcwd()
        try:
            os.mkdir(path + '/' + str(selection_table.description[int(value)]))
        except FileExistsError:
            pass

    def chdir(self,value):
        path = os.getcwd()
        os.chdir(path + '/' + str(selection_table.description[int(value)]))
