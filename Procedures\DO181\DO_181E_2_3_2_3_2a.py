# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Reply Pulse Characteristic, Section *******.2
             
             Step1: Mode S Preamble. Interrogate the transponder with a standard
			 Mode A/Mode S All-Call.  Display the Mode S reply on an Oscilloscope.
			 Measure the pulse duration of the first four reply pulses.  Measure
			 the pulse spacing between the leading edge of the first and each of the 
			 second, third, and fourth pulses.
             
             Step2: Mode S Reply Data Pulses: Measure the pulse spacing of
			 the fifth reply pulse with reference to the first reply pulse.
             
             Step3: Mode S Amplitude Variation:  Measure the maximm power
			 differential between pulses in the Mode S reply.
             
             Step4: Mode S Reply Pulse Shape: measure the rise time of the reply 
			 pulses.
             
             Step5: Mode S Reply Pulse Spacing Tolerance: Determine that the leading edge of any
			 reply pulse is within 50 nanoseconds of its assigned position.
              
             
INPUTS:      RM, ATC, Scope, PwrMeter, PathLoss
OUTPUTS:     'PulseDuration' -- array of pulse durations in ModeS Preamble
             'PulseSpacing'  -- array of pulse spacing in ModeS Preamble
             'PulseSpacing5' -- pulse spacing between 1st and 5th Pulse
             'PowerDifference -- max power deviation in reply pulses
             'RiseTime'  -- average Rise/Fall times of reply pulses
             'FallTime'  -- average Fall times of reply pulses
             'Tolerance' -- average Pulse Position tolerance

HISTORY:

04/13/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review     
08/06/2020   AKS    Added comments per review 
03/01/2021   MRS    Updates for New Handlers  
10/2023      CS     Updated to use ATC instead of Scope for Pulse Characteristics. Don't currently have a reliable trigger
                    mechanism when using TXD flight s/w.                         
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import B4500CPwrMeter
from TXDLib.Handlers.D3054Scope import D3054Scope


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def modeX_Setup(scope_obj,timescale,threshold,trigger):
    """ Basic Scope setup for Mode S Pulse. 
   Chan 2: Trigger, Chan3: Data ... RFBOB must be set up apriori."""
    
    scope_obj.Reset()
    #Display Chan 2 and 3 (only use Chan 2,3 for this test)
    scope_obj.chanDisplay(1,0)
    scope_obj.chanDisplay(2,1)
    scope_obj.chanDisplay(3,1)
    scope_obj.chanDisplay(4,0)
    #Set the Scale
    scope_obj.voltDiv(1, 100, "mV")
    scope_obj.voltDiv(2, 100, "mV")
    scope_obj.voltDiv(3, 20, "mV")
    scope_obj.voltDiv(4, 20, "mV")
    #Digitize all chanels
    #scope_obj.Digitize()
    #Invert Chan 3 and 4
    scope_obj.invertChan(3,1)
    scope_obj.invertChan(4,1)
    #Set Impdance 3 and 4 to 50 Ohms
    scope_obj.setChanImpdance(3,5)
    scope_obj.setChanImpdance(4,5)
    #Set TimeScale and Trigger Level
    scope_obj.timeScale(timescale, "us")
    scope_obj.trigSource(trigger)
    scope_obj.trigType("EDGE")
    scope_obj.setEdgeTrigger(trigger, threshold, "mV")   #chan 2 is trigger
    scope_obj.trigRun()

def measurePulseChar(scope_obj, edgePPosition, source = 1):
    """Function that measures PWidth, Rise, and fall for a given Pulse rising edge position.
    Fall time is taken by moving the cursor edgePPosition + PWidth. """

    #Measure Chan 3
    scope_obj.setMeasureSource(source)
    result = []

    scope_obj.setTimePosition(edgePPosition)
    result.append(scope_obj.measPWidth())
    scope_obj.timeScale(.5, "us")
    time.sleep(.1)
    result.append(scope_obj.measRiseTime())
    scope_obj.setTimePosition(edgePPosition + result[0])
    time.sleep(.1)
    result.append(scope_obj.measFallTime())
    time.sleep(.1)

    return result  

def trigDetect(scope_obj):
    """ Function that triggers a given waveform and returns all detected edge positions based
    on trigger position. """

    #Trigger Source Chan is set in setup (modex_setup)
    if scope_obj.trigSingle(2):
        return 1
    return 0


def pw_init(pw,mode):
    """ Initializes Power Meter to pulse mode, sets timebase, sets trigger""" 
    
    ### PUT METER IN PULSE MODE
    pw.autoset()         #Initialize to defaults
    time.sleep(5)
    #Mode and Input Channel
    pw.setCalculateMode('PULSE')
    pw.setCalculateUnits('dBm')
    pw.setCalculate1_on('ON')
    
    #Trigger
    pw.basicWrite("TRIGger:POSition LEFT")
    pw.basicWrite("TRIGger:SOURce CH1")   
    pw.basicWrite("TRIGger:SLOPe POS")
    pw.basicWrite("TRIGger:MODE NORMAL")  #Importantae!
    pw.basicWrite("TRIGger:LEV 50.0")    #Importantae!
    pw.basicWrite("TRIGger:HOLDoff 30e-6")
    pw.basicWrite("DISPlay:TRACe1:VSCALE 10.00")
    pw.basicWrite("DISPlay:TRACe1:VCENTer 21.71")
    pw.basicWrite("SENSe1:AVERage 1")
    #pw.basicWrite("SENSe2:AVERage 1")
    #pw.basicWrite("SENSe3:AVERage 1")
    #pw.basicWrite("SENSe4:AVERage 1")
    pw.basicWrite("INITiate:CONTinuous OFF")
        
    #TimeBase
    pw.basicWrite("DISPlay:MODE GRAPH")
    if (mode == 'ModeA'):
        TimeBase = '1e-6'                 #Importantae!
        pw.setTimeBase(TimeBase)
    else:
        TimeBase = '10e-6'                 #Importantae!
        pw.setTimeBase(TimeBase)
       
    print("TimeBase-Pulse: ",pw.getTimeBase())
    print("TSPAN: ",pw.basicQuery("DISPlay:TSPAN?"))
    pw.setFrequency1('1.090e9')
      

def find_pulses(pw,title="PowerMeter (dBm vs time)"):
    """ using Power Meter, finds peaks, gets stats for each pulse, and plots. Returns string with
    pulse parameters and integer with number of pulses. """
    pw.basicWrite("ABORt")
    time.sleep(0.3)
    pw.basicWrite("INITiate:CONTinuous OFF")
    time.sleep(1)
    pw.basicWrite("INITiate:IMMediate")
    time.sleep(2)
    pw.basicWrite("INITiate:CONTinuous OFF")
    time.sleep(0.3)
    nPulse = pw.findpeaks('50')      #actual level for max in dBm
    pwr = ""
    for i in range(nPulse):
        print("\n\nPULSE : ",i)
        pw.setpulsepositions(i)        # set markers for pulse i
        time.sleep(1)
        pwr = pw.getpwrmeasuremet()
    #plots the results
    #pw.plotpeaks('-20',title)            #comment this statment when running from TestStand
    return pwr, nPulse
    


##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_3_2a(rm,atc,scope,pwrmtr,PathLoss):
    """ DO-181E, Reply Pulse Characteristics: Sect *******_2a """
    
    rm.logMessage(2,"*** DO-181E, Reply Pulse Characteristics: Sect *******_2a ***\r\n")
    
  

    #Results read by TestStand
    PulseDuration = [0.0,0.0,0.0,0.0]    #array of pulse durations in ModeS Preamble
    PulseSpacing = [2.0,0.0,0.0,0.0]     #array of pulse spacing in ModeS Preamble
    PulseSpacing5 = 0.0                  #pulse spacing between 1st and 5th Pulse
    PowerDifference = 0.0                #max power deviation in reply pulses
    RiseTime = 0.0                       #average Rise times of reply pulses
    FallTime = 0.0                       #average Fall times of reply pulses
    Tolerance = 0.0                      #average Pulse Position tolerance
    #PulseWidth1 = 0.0                       #Pulse Width for P1
    #PulseWidth2 = 0.0                       #Pulse Width for P2
    #PulseWidth3 = 0.0                       #Pulse Width for P3
    #PulseWidth4 = 0.0                       #Pulse Width for P4
    sf = 1.0e-3                             #Scale factor (convert nsec to usec)

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE S
    atc.transponderModeS()
    time.sleep(0.3)
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     
    time.sleep(0.3)
    atc.gwrite(":ATC:XPDR:PRF 1")  #Set Power Deviation for Bot Antenna 
    time.sleep(0.3)
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF   
    time.sleep(10)

    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    
    #Steps 1,2 and 4, Preamble and Rise/Fall Time Avg
    rm.logMessage(0,"*Test_2_3_2_3_2a_Step1_Step2_Step4 - Start")  
    ''' 
    #Set Up the OScope for Pulse Measurements    
    tmescale =  50.0            #scope time scale (usec/div)
    threshold = 50.0           #threshold (mV)
    trigger = 2
    modeX_Setup(scope,tmescale,threshold,trigger)
    time.sleep(5)

    #Scale Factor for Scope Measurements
    sf = 1.0e6      #usec
    if trigDetect(scope) == 1:
        # Threshold is in Volts, essentially a y-scale line that if a rising edge passes it is then registered. IE 20mV
        PEdges,NEdges = scope.digiEdgePos(10/1000.0,source = 3)
        
       
        #Number of Positive/Negative Edges
        print("PEdges: ",len(PEdges),PEdges)     
        print("NEdges: ",len(NEdges),NEdges)
        if len(PEdges) == 0:
            rm.logMessage(3,"Test_2_3_2_3_2a - Error, No Edges Detected")
        else:
            #Loop thru Positive Edges and gather pulse data
            for edge_pos in PEdges:
                #print ("EdgePos: ",edge_pos)
                pulse_char = measurePulseChar(scope, edge_pos,source = 3)
                rm.logMessage(0,"Pulse duration: " + str(pulse_char[0]))
                rm.logMessage(0,"Pulse rise: " + str(pulse_char[1]))
                rm.logMessage(0,"Pulse fall: " + str(pulse_char[2]))
                RiseTime = RiseTime + pulse_char[1]                      #average Rise times of reply pulses
                FallTime = FallTime + pulse_char[2]                      #average Fall times of reply pulses
                
            #Comput Rise/Fall Averages
            RiseTime = (RiseTime/len(PEdges)) * sf
            FallTime = (FallTime/len(PEdges)) * sf
            
            # Uncomment for module built in plot
            scope.timeScale(tmescale,"us")
            scope.setTimePosition(PEdges[0])
            #scope.plotWave()
            

            #Evaluate Preamble Pulses for Results
            PulseDuration[0] = (NEdges[0] - PEdges[0]) * sf - 0.3    #0.3 is to the 6dB power level
            PulseDuration[1] = (NEdges[1] - PEdges[1]) * sf - 0.3
            PulseDuration[2] = (NEdges[2] - PEdges[2]) * sf - 0.3
            PulseDuration[3] = (NEdges[3] - PEdges[3]) * sf - 0.3
            PulseSpacing[0] = (PEdges[1] - PEdges[0]) * sf
            PulseSpacing[1] = (PEdges[2] - PEdges[0]) * sf
            PulseSpacing[2] = (PEdges[3] - PEdges[0]) * sf
            PulseSpacing[3] = (PEdges[4] - PEdges[0]) * sf
            PulseSpacing5 = (PEdges[4] - PEdges[0])    * sf                #pulse spacing between 1st and 5th Pulse
            span =( NEdges[len(PEdges)-1]- PEdges[0]) * sf
            print("*** MESSAGE SPAN ***",span )
            span =( PEdges[4] - PEdges[0]) * sf
            print("*** PreAmble Span ***",span)
            span = (NEdges[len(PEdges)-1] - PEdges[4]) * sf
            print("*** Data Span ***",span)
    '''
#Get P1 PulseWidth from ATC
    time.sleep(2)
    PulseDuration[0] = atc.getPulseWidth(2)
    time.sleep(1)
    rm.logMessage(0,("PulseWidth1: " + str(PulseDuration[0])))
        
    # fix for erroneous response
    count = 0
    while (PulseDuration[0] == 0.0 or PulseDuration[0] == 20) and count < 10:
        time.sleep(1)
        PulseDuration[0] = atc.getPulseWidth(2)
        count = count + 1

    PulseDuration[0] = (PulseDuration[0] * sf)
    rm.logMessage(0,("PulseWidth1 Final: " + str(PulseDuration[0])))
    
#Get Rise Time of P1
    time.sleep(1)
    atc.waitforstatus()  
    time.sleep(2)
    RiseTime = atc.getRiseTime(2)
    time.sleep(1)
    rm.logMessage(0,("RiseTime: " + str(RiseTime)))
        
    # fix for erroneous response
    count = 0
    while (RiseTime == 0.0 or RiseTime == 20) and count < 10:
        time.sleep(1)
        RiseTime = atc.getRiseTime(2)
        count = count + 1

    RiseTime = (RiseTime * sf)
    rm.logMessage(0,("RiseTime Final: " + str(RiseTime)))
    
#Get Fall Time of P1
    time.sleep(1)
    atc.waitforstatus()  
    time.sleep(2)
    FallTime = atc.getFallTime(2)
    time.sleep(1)
    rm.logMessage(0,("FallTime: " + str(FallTime)))
        
    # fix for erroneous response
    count = 0
    while (FallTime == 0.0 or FallTime == 20) and count < 10:
        time.sleep(1)
        FallTime = atc.getFallTime(2)
        count = count + 1

    FallTime = (FallTime * sf)
    rm.logMessage(0,("FallTime Final: " + str(FallTime)))

#Get Spacing for P1
    time.sleep(0.3)
    atc.waitforstatus()  
    time.sleep(2)
    PulseSpacing[0] = atc.getPulsePosition(2)
    time.sleep(1)
    rm.logMessage(0,("Pulse Position for P1: " + str(PulseSpacing[0])))
        
    # fix for erroneous response
    count = 0
    while (PulseSpacing[0] == 20 or PulseSpacing[0] == 2) and count < 10:
        time.sleep(1)
        PulseSpacing[0] = atc.getPulsePosition(2)
        count = count + 1
    
    time.sleep(0.3)
    PulseSpacing[0] = (PulseSpacing[0] * sf)
    rm.logMessage(0,("Pulse Position for P1 Final: " + str(PulseSpacing[0])))

#Get P2 PulseWidth from ATC
    time.sleep(0.3)
    atc.waitforstatus()  
    time.sleep(2)
    atc.gwrite(":ATC:MEA:SET:PUL 15")      #Measure P2 Mode S Reply
    time.sleep(2)

    PulseDuration[1] = atc.getPulseWidth(2)
    time.sleep(1)
    rm.logMessage(0,("PulseWidth2: " + str(PulseDuration[1])))
        
    # fix for erroneous response
    count = 0
    while (PulseDuration[1] == 0.0 or PulseDuration[1] == 20) and count < 10:
        time.sleep(1)
        PulseDuration[1] = atc.getPulseWidth(2)
        count = count + 1

    PulseDuration[1] = (PulseDuration[1] * sf)
    rm.logMessage(0,("PulseWidth2 Final: " + str(PulseDuration[1])))

#Get Spacing for P2
    time.sleep(0.3)
    atc.waitforstatus()  
    time.sleep(2)
    PulseSpacing[1] = atc.getPulsePosition(2)
    time.sleep(1)
    rm.logMessage(0,("Pulse Position for P2: " + str(PulseSpacing[1])))
        
    # fix for erroneous response
    count = 0
    while (PulseSpacing[1] == 0.0 or PulseSpacing[1] == 20) and count < 10:
        time.sleep(1)
        PulseSpacing[1] = atc.getPulsePosition(2)
        count = count + 1
    
    time.sleep(0.3)
    PulseSpacing[1] = (PulseSpacing[1] * sf)
    rm.logMessage(0,("Pulse Position for P2 Final: " + str(PulseSpacing[1])))

#Get P3 PulseWidth from ATC
    time.sleep(0.3)
    atc.waitforstatus()  
    time.sleep(2)
    atc.gwrite(":ATC:MEA:SET:PUL 16")      #Measure P3 Mode S Reply
    time.sleep(2)

    PulseDuration[2] = atc.getPulseWidth(2)
    time.sleep(1)
    rm.logMessage(0,("PulseWidth3: " + str(PulseDuration[2])))
        
    # fix for erroneous response
    count = 0
    while (PulseDuration[2] == 0.0 or PulseDuration[2] == 20) and count < 10:
        time.sleep(1)
        PulseDuration[2] = atc.getPulseWidth(2)
        count = count + 1

    PulseDuration[2] = (PulseDuration[2] * sf)
    rm.logMessage(0,("PulseWidth3 Final: " + str(PulseDuration[2])))

#Get Spacing for P3
    time.sleep(0.3)
    atc.waitforstatus()  
    time.sleep(2)
    PulseSpacing[2] = atc.getPulsePosition(2)
    time.sleep(1)
    rm.logMessage(0,("Pulse Position for P3: " + str(PulseSpacing[2])))
        
    # fix for erroneous response
    count = 0
    while (PulseSpacing[2] == 0.0 or PulseSpacing[2] == 20) and count < 10:
        time.sleep(1)
        PulseSpacing[2] = atc.getPulsePosition(2)
        count = count + 1
    
    time.sleep(1)
    PulseSpacing[2] = (PulseSpacing[2] * sf)
    rm.logMessage(0,("Pulse Position for P3 Final: " + str(PulseSpacing[2])))

#Get P4 PulseWidth from ATC
    time.sleep(1)
    atc.waitforstatus()  
    time.sleep(2)
    atc.gwrite(":ATC:MEA:SET:PUL 17")      #Measure P4 Mode S Reply
    time.sleep(2)

    PulseDuration[3] = atc.getPulseWidth(2)
    time.sleep(1)
    rm.logMessage(0,("PulseWidth4: " + str(PulseDuration[3])))
        
    # fix for erroneous response
    count = 0
    while (PulseDuration[3] == 0.0 or PulseDuration[3] == 20) and count < 10:
        time.sleep(1)
        PulseDuration[3] = atc.getPulseWidth(2)
        count = count + 1

    PulseDuration[3] = (PulseDuration[3] * sf)
    rm.logMessage(0,("PulseWidth4 Final: " + str(PulseDuration[3])))

#Get Spacing for P4
    time.sleep(0.3)
    atc.waitforstatus()  
    time.sleep(2)
    PulseSpacing[3] = atc.getPulsePosition(2)
    time.sleep(1)
    rm.logMessage(0,("Pulse Position for P4: " + str(PulseSpacing[3])))
        
    # fix for erroneous response
    count = 0
    while (PulseSpacing[3] == 0.0 or PulseSpacing[3] == 20) and count < 10:
        time.sleep(1)
        PulseSpacing[3] = atc.getPulsePosition(2)
        count = count + 1
    
    time.sleep(0.3)
    PulseSpacing[3] = (PulseSpacing[3] * sf)
    rm.logMessage(0,("Pulse Position for P4 Final: " + str(PulseSpacing[3])))
    time.sleep(1)

#Get Preamble Spacing from ATC
    time.sleep(1)
    atc.waitforstatus()  
    time.sleep(2)
    atc.gwrite(":ATC:MEA:SET:PUL 30")      #Measure Mode S Preamble Reply
    time.sleep(.03)
    atc.gwrite(":ATC:MEA:DFORMAT 2")       #Measurement Mode to Float
    time.sleep(5)
    #atc.gwrite(":ATC:MEA:SET:DFBIT?")
    #Turn off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    time.sleep(10)
    PulseSpacing5 = atc.getPulsePosition(2)
    time.sleep(1)
    rm.logMessage(0,("Pulse Position for P5: " + str(PulseSpacing5)))
            
    # fix for erroneous response
    count = 0
    while (PulseSpacing5 == 0.0 or PulseSpacing5 == 20) and count < 10:
        time.sleep(1)
        PulseSpacing5 = atc.getPulsePosition(2)
        count = count + 1
    
    time.sleep(0.3)
    PulseSpacing5 = (PulseSpacing5 * sf)
    rm.logMessage(0,("Pulse Position for P5 Final: " + str(PulseSpacing5)))
    time.sleep(1)

    #Turn RF back on
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(5)

#Step 3 Power Variation
    rm.logMessage(0,"*Test_2_3_2_3_2a_Step3 - Start")  
    #Init Power Meter
    pw_init(pwrmtr,'ModeS')
    time.sleep(2)
    #Get Number of pulses
    p_str, npulse_str = find_pulses(pwrmtr)  
    print("\n*NumPULSEs: ",npulse_str)
    nPulse = int(npulse_str)
    rm.logMessage(0,("NumPULSEs: " + str(nPulse)))
    #rm.logMessage(0,("NumPULSEs: " + str(nPulse_str)))
    pwr = 0.0
    pmax = -40.0
    pmin = 10.0
    for i in range(nPulse):
        print("\n\nPULSE : ",i)
        pwrmtr.setpulsepositions(i)        # set markers for pulse i
        time.sleep(1)
        pwr_str = pwrmtr.getpwrmeasuremet()
        pwr_s = pwr_str.split(',')
        pwr = float(pwr_s[3])              #max power
        if pmax == -40: 
            pmax = pwr
            pmin = pwr
        if pwr > pmax: pmax = pwr
        if pwr < pmin: pmin = pwr
        
        
    rm.logMessage(0,("Final Power Max: %f" % pmax))
    rm.logMessage(0,("Final Power Min: %f" % pmin))
    PowerDifference = pmax - pmin       #min/max difference
    rm.logMessage(0,("PowerDiff: %f" % PowerDifference))
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    rm.logMessage(0,"Test_2_3_2_3_2a - Done")    
    
    rm.logMessage(2,"Done, closing session")
    
    #concatonate lists    
    return PulseDuration + PulseSpacing + [PulseSpacing5] + [PowerDifference] + [RiseTime] + [FallTime] + [Tolerance]

##############################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

     #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

    pwr_obj = B4500CPwrMeter(rm)
    pwr_obj.Reset()

    scope_obj = D3054Scope(rm)
    scope_obj.Reset()

   
    res = Test_2_3_2_3_2a(rm,atc_obj,scope_obj,pwr_obj,12.0)
    
    scope_obj.close()
    atc_obj.close()
    pwr_obj.close()

