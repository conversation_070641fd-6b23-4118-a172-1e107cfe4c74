#!/usr/bin/env python3
"""
Unit Tests for N9010B Spectrum Analyzer Handler
Tests both original functionality and optimization compatibility
"""

import unittest
import time
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from tests.mocks.mock_spectrum_analyzer import MockSpectrumAnalyzer
from unittest.mock import Mock, patch


class TestN9010BSpectrumAnalyzer(unittest.TestCase):
    """Test cases for N9010B Spectrum Analyzer handler"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_rm = Mock()
        self.spec_an = MockSpectrumAnalyzer(self.mock_rm)
        self.spec_an.connect("TCPIP::*************::INSTR")
        
    def tearDown(self):
        """Clean up after tests"""
        if self.spec_an.connected:
            self.spec_an.disconnect()
            
    def test_connection(self):
        """Test spectrum analyzer connection functionality"""
        # Test connection status
        self.assertTrue(self.spec_an.connected)
        
        # Test disconnection
        self.spec_an.disconnect()
        self.assertFalse(self.spec_an.connected)
        
        # Test reconnection
        result = self.spec_an.connect("TCPIP::*************::INSTR")
        self.assertTrue(result)
        self.assertTrue(self.spec_an.connected)
        
    def test_basic_commands(self):
        """Test basic SCPI command functionality"""
        # Test write command
        self.spec_an.write("*RST")
        self.assertEqual(self.spec_an.last_command, "*RST")
        
        # Test query command
        idn = self.spec_an.query("*IDN?")
        self.assertIn("N9010B", idn)
        
    def test_frequency_configuration(self):
        """Test frequency configuration"""
        print("\n=== Testing Frequency Configuration ===")
        
        # Test center frequency
        center_freq = 1030e6  # 1030 MHz
        self.spec_an.setCenterFrequency(center_freq)
        freq = self.spec_an.getCenterFrequency()
        self.assertAlmostEqual(freq, center_freq, delta=1000)
        
        # Test frequency span
        span = 10e6  # 10 MHz span
        self.spec_an.setSpan(span)
        current_span = self.spec_an.getSpan()
        self.assertAlmostEqual(current_span, span, delta=1000)
        
        # Test start/stop frequencies
        start_freq = 1000e6
        stop_freq = 1100e6
        self.spec_an.setStartFrequency(start_freq)
        self.spec_an.setStopFrequency(stop_freq)
        
        print(f"Center frequency: {freq/1e6:.3f} MHz")
        print(f"Span: {current_span/1e6:.3f} MHz")
        print(f"Start: {start_freq/1e6:.3f} MHz")
        print(f"Stop: {stop_freq/1e6:.3f} MHz")
        
    def test_amplitude_configuration(self):
        """Test amplitude configuration"""
        print("\n=== Testing Amplitude Configuration ===")
        
        # Test reference level
        ref_level = -20.0  # -20 dBm
        self.spec_an.setReferenceLevel(ref_level)
        level = self.spec_an.getReferenceLevel()
        self.assertEqual(level, ref_level)
        
        # Test attenuation
        attenuation = 10.0  # 10 dB
        self.spec_an.setAttenuation(attenuation)
        atten = self.spec_an.getAttenuation()
        self.assertEqual(atten, attenuation)
        
        print(f"Reference level: {level} dBm")
        print(f"Attenuation: {atten} dB")
        
    def test_measurement_configuration(self):
        """Test measurement configuration"""
        # Test resolution bandwidth
        rbw = 1e3  # 1 kHz
        self.spec_an.setResolutionBandwidth(rbw)
        current_rbw = self.spec_an.getResolutionBandwidth()
        self.assertAlmostEqual(current_rbw, rbw, delta=100)
        
        # Test video bandwidth
        vbw = 3e3  # 3 kHz
        self.spec_an.setVideoBandwidth(vbw)
        current_vbw = self.spec_an.getVideoBandwidth()
        self.assertAlmostEqual(current_vbw, vbw, delta=100)
        
        # Test sweep time
        sweep_time = 0.1  # 100 ms
        self.spec_an.setSweepTime(sweep_time)
        current_sweep = self.spec_an.getSweepTime()
        self.assertAlmostEqual(current_sweep, sweep_time, delta=0.01)
        
        print(f"RBW: {current_rbw/1e3:.1f} kHz")
        print(f"VBW: {current_vbw/1e3:.1f} kHz")
        print(f"Sweep time: {current_sweep*1000:.1f} ms")
        
    def test_trace_measurements(self):
        """Test trace measurement functions"""
        print("\n=== Testing Trace Measurements ===")
        
        # Configure for measurement
        self.spec_an.setCenterFrequency(1030e6)
        self.spec_an.setSpan(10e6)
        self.spec_an.setReferenceLevel(-20.0)
        
        # Test trace data acquisition
        trace_data = self.spec_an.getTraceData()
        self.assertIsInstance(trace_data, list)
        self.assertGreater(len(trace_data), 0)
        
        # Test frequency array
        freq_array = self.spec_an.getFrequencyArray()
        self.assertIsInstance(freq_array, list)
        self.assertEqual(len(freq_array), len(trace_data))
        
        print(f"Trace points: {len(trace_data)}")
        print(f"Frequency range: {freq_array[0]/1e6:.1f} - {freq_array[-1]/1e6:.1f} MHz")
        print(f"Amplitude range: {min(trace_data):.1f} - {max(trace_data):.1f} dBm")
        
    def test_marker_measurements(self):
        """Test marker measurement functions"""
        print("\n=== Testing Marker Measurements ===")
        
        # Set up measurement
        self.spec_an.setCenterFrequency(1030e6)
        self.spec_an.setSpan(10e6)
        
        # Test marker operations
        marker_freq = 1030e6
        self.spec_an.setMarker(1, marker_freq)
        
        # Test marker amplitude
        marker_amp = self.spec_an.getMarkerAmplitude(1)
        self.assertIsInstance(marker_amp, float)
        
        # Test peak search
        peak_freq, peak_amp = self.spec_an.searchPeak()
        self.assertIsInstance(peak_freq, float)
        self.assertIsInstance(peak_amp, float)
        
        print(f"Marker 1 amplitude: {marker_amp:.2f} dBm")
        print(f"Peak: {peak_freq/1e6:.3f} MHz at {peak_amp:.2f} dBm")
        
    def test_measurement_timing_optimization(self):
        """Test measurement timing for optimization compatibility"""
        print("\n=== Testing Measurement Timing ===")
        
        # Test single sweep timing
        start_time = time.time()
        self.spec_an.singleSweep()
        single_sweep_time = time.time() - start_time
        
        # Test trace acquisition timing
        start_time = time.time()
        trace_data = self.spec_an.getTraceData()
        trace_time = time.time() - start_time
        
        # Test marker measurement timing
        start_time = time.time()
        marker_amp = self.spec_an.getMarkerAmplitude(1)
        marker_time = time.time() - start_time
        
        print(f"Single sweep time: {single_sweep_time:.3f}s")
        print(f"Trace acquisition time: {trace_time:.3f}s")
        print(f"Marker measurement time: {marker_time:.3f}s")
        
        # Verify measurements are reasonably fast
        self.assertLess(single_sweep_time, 2.0, "Single sweep should be fast")
        self.assertLess(trace_time, 1.0, "Trace acquisition should be fast")
        self.assertLess(marker_time, 0.5, "Marker measurement should be fast")
        
    def test_measurement_modes(self):
        """Test different measurement modes"""
        # Test spectrum analyzer mode
        self.spec_an.setMode("SPECTRUM")
        mode = self.spec_an.getMode()
        self.assertEqual(mode, "SPECTRUM")
        
        # Test power measurement mode
        self.spec_an.setMode("POWER")
        mode = self.spec_an.getMode()
        self.assertEqual(mode, "POWER")
        
        print(f"Current mode: {mode}")
        
    def test_averaging_configuration(self):
        """Test averaging configuration"""
        # Test averaging count
        avg_count = 10
        self.spec_an.setAveraging(avg_count)
        current_avg = self.spec_an.getAveraging()
        self.assertEqual(current_avg, avg_count)
        
        # Test averaging type
        self.spec_an.setAveragingType("RMS")
        avg_type = self.spec_an.getAveragingType()
        self.assertEqual(avg_type, "RMS")
        
        print(f"Averaging: {current_avg} ({avg_type})")
        
    def test_error_handling(self):
        """Test error handling scenarios"""
        # Test disconnected operations
        self.spec_an.disconnect()
        
        with self.assertRaises(Exception):
            self.spec_an.getTraceData()
            
        with self.assertRaises(Exception):
            self.spec_an.setCenterFrequency(1000e6)
            
        # Reconnect for other tests
        self.spec_an.connect("TCPIP::*************::INSTR")
        
        # Test invalid frequency
        with self.assertRaises(ValueError):
            self.spec_an.setCenterFrequency(-1000)  # Negative frequency
            
        # Test invalid span
        with self.assertRaises(ValueError):
            self.spec_an.setSpan(0)  # Zero span
            
    def test_reset_functionality(self):
        """Test reset functionality"""
        # Change some settings
        self.spec_an.setCenterFrequency(2000e6)
        self.spec_an.setSpan(20e6)
        self.spec_an.setReferenceLevel(-10.0)
        
        # Reset
        self.spec_an.reset()
        
        # Verify reset to defaults
        freq = self.spec_an.getCenterFrequency()
        span = self.spec_an.getSpan()
        ref_level = self.spec_an.getReferenceLevel()
        
        self.assertEqual(freq, 1000e6)    # Default frequency
        self.assertEqual(span, 100e6)     # Default span
        self.assertEqual(ref_level, 0.0)  # Default reference level
        
        print("Reset functionality verified")
        
    def test_optimization_compatibility(self):
        """Test compatibility with system optimizations"""
        print("\n=== Testing Optimization Compatibility ===")
        
        # Test rapid configuration changes (simulating optimized test flow)
        start_time = time.time()
        
        # Simulate measurement sequence from optimized test procedure
        configurations = [
            (1030e6, 10e6, -20.0),  # Transponder frequency
            (978e6, 5e6, -30.0),    # UAT frequency
            (1090e6, 20e6, -10.0),  # Mode S frequency
        ]
        
        measurements = []
        for center_freq, span, ref_level in configurations:
            self.spec_an.setCenterFrequency(center_freq)
            self.spec_an.setSpan(span)
            self.spec_an.setReferenceLevel(ref_level)
            
            # Take measurement
            peak_freq, peak_amp = self.spec_an.searchPeak()
            measurements.append((peak_freq, peak_amp))
            
        total_time = time.time() - start_time
        
        print(f"Rapid configuration sequence: {total_time:.3f}s")
        print(f"Measurements taken: {len(measurements)}")
        
        # Verify all measurements are valid
        for freq, amp in measurements:
            self.assertIsInstance(freq, float)
            self.assertIsInstance(amp, float)
            self.assertGreater(freq, 0)
            
        print("✓ Spectrum analyzer compatible with optimized timing")


class TestN9010BPerformance(unittest.TestCase):
    """Performance tests for N9010B Spectrum Analyzer"""
    
    def setUp(self):
        """Set up performance test fixtures"""
        self.mock_rm = Mock()
        self.spec_an = MockSpectrumAnalyzer(self.mock_rm)
        self.spec_an.connect("TCPIP::*************::INSTR")
        
    def test_sweep_performance(self):
        """Test sweep performance"""
        print("\n=== Spectrum Analyzer Sweep Performance ===")
        
        # Test different sweep configurations
        sweep_configs = [
            (1e3, 0.1),    # 1 kHz RBW, 100 ms sweep
            (10e3, 0.05),  # 10 kHz RBW, 50 ms sweep
            (100e3, 0.01), # 100 kHz RBW, 10 ms sweep
        ]
        
        for rbw, sweep_time in sweep_configs:
            self.spec_an.setResolutionBandwidth(rbw)
            self.spec_an.setSweepTime(sweep_time)
            
            start_time = time.time()
            self.spec_an.singleSweep()
            actual_time = time.time() - start_time
            
            print(f"RBW: {rbw/1e3:.0f} kHz, Target: {sweep_time*1000:.0f} ms, Actual: {actual_time*1000:.1f} ms")
            
    def test_measurement_throughput(self):
        """Test measurement throughput"""
        print("\n=== Measurement Throughput Test ===")
        
        num_measurements = 10
        
        # Test marker measurement throughput
        start_time = time.time()
        for i in range(num_measurements):
            amp = self.spec_an.getMarkerAmplitude(1)
            
        marker_time = time.time() - start_time
        
        # Test peak search throughput
        start_time = time.time()
        for i in range(num_measurements):
            freq, amp = self.spec_an.searchPeak()
            
        peak_time = time.time() - start_time
        
        print(f"Marker measurements ({num_measurements}): {marker_time:.3f}s")
        print(f"Peak searches ({num_measurements}): {peak_time:.3f}s")
        print(f"Marker rate: {num_measurements/marker_time:.1f} meas/s")
        print(f"Peak rate: {num_measurements/peak_time:.1f} meas/s")


if __name__ == '__main__':
    unittest.main(verbosity=2)
