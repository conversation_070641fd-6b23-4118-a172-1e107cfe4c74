''' Instrument handler for NI PXI-6528 Card '''

import time, nidaqmx

class NI6528Discretes():
    def __init__(self, ate_rm):
        try:
            self.NI6528 = None
            self.resourceManager = ate_rm
            # Ensures we can find card even if installed in wrong slot
            for device in list(nidaqmx.system.system.System.local().devices):
                if device.product_type == "PXI-6528":
                    self.NI6528 = nidaqmx.system.device.Device(device.name)
            if self.NI6528 == None:
                self.resourceManager.logMessage(3, "Couldn't find PXI-6528")
            else:
                self.resourceManager.logMessage(0, "Resource Opened")
        except:
            self.resourceManager.logMessage(3, "Resource Failed to Open")
            raise
        
    def ident(self):
        model = self.NI6528.product_type
        name = self.NI6528.name
        self.resourceManager.logMessage(1, "Model: {} Name: {}".format(str(model), str(name)))
        return (model, name)
    
    def reset(self):
        self.resourceManager.logMessage(2, "Resetting Device...")
        self.NI6528.reset_device()
        self.resourceManager.logMessage(2, "Device Reset")

    def selfTest(self):
        self.resourceManager.logMessage(1, "Performing Self Test...")
        self.NI6528.self_test_device()
        self.resourceManager.logMessage(1, "Self Test completed")

    # Digital I/O Operations
    
    ''' Read a single channel '''
    def readChannel(self, port, line):
        with nidaqmx.Task() as readTask:
            if port < 3: # input channel
                readTask.di_channels.add_di_chan("{}/port{}/line{}".format(self.NI6528.name, str(port), str(line)))
            else: # output channel
                readTask.do_channels.add_do_chan("{}/port{}/line{}".format(self.NI6528.name, str(port), str(line)))
            data = readTask.read()
        self.resourceManager.logMessage(1, "Port {} Line {} Value: {}".format(str(port), str(line), str(data)))
        return data
    
    ''' Read a port of channels, data returned as an integer '''
    def readPort(self, port):
        with nidaqmx.Task() as readTask:
            if port < 3: # input channel
                readTask.di_channels.add_di_chan("{}/port{}/line0:{}/port{}/line7".format(self.NI6528.name, str(port), self.NI6528.name, str(port)))
            else: # output channel
                readTask.do_channels.add_do_chan("{}/port{}/line0:{}/port{}/line7".format(self.NI6528.name, str(port), self.NI6528.name, str(port)))
            data = readTask.read()
        self.resourceManager.logMessage(1, "Port {} Data: {}".format(str(port), str(data)))
        return data

    ''' Write a value to an output channel '''
    def writeChannel(self, port, line, value):
        with nidaqmx.Task() as writeTask:
            writeTask.do_channels.add_do_chan("{}/port{}/line{}".format(self.NI6528.name, str(port), str(line)))
            samples = writeTask.write(bool(value))
        self.resourceManager.logMessage(1, "{} written to Port {} Line {}".format(str(bool(value)), str(port), str(line)))
        return samples

    ''' Write values to a port of output channels. value is in integer form '''
    def writePort(self, port, value):
        with nidaqmx.Task() as writeTask:
            writeTask.do_channels.add_do_chan("{}/port{}/line0:{}/port{}/line7".format(self.NI6528.name, str(port), self.NI6528.name, str(port)))
            samples = writeTask.write(value)
        self.resourceManager.logMessage(1, "{} written to Port {}".format(str(value), str(port)))
        return samples
    