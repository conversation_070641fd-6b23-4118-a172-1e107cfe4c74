''' Collection of functions to initialize and control
    the SPI devices on the TXD RF Board. '''

import os, numpy, time, csv, math, glob, json, random, socket, base64
from TXDLib.Handlers import UUTReg
from TXDLib.Procedures import mem_map
from statistics import median
from datetime import date, datetime
import sys

''' DAC104S085 '''
def updateQuadDAC(aterm, DACA, DACB, DACC, DACD):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]
    R92 = 10000
    R89 = 10000
    R91 = 20000
    Vref = 5

    E1_OUTPUT_BIAS_DAC = Vref*DACA/(1023)
    E1_OUTPUT_BIAS_GATE = ((E1_OUTPUT_BIAS_DAC*R89+Vref*R92)*R91)/(R89*(R91+R92)+R91*R92)
    E2_OUTPUT_BIAS_DAC = Vref*DACB/(1023)
    E2_OUTPUT_BIAS_GATE = ((E2_OUTPUT_BIAS_DAC*R89+Vref*R92)*R91)/(R89*(R91+R92)+R91*R92)

    E1_DRIVER_BIAS_DAC = Vref*DACC/(1023)
    E1_DRIVER_BIAS_GATE = ((E1_DRIVER_BIAS_DAC*R89+Vref*R92)*R91)/(R89*(R91+R92)+R91*R92)
    E2_DRIVER_BIAS_DAC = Vref*DACD/(1023)
    E2_DRIVER_BIAS_GATE = ((E2_DRIVER_BIAS_DAC*R89+Vref*R92)*R91)/(R89*(R91+R92)+R91*R92)

    DACA_WORD = hex(int(bin(DACA)[2:].zfill(10) + '00',2))
    aterm.logMessage(1, 'DACA Set to ' + str(DACA) + ' (' + DACA_WORD + ')')
    aterm.logMessage(1, 'VOUTA  = ' + str(E1_OUTPUT_BIAS_DAC) + 'V, E1_OUTPUT_BIAS = ' + str(E1_OUTPUT_BIAS_GATE))
    DACB_WORD = hex(int(bin(DACB)[2:].zfill(10) + '00',2))
    aterm.logMessage(1, 'DACB Set to ' + str(DACB) + ' (' + DACB_WORD + ')')
    aterm.logMessage(1, 'VOUTB  = ' + str(E2_OUTPUT_BIAS_DAC) + 'V, E2_OUTPUT_BIAS = ' + str(E2_OUTPUT_BIAS_GATE))
    DACC_WORD = hex(int(bin(DACC)[2:].zfill(10) + '00',2))
    aterm.logMessage(1, 'DACC Set to ' + str(DACC) + ' (' + DACC_WORD + ')')
    aterm.logMessage(1, 'VOUTC  = ' + str(E1_DRIVER_BIAS_DAC) + 'V, E1_DRIVER_BIAS = ' + str(E1_DRIVER_BIAS_GATE))
    DACD_WORD = hex(int(bin(DACD)[2:].zfill(10) + '00',2))
    aterm.logMessage(1, 'DACD Set to ' + str(DACD) + ' (' + DACD_WORD + ')')
    aterm.logMessage(1, 'VOUTD  = ' + str(E2_DRIVER_BIAS_DAC) + 'V, E2_DRIVER_BIAS = ' + str(E2_DRIVER_BIAS_GATE))
    count = 0
    #READ = 1
    WRITE = 0

    # TX_DAC_CLK
    LINK_ID = int(0x3)
    DEVICE_ID = int(0x1)
    DATA_BYTE_COUNT = 0

    SLAVE_ADDRESS = hex(int('00' + '01',2)) # E1 Output
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, int(SLAVE_ADDRESS,16), int(DACA_WORD,16))
                                )
    count += 1
    SLAVE_ADDRESS = hex(int('01' + '01',2)) # E2 Output
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, int(SLAVE_ADDRESS,16), int(DACB_WORD,16))
                                )
    count += 1
    SLAVE_ADDRESS = hex(int('10' + '01',2)) # E1 Driver
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, int(SLAVE_ADDRESS,16), int(DACC_WORD,16))
                                )
    count += 1
    SLAVE_ADDRESS = hex(int('11' + '01',2)) # E2 Driver
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, int(SLAVE_ADDRESS,16), int(DACD_WORD,16))
                                )
    aterm.logMessage(1, "Procedure Ended")

''' Functions to aid in programming synthesizers '''
def getLockStatus(aterm):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]
    # Get Read Status Register:
    count = 0
    SPI_Status_Result = UUTReg.readRFRegister(count, mem_map.SPI_STATUS_REG)
    SPI_Status_Result_Bin = bin(int(SPI_Status_Result[2:],16))[2:].zfill(64)
    aterm.logMessage(1, "SPI Status Result:" + SPI_Status_Result)
    aterm.logMessage(1, "Bin SPI Status Result:" + SPI_Status_Result_Bin)
    RX_SYNTH_LOC_DET = SPI_Status_Result_Bin[-25]
    TX_LO_MUXOUT = SPI_Status_Result_Bin[-24]
    TX_IF_MUXOUT = SPI_Status_Result_Bin[-23]
    COM_ERR = SPI_Status_Result_Bin[-22]
    aterm.logMessage(1, "RX_SYNTH_LOC_DET Status Result:" + RX_SYNTH_LOC_DET)
    aterm.logMessage(1, "TX_LO_SYNTH Status Result:" + TX_LO_MUXOUT)
    aterm.logMessage(1, "TX_IF_CLK_MUXOUT Status Result:" + TX_IF_MUXOUT)
    aterm.logMessage(1, "COM_ERR Status Result:" + COM_ERR)
    aterm.logMessage(1, "Procedure Ended")
    return [RX_SYNTH_LOC_DET, TX_LO_MUXOUT, TX_IF_MUXOUT]

def programTxSynth(aterm, deviceID, rCounter, control, nCounter):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]
    count = 1
    #READ = 1
    WRITE = 0
    LINK_ID = int(0x4)
    DEVICE_ID = int(deviceID)
    DATA_BYTE_COUNT = 0
    SLAVE_ADDRESS = 0

    # R Counter
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, rCounter)
                                )
    # Control
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, control)
                                )

    # Tell SPI Register to sleep for 20ms
    LINK_ID = int(0xF)
    DEVICE_ID = int(0xF)
    SPI_Sleep_Time = 20000
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0, SPI_Sleep_Time)
                                )
    LINK_ID = int(0x4)
    DEVICE_ID = int(deviceID)
    # SPISleep(count, 20000) # helper function will replace above sleep code

    # N Counter
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, nCounter)
                                )
    aterm.logMessage(1, "Procedure Ended")

''' Perform dynamic biasing of amplfiers, continues until target current is met for all four '''
def runBias(aterm, targetCurrent=[0.180, 2.000, 0.150, 0.800], manualGate=[200, 200, 200, 200], customDir=None):
    aterm.logMessage(1, "Procedure Started")

    today = datetime.today()
    todayDate = today.strftime("%b-%d-%Y--%H-%M-%S")
    newFolder = "\\Bias\\TXSynthBias_" + todayDate

    # initial variables
    measuredCurrent = [0, 0, 0, 0]
    loop = 0 # track how many passes it is taking
    done = False
    MAX_ATTEMPTS = 20
    HIGHEST_DAC_COUNT = pow(2,10) - 1
    LOW_TARGET = 0.95
    HIGH_TARGET = 1.05

    # Make new directory path to store csv files
    if customDir == None:
        csvPath = "C:\\Test Data\\TXD RF" + newFolder
    else:
        csvPath = customDir + newFolder
    os.makedirs(csvPath)

    # index labels to track amplifier location
    e1Driver = 0
    e1Total  = 1
    e2Driver = 2
    e2Total  = 3

    '''Initial gate count; provide desired value in manualGate list if no biasing desired
       for a particular amplifier'''
    DACA = manualGate[e1Total] if targetCurrent[e1Total] == None else 100    # E1 Output Gate Count
    DACB = manualGate[e2Total] if targetCurrent[e2Total] == None else 200   # E2 Output Gate Count
    DACC = manualGate[e1Driver] if targetCurrent[e1Driver] == None else 200 # E1 Driver Gate Count
    DACD = manualGate[e2Driver] if targetCurrent[e2Driver] == None else 200 # E2 Driver Gate Count

    dacVoltages = [DACC, DACA, DACD, DACB]

    # history of voltage and current for building regression model
    # E1 Driver, E1 Output, E2 Driver, E2 Output
    gateNomineeHistory = [[0], [0], [0], [0]]
    currentsHistory    = [[0], [0], [0], [0]]

    doneList = [False, False, False, False]

    while (done == False and loop <= MAX_ATTEMPTS):

        loop += 1

        # DACA_WORD = hex(int(bin(dacVoltages[e1Total])[2:].zfill(10) + '00', 2))
        # DACB_WORD = hex(int(bin(dacVoltages[e2Total])[2:].zfill(10) + '00', 2))
        # DACC_WORD = hex(int(bin(dacVoltages[e1Driver])[2:].zfill(10) + '00', 2))
        # DACD_WORD = hex(int(bin(dacVoltages[e2Driver])[2:].zfill(10) + '00', 2))
        DACA_WORD = hex(int(bin(dacVoltages[e1Total])[2:].zfill(12), 2))
        DACB_WORD = hex(int(bin(dacVoltages[e2Total])[2:].zfill(12), 2))
        DACC_WORD = hex(int(bin(dacVoltages[e1Driver])[2:].zfill(12), 2))
        DACD_WORD = hex(int(bin(dacVoltages[e2Driver])[2:].zfill(12), 2))

        # READ = 1
        WRITE = 0
        LINK_ID = int(0x3)
        DEVICE_ID = int(0x1)
        DATA_BYTE_COUNT = 0
        SLAVE_ADDRESS_E1_OUTPUT = hex(int('00' + '01', 2)) #E1 Output
        SLAVE_ADDRESS_E2_OUTPUT = hex(int('01' + '01', 2)) #E2 Output
        SLAVE_ADDRESS_E1_DRIVER = hex(int('10' + '01', 2)) #E1 Driver
        SLAVE_ADDRESS_E2_DRIVER = hex(int('11' + '01', 2)) #E2 Driver
        # E1_OUTPUT_VALUE = genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, int(SLAVE_ADDRESS_E1_OUTPUT, 16), int(DACA_WORD, 16))
        # E2_OUTPUT_VALUE = genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, int(SLAVE_ADDRESS_E2_OUTPUT, 16), int(DACB_WORD, 16))
        # E1_DRIVER_VALUE = genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, int(SLAVE_ADDRESS_E1_DRIVER, 16), int(DACC_WORD, 16))
        # E2_DRIVER_VALUE = genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, int(SLAVE_ADDRESS_E2_DRIVER, 16), int(DACD_WORD, 16))
        E1_OUTPUT_VALUE = int(DACA_WORD, 16)
        E2_OUTPUT_VALUE = int(DACB_WORD, 16)
        E1_DRIVER_VALUE = int(DACC_WORD, 16)
        E2_DRIVER_VALUE = int(DACD_WORD, 16)
        PROGRAM_DAC(7, E1_DRIVER_VALUE, E1_OUTPUT_VALUE, E2_DRIVER_VALUE, E2_OUTPUT_VALUE)

        # Time for current readings to settle
        time.sleep(0.3)

        dacVoltagesList = [["E1_DRIVER_VOLTAGE"] + [dacVoltages[e1Driver]] * 100,
                           ["E2_DRIVER_VOLTAGE"] + [dacVoltages[e2Driver]] * 100,
                           ["E1_OUTPUT_VOLTAGE"] + [dacVoltages[e1Total]] * 100,
                           ["E2_OUTPUT_VOLTAGE"] + [dacVoltages[e2Total]] * 100]

        countE1Driver = []
        countE1Total  = []
        countE2Driver = []
        countE2Total  = []
        try:
            (countE1Driver, countE2Driver, countE1Total, countE2Total) = MEASURE_ADC(5)
        except RuntimeError as e:
            aterm.logMessage(3, e)
            raise

        # Convert count arrays to current
        countDecE1Driver = ["E1_DRIVER_ADC"]
        vSenseE1Driver   = ["E1_DRIVER_VSENSE"]
        a1 = []
        for i in range(len(countE1Driver)):
            current_data = int(countE1Driver[i],16)
            # perform two's complement if neccessary
            if (current_data & 0x1000) == 0x1000:
                current_data = -((current_data ^ 0x1fff) + 1)
            countDecE1Driver.append(current_data)
            a_value = convertToCurrent(current_data, 0.1, vSenseE1Driver)
            a1.append(a_value)

        countDecE2Driver = ["E2_DRIVER_ADC"]
        vSenseE2Driver   = ["E2_DRIVER_VSENSE"]
        b1 = []
        for i in range(len(countE2Driver)):
            current_data = int(countE2Driver[i],16)
            # perform two's complement if neccessary
            if (current_data & 0x1000) == 0x1000:
                current_data = -((current_data ^ 0x1fff) + 1)
            countDecE2Driver.append(current_data)
            b_value = convertToCurrent(current_data, 0.1, vSenseE2Driver)
            b1.append(b_value)

        c1 = []
        countDecE1Total = ["E1_TOTAL_ADC"]
        vSenseE1Total   = ["E1_TOTAL_VSENSE"]
        for i in range(len(countE1Total)):
            current_data = int(countE1Total[i],16)
            # perform two's complement if neccessary
            if (current_data & 0x1000) == 0x1000:
                current_data = -((current_data ^ 0x1fff) + 1)
            countDecE1Total.append(current_data)
            c_value = convertToCurrent(current_data, 0.05, vSenseE1Total)
            c1.append(c_value)

        countDecE2Total = ["E2_TOTAL_ADC"]
        d1 = []
        vSenseE2Total   = ["E2_TOTAL_VSENSE"]
        for i in range(len(countE2Total)):
            current_data = int(countE2Total[i],16)
            # perform two's complement if neccessary
            if (current_data & 0x1000) == 0x1000:
                current_data = -((current_data ^ 0x1fff) + 1)
            countDecE2Total.append(current_data)
            d_value = convertToCurrent(current_data, 0.05, vSenseE2Total)
            d1.append(d_value)

        # grab current values from list of samples
        driverCurrentE1 = median(a1)
        measuredCurrent[e1Driver] = driverCurrentE1
        driverCurrentE2 = median(b1)
        measuredCurrent[e2Driver] = driverCurrentE2
        finalCurrentE1  = median(c1) - driverCurrentE1
        measuredCurrent[e1Total] = finalCurrentE1
        finalCurrentE2  = median(d1) - driverCurrentE2
        measuredCurrent[e2Total] = finalCurrentE2

        R92 = 10000
        R89 = 10000
        R91 = 20000
        Vref = 5

        E1_OUTPUT_BIAS_DAC = Vref * dacVoltages[e1Total] / (1023)
        E1_OUTPUT_BIAS_GATE = ((E1_OUTPUT_BIAS_DAC * R89 + Vref * R92) * R91) / (R89 * (R91 + R92) + R91 * R92)
        E2_OUTPUT_BIAS_DAC = Vref * dacVoltages[e2Total] / (1023)
        E2_OUTPUT_BIAS_GATE = ((E2_OUTPUT_BIAS_DAC * R89 + Vref * R92) * R91) / (R89 * (R91 + R92) + R91 * R92)

        E1_DRIVER_BIAS_DAC = Vref * dacVoltages[e1Driver] / (1023)
        E1_DRIVER_BIAS_GATE = ((E1_DRIVER_BIAS_DAC * R89 + Vref * R92) * R91) / (R89 * (R91 + R92) + R91 * R92)
        E2_DRIVER_BIAS_DAC = Vref * dacVoltages[e2Driver] / (1023)
        E2_DRIVER_BIAS_GATE = ((E2_DRIVER_BIAS_DAC * R89 + Vref * R92) * R91) / (R89 * (R91 + R92) + R91 * R92)

        # log attempt
        aterm.logMessage(1, 'DACA Set to ' + str(dacVoltages[e1Total]) + ', E1_OUTPUT_BIAS = ' + str(round(E1_OUTPUT_BIAS_GATE, 3)) + 'V -> Resulting Current = ' + str(round(finalCurrentE1, 3)) + 'A')
        aterm.logMessage(1, 'DACB Set to ' + str(dacVoltages[e2Total]) + ', E2_OUTPUT_BIAS = ' + str(round(E2_OUTPUT_BIAS_GATE, 3)) + 'V -> Resulting Current = ' + str(round(finalCurrentE2, 3)) + 'A')
        aterm.logMessage(1, 'DACC Set to ' + str(dacVoltages[e1Driver]) + ', E1_DRIVER_BIAS = ' + str(round(E1_DRIVER_BIAS_GATE, 3)) + 'V -> Resulting Current = ' + str(round(driverCurrentE1, 3)) + 'A')
        aterm.logMessage(1, 'DACD Set to ' + str(dacVoltages[e2Driver]) + ', E2_DRIVER_BIAS = ' + str(round(E2_DRIVER_BIAS_GATE, 3)) + 'V -> Resulting Current = ' + str(round(driverCurrentE2, 3)) + 'A')

        a1.insert(0, "E1_DRIVER_CURRENT")
        b1.insert(0, "E2_DRIVER_CURRENT")
        c1.insert(0, "E1_TOTAL_CURRENT")
        d1.insert(0, "E2_TOTAL_CURRENT")

        # inverse all lists
        csvList = dacVoltagesList + [countDecE1Driver] + [countDecE2Driver] + [countDecE1Total] + [countDecE2Total] + [vSenseE1Driver] + [vSenseE2Driver] + [vSenseE1Total] + [vSenseE2Total] + [a1] + [b1] + [c1] + [d1]
        csvList = zip(*csvList)

        # write to csv file
        # ADC_Count, ADC_Voltage, Vsense, current
        # for E1 Driver, E2 Driver, E1 Total, and E2 Total
        csv_filename = csvPath + "\\BIAS_SAMPLE_ADC_run_" + str(loop) + '.csv'
        with open(csv_filename, 'w', newline = '') as csvFile:
            writer = csv.writer(csvFile, delimiter = ',')
            for column in csvList:
                writer.writerow(column)

        # polynomial fit
        for i in range(0, 4):
            lastGateNominee = dacVoltages[i] # Current bias voltage on selected amplifier
            lastCurrent = measuredCurrent[i] # Active current of selected amplifier
            if lastGateNominee not in gateNomineeHistory[i]:
                gateNomineeHistory[i].append(lastGateNominee)
                currentsHistory[i].append(lastCurrent)

            nextGateNominee = 1

            if targetCurrent[i] == None:
                nextGateNominee = lastGateNominee # keep the same gate voltage
                doneList[i] = True
            elif measuredCurrent[i] < 0.100:
                # Low current, selecting midpoint VGS
                nextGateNominee = int(round(lastGateNominee + (HIGHEST_DAC_COUNT - lastGateNominee) / 2, 0))
            elif measuredCurrent[i] < LOW_TARGET * targetCurrent[i] or measuredCurrent[i] > HIGH_TARGET * targetCurrent[i]: # Not within 5% target
                if len(currentsHistory[i]) <= 3:
                    if lastCurrent < targetCurrent[i]:
                        # take midpoint between current voltage and max voltage
                        nextGateNominee = lastGateNominee + 100  # rounding?
                    else:
                        # take midpoint between 1 and current voltage
                        nextGateNominee = int((lastGateNominee + 1) / 2)
                else:
                    # create quadratic regression equation from gatenominee and current history
                    # use to find next nominee
                    x = numpy.array(gateNomineeHistory[i])
                    y = numpy.array(currentsHistory[i])
                    z = numpy.polynomial.polynomial.Polynomial.fit(x, y, 2)
                    z = z.convert().coef # grab coefficients
                    # print(z)
                    a = z[2]
                    b = z[1]
                    c = z[0] - targetCurrent[i]
                    if 4 * a * c <= math.pow(b, 2): # make sure we dont get an imaginary number back
                        coefficients = [a, b, c]
                        nextGateNominee = int(round(max(numpy.roots(coefficients)), 0)) # take largest root of quadratc equation
                    else:
                        nextGateNominee = lastGateNominee # remeasure
                ## Dont try the same gate value twice
                if nextGateNominee in gateNomineeHistory[i]:
                    if lastCurrent < targetCurrent[i]:
                        nextGateNominee = int(round(nextGateNominee + 0.01 * HIGHEST_DAC_COUNT, 0))
                    else:
                        nextGateNominee = int(round(nextGateNominee - 0.01 * HIGHEST_DAC_COUNT, 0))

            else:
                # print("keeping last nominee " + str(i))
                nextGateNominee = lastGateNominee # nominee works, keep it!
                doneList[i] = True

            # assign new gate candidate unless we reached maximum # of tries
            if loop < MAX_ATTEMPTS - 1:
                dacVoltages[i] = min(max(nextGateNominee,0),1023)

        # if all four voltages are done, exit at next loop
        done = doneList[0] & doneList[1] & doneList[2] & doneList[3]

    if (done):
        aterm.logMessage(0, "Biasing completed in " + str(loop) + " attempts")
    else:
        aterm.logMessage(3, "Biasing FAILED: could not reach target current within " + str(MAX_ATTEMPTS) + " attempts")

    aterm.logMessage(1, "Procedure Ended")
    return (dacVoltages, measuredCurrent)

def programRxRFSynth(aterm, freq):
    aterm.logMessage(1, "Procedure Started - freq = %s"%str(freq))
    UUTReg = aterm.instruments["UUTReg"]
    count = 0
    #READ = 1
    WRITE = 0
    LINK_ID = int(0x1)
    DEVICE_ID = int(0x1)
    DATA_BYTE_COUNT = 0
    SLAVE_ADDRESS = 0

    rfNRegInt = int(freq/10)
    rfNRgFrac = int((freq)-(rfNRegInt*10))
    RF_N_Register = (rfNRgFrac<<3)|(rfNRegInt<<15)

    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, RF_N_Register)
                                )

    time.sleep(0.1)
    aterm.logMessage(1, "Procedure Ended")

def rxADCDCOffset(aterm, offsets):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]
    LINK_ID = 0
    #Devide ID #1 is TCAS/XPDR.  #2 is UAT/DME
    DEVICE_ID = 1
    DATA_BYTE_COUNT = 0
    WRITE = 0
    READ = 1
    count = 0
    #DME
    E1_offset = abs(int(offsets[0]))
    #UAT
    E2_offset = abs(int(offsets[1]))
    #TCAS_S
    E3_offset = abs(int(offsets[2]))
    #TCAS_P
    E4_offset = abs(int(offsets[3]))

    #SET THE OFFSETS
    #DC Offset E1 DME def=0
    DEVICE_ID = 2
    SLAVE_ADDRESS = 0x05
    DATA = 0x01
    count += 1
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)
    
    DEVICE_ID = 2
    SLAVE_ADDRESS = 0x10
    DATA = E1_offset
    count += 1
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)
    
    #DC Offset E2 UAT def=0
    DEVICE_ID = 2
    SLAVE_ADDRESS = 0x05
    DATA = 0x02
    count += 1
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)
    
    DEVICE_ID = 2
    SLAVE_ADDRESS = 0x10
    DATA = E2_offset
    count += 1
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)
    
    #DC Offset E3 TCAS Secondary def=0
    DEVICE_ID = 1
    SLAVE_ADDRESS = 0x05
    DATA = 0x01
    count += 1
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)

    DEVICE_ID = 1
    SLAVE_ADDRESS = 0x10
    DATA = E3_offset
    count += 1
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)

    #DC Offset E4 TCAS Primary def=0
    DEVICE_ID = 1
    SLAVE_ADDRESS = 0x05
    DATA = 0x02
    count += 1
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)

    DEVICE_ID = 1
    SLAVE_ADDRESS = 0x10
    DATA = E4_offset
    count += 1
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)
    aterm.logMessage(1, "Procedure Ended")

def programRxIFSynth(aterm, freq):
    aterm.logMessage(1, "Procedure Started - freq = %s"%str(freq))
    UUTReg = aterm.instruments["UUTReg"]
    count = 0
    #READ = 1
    WRITE = 0
    LINK_ID = int(0x1)
    DEVICE_ID = int(0x1)
    DATA_BYTE_COUNT = 0
    SLAVE_ADDRESS = 0

    IF_N_CP_GAIN = 0
    IF_N_PRESCALER = 0
    IF_N_B_COUNTER = int(freq/2)
    IF_N_A_COUNTER = 0 if (freq%2==0) else 4
    IF_N_CTRL = 4
    IF_N_Register = ((IF_N_CP_GAIN<<23) | (IF_N_PRESCALER<<21) | (IF_N_B_COUNTER<<9) | (IF_N_A_COUNTER<<3) | (IF_N_CTRL))

    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, IF_N_Register)
                                )
    time.sleep(0.1)
	# IF R Counter Register 5
    UUTReg.writeRFRegister(6, mem_map.SPI_COM_FIFO_LO, 0x1102000000000C85)
    time.sleep(0.1)
    aterm.logMessage(1, "Procedure Ended")

def setDMEAtten(aterm, value):
    aterm.logMessage(1, "Procedure Started - value = %s"%str(value))
    UUTReg = aterm.instruments["UUTReg"]
    count = 0
    #READ = 1
    WRITE = 0
    LINK_ID = 0
    DEVICE_ID = 0
    DATA_BYTE_COUNT = 0
    SLAVE_ADDRESS = 0

    count += 1
    if value==0:    
        UUTReg.writeRFRegister(count, mem_map.DME_CFG_REG,0x0000000000900000)
    else:
        UUTReg.writeRFRegister(count, mem_map.DME_CFG_REG,0x0000FFFFFF900000)
    time.sleep(0.1)
    aterm.logMessage(1, "Procedure Ended")

def initRxADC(aterm):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]
    LINK_ID = 0
    DEVICE_ID = 2
    DATA_BYTE_COUNT = 0
    WRITE = 0

    count = 0

    SLAVE_ADDRESS = 0x05
    DATA = 3
    count += 1
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)

    #twos comp
    SLAVE_ADDRESS = 0x14
    DATA = 0x81
    count += 1
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)

    #Sets the clock delay
    SLAVE_ADDRESS = 0x17
    DATA = 0xA7
    count += 1
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)
    aterm.logMessage(1, "Procedure Ended")

def setRxAdcDelay(aterm, index):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]

    LINK_ID = 0
    DEVICE_ID = 2
    DATA_BYTE_COUNT = 0
    WRITE = 0
    SLAVE_ADDRESS = 0x17
    DATA = (0xA0 | index)
    count = 0
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
    time.sleep(0.5)
    aterm.logMessage(1, "Procedure Ended")

def setRFSW(aterm, switchStates):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]

    count = 0
    UUTReg.writeRFRegister(count, mem_map.DBG_BIT_RF_SW_CTRL, switchStates)
    time.sleep(0.5)
    aterm.logMessage(1, "Procedure Ended")

def initRxSynth(aterm):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]

    count = 0
    #READ = 1
    WRITE = 0
    LINK_ID = int(0x1)
    DEVICE_ID = int(0x1)
    DATA_BYTE_COUNT = 0
    SLAVE_ADDRESS = 0

    RF_N_Register = int(0x460000)
    R_Counter_Reg_1 = int(0x150051)
    RF_Control_Reg_2 = int(0x008EC2)
    IF_N_Counter_Reg_4 = int(0x00E01C)
    IF_R_Counter_Reg_5 = int(0x000325)
    IF_Control_Reg_6 = int(0x0006C6)
    LockDet_Setup_3 = int(0x0006C3)

    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, RF_N_Register)
                                )
    time.sleep(0.1)
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, R_Counter_Reg_1)
                                )
    time.sleep(0.1)
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, RF_Control_Reg_2)
                                )
    time.sleep(0.1)
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, IF_N_Counter_Reg_4)
                                )
    time.sleep(0.1)
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, IF_R_Counter_Reg_5)
                                )
    time.sleep(0.1)
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, IF_Control_Reg_6)
                                )
    time.sleep(0.1)
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, LockDet_Setup_3)
                                )
    time.sleep(0.1)
    aterm.logMessage(1, "Procedure Ended")

''' Helper functions '''

def PROGRAM_DAC_old(requestId, e1_driver_dac_word, e1_final_dac_word, e2_driver_dac_word, e2_final_dac_word):
    today = date.today()
    this_year = str(today)[:4]
    this_month = str(today)[5:7]
    this_day = str(today)[8:10]
    #current_directory = os.getcwd()
    os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\request")
    data = {
        "bias-message": {
            "requestId": requestId,
            "command": "set_dac",
            "dac_e1_driver": e1_driver_dac_word,
            "dac_e1_final": e1_final_dac_word,
            "dac_e2_driver": e2_driver_dac_word,
            "dac_e2_final": e2_final_dac_word
        }
    }
    for removable_file in glob.glob("*.json_processed"):
        os.remove(removable_file)
    filename = "bias_dac_msg_" + str(requestId) + ".json"
    with open(filename, "w") as write_file:
        json.dump(data, write_file)
    time.sleep(0.5)
    os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\response\\bias-response")
    status_message = 0
    time.sleep(0.7)
    output_file = (glob.glob("bias-response_" + str(requestId) + "_" + this_year + "_" + this_month + "_" + this_day +  "_*.json"))
    with open(max(output_file), "r") as read_file:
        json_response = json.load(read_file)
        status_message = json_response["bias-response"]["status"]
    # os.remove(max(output_file))
    # os.chdir(current_directory)
    if status_message == 256:
        return True
    else:
        return False

def PROGRAM_DAC(requestId, e1_driver_dac_word, e1_final_dac_word, e2_driver_dac_word, e2_final_dac_word):
    UDP_IP = "************"
    UDP_PORT = 5121

    sock = socket.socket(socket.AF_INET, # Internet
                    socket.SOCK_DGRAM) # UDP

    sock.bind((UDP_IP, UDP_PORT))
    r = {
        "bias-message": {
            "requestId": requestId,
            "command": "set_dac",
            "dac_e1_driver": e1_driver_dac_word,
            "dac_e1_final": e1_final_dac_word,
            "dac_e2_driver": e2_driver_dac_word,
            "dac_e2_final": e2_final_dac_word
        }
    }
    r = str.encode(json.dumps(r))
    sock.sendto(r,('*************',UDP_PORT))
    i=0
    status_message = 0
    while i<1:
        data = sock.recv(2048)
        data = data.decode('utf-8').split('\x00')[0]
        json_response = json.loads(data)

        try:
            status_message = (json_response["bias-response"]["status"])
            i+=1
        except KeyError:
            pass
    sock.close()

    if status_message == 256:
        return True
    else:
        return False

def MEASURE_ADC(requestId):
    UDP_IP = "************"
    UDP_PORT = 5121

    sock = socket.socket(socket.AF_INET, # Internet
                    socket.SOCK_DGRAM) # UDP

    sock.bind((UDP_IP, UDP_PORT))

    # Attempt measurement up to two times
    attempt = 0
    while attempt < 2:
        r = {
            "bias-message": {
                "requestId": requestId,
                "command": "measure"
            }
        }
        r = str.encode(json.dumps(r))
        sock.sendto(r,('*************',UDP_PORT))
        return_values = ""
        i=0
        while i<1:
            data = sock.recv(2048)
            data = data.decode('utf-8').split('\x00')[0]
            json_response = json.loads(data)
            try:
                data = (base64.b64decode(json_response['bias-response']['tx_bias_data_base64']))
                i+=1
            except KeyError:
                pass
        sock.close()
        return_values = []
        voltages = []
        dataarr = bytearray(data)
        flippedData = bytearray(data)
        index = 0
        while (index<len(dataarr)):
            for j in range(0,8):
                flippedData[index+j]=dataarr[(7-j)+index]
            index+=8
        return_values=flippedData.hex()
        status = json_response["bias-response"]["status"]
        if int(status, 16) == 0:
            return parse_hex_string(return_values)
        attempt += 1

    # Something wrong with data, abort
    raise RuntimeError("Invalid/Corrupt bias data. Aborting...")

def MEASURE_ADC_old(requestId):
    today = date.today()
    this_year = str(today)[:4]
    this_month = str(today)[5:7]
    this_day = str(today)[8:10]

    # Attempt measurement up to two times
    attempt = 0
    while attempt < 2:
        os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\request")
        data = {
            "bias-message": {
                "requestId": requestId,
                "command": "measure"
            }
        }
        for removable_file in glob.glob("*.json_processed"):
            os.remove(removable_file)
        filename = "bias_offset_msg_" + str(requestId) + ".json"
        with open(filename, "w") as write_file:
            json.dump(data, write_file)
        time.sleep(0.5)
        os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\response\\bias-response")
        return_values = ""
        time.sleep(0.7)
        output_file = (glob.glob("bias-response_" + str(requestId) + "_" + this_year + "_" + this_month + "_" + this_day +  "_*.json"))
        with open(max(output_file), "r") as read_file:
            json_response = json.load(read_file)
            return_values = json_response["bias-response"]["tx_bias_data_hex"]
            status = json_response["bias-response"]["status"]
        if int(status, 16) == 0:
            return parse_hex_string(return_values)
        attempt += 1

    # Something wrong with data, abort
    raise RuntimeError("Invalid/Corrupt bias data. Aborting...")

def parse_hex_string(hex_string):
    e1_driver = []
    e2_driver = []
    e1_total = []
    e2_total = []
    i = 0
    while i < len(hex_string):
        e2_total.append(hex_string[i:i+4].zfill(4))
        e1_total.append(hex_string[i+4:i+8].zfill(4))
        e2_driver.append(hex_string[i+8:i+12].zfill(4))
        e1_driver.append(hex_string[i+12:i+16].zfill(4))
        i = i + 16
    return (e1_driver, e2_driver, e1_total, e2_total)

''' Tell SPI Register to sleep for sleepTime (us) '''
def SPISleep(aterm, count, sleepTime):
    UUTReg = aterm.instruments["UUTReg"]
    linkID = int(0xF)
    deviceID = int(0xF)
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, linkID, deviceID, 0, 0, 0, sleepTime)
                                )

''' Convert attributes into a 64 bit (quad word) binary sequence that can be written to a SPI register '''
def genSPIQword(aterm, Link_ID, Device_ID, Operation, Data_Byte_Count, Slave_Address=False, Data=False):
    #Implement from HSID3077
    # 0x0B00FF
    #Data = str(hex(Data))
    #Data = Data[2:]
    #Data = Data.zfill(8)
    print(Data)
    if Slave_Address == False:
        SlaveAddress = 0
    else:
        SlaveAddress = Slave_Address

    print("Data: " + str(Data))
    if Data == False:
        Data = 0
    else:
        Data = Data
    print("SlaveAddress: "  + str(SlaveAddress))

    binary_string = (bin(Link_ID)[2:].zfill(4) + bin(Device_ID)[2:].zfill(4) + bin(0)[2:].zfill(5) + bin(Operation)[2:].zfill(1) + bin(Data_Byte_Count)[2:].zfill(2) + bin(SlaveAddress)[2:].zfill(16)+ bin(Data)[2:].zfill(32))

    print(binary_string)
    print(hex(int(binary_string,2)))
    return int(binary_string, 2)

def convertToCurrent(adcCount, rSense, senseList):
    ADC_Vref     = 4.096
    ADA4941_Vref = 2
    LTC6101_Rin  = 100
    LTC6101_Rout = 1500
    ADC_Bits     = 12

    ADC_Voltage = adcCount * 2 * ADC_Vref / (2 ** (ADC_Bits + 1))
    vSense = (ADC_Voltage + 2 * ADA4941_Vref) / 2
    senseList.append(vSense)
    current = (LTC6101_Rin * vSense / LTC6101_Rout) / rSense

    return current

'''DAC INIT STUFF'''
# Reads seek bit from AD9783
def getSeekValue(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, count):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]

    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, 0x06, 0x00)) # Get Seek Bit
    count += 1
    spi_rec_reg_out = decode(aterm, UUTReg.readRFRegister(count, mem_map.SPI_REC_FIFO_LO)) # Read FPGA SPI Rec Register
    SEEK_VALUE = hex(spi_rec_reg_out[5])
    SEEK_VALUE = bin(int(SEEK_VALUE,16))[2:]
    aterm.logMessage(1, "Procedure Ended")
    return SEEK_VALUE[-1]

def setSetAndHold(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, count, SET_VALUE,HLD_VALUE):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]

    WRITE = 0
    SET_HLD_VALUE = bin(SET_VALUE)[2:].zfill(4) + bin(HLD_VALUE)[2:].zfill(4)
    aterm.logMessage(1, 'SET_HLD_VALUE = ' + SET_HLD_VALUE)
    aterm.logMessage(1, 'SET_HLD_VALUE = ' + str(int(SET_HLD_VALUE,2)))
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x04, int(SET_HLD_VALUE, 2)))
    aterm.logMessage(1, "Procedure Ended")

''' Parse the SPI register read per HSID '''
def decode(aterm, hexString):
    aterm.logMessage(1, 'Received from SPI: ' + hexString)
    binString = bin(int(hexString[2:],16))[2:].zfill(64)
    aterm.logMessage(1, 'Received from SPI (int): ' + str(int(hexString[2:],16)))
    aterm.logMessage(1, 'Received from SPI (bin): ' + binString)
    Link_ID = binString[0:4]
    Device_ID = binString[4:8]
    Reserved = binString[8:14]
    Data_Byte_Count = binString[14:16]
    Slave_Address = binString[16:32]
    Data = binString[32:64]
    aterm.logMessage(1, 'Link_ID: ' + Link_ID + ', Device_ID: ' + Device_ID + ', Reserved: ' + Reserved + ', Data_Byte_Count: ' + Data_Byte_Count + ', Slave_Address: ' + Slave_Address + ', Data: ' + Data)
    return [int(Link_ID,2), int(Device_ID,2), int(Reserved,2), int(Data_Byte_Count,2), int(Slave_Address,2), int(Data,2)]

def initFreqDac(aterm):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]
    # SPI Control Values from HSID for AD9783
    LINK_ID = 2
    DEVICE_ID = 1
    READ = 1
    WRITE = 0
    DATA_BYTE_COUNT = 0
    count = 0

    ## Building the DAC Array Parameters
    SMP  = []
    SEEK = []
    SET  = []
    HLD  = []

    # Write SPI Values relating to DAC initialization.
    # Initialization Word 1
    # 0x0200 Write zeros to the Data control register to enable twos complement and
    # disable data clock output inversion. Not needed, Spare.
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x02, 0x00))

    # Initialization Word 2
    # 0x0A05 Enable mix mode on both DACs through the Mix Mode register.
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x0A, 0x05))

    # Initialization Word 3
    # 0x0BFF DAC1 full-scale 10-bit adjustment word. LSBs.
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x0B, 0xFF))

    # Initialization Word 4
    # 0x0C03 DAC1 full-scale 10-bit adjustment word. MSBs.
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x0C, 0x03))

    # Initialization Word 5
    # 0x0FFF DAC2 full-scale 10-bit adjustment word. LSBs.
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x0F, 0xFF))

    # Initialization Word 6
    # 0x1003 DAC2 full-scale 10-bit adjustment word. MSBs.
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x10, 0x03))

    # Initialization Word 7
    # 0x0330 Power-Down register of DAC. Power down AUX1 and AUX2 only.
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x03, 0x30))

    # Initialization Word 8
    # 0x0200 Write zeros to the Data control register to enable twos complement and
    # disable data clock output inversion. Not needed, Spare.
    count += 1
    UUTReg.writeRFRegister(count,
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x02, 0x00))
    count += 1


    ## Set the value of SET and HLD to 0.
    SET_VALUE = 0
    HLD_VALUE = 0
    setSetAndHold(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, count, SET_VALUE, HLD_VALUE) # Write new value to SET Register (0x04, 7:4) and write 0 HLD Register (0x04,3:0).

    SMP_VALUE = -1

    while (SMP_VALUE <= 31):

        ## Increment SMP and record the value of the SEEK bit.
        SMP_VALUE += 1
        count += 1
        UUTReg.writeRFRegister(count,
                                     mem_map.SPI_COM_FIFO_LO,
                                     genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x05, SMP_VALUE)) # write SMP value
        count += 1
        SEEK_VALUE = getSeekValue(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, count) # Get seek value
        # record the SEEK value
        SEEK.append(SEEK_VALUE)
        lastSeekValue = SEEK_VALUE
        aterm.logMessage(1, '   SMP Time: ' + str(SMP_VALUE)+ ', SEEK BIT: ' + SEEK_VALUE + ", SET Time: " + str(SET_VALUE) + ", HLD Time: " + str(HLD_VALUE))

        # record the HLD value
        HLD.append(HLD_VALUE)

        # record the SEt value
        SET.append(SET_VALUE)


    ## Print the Array
    for i in range(32):
        aterm.logMessage(1, 'SMP: ' + str(i) + ', SEEK: ' + str(SEEK[i]) + ', SET: ' + \
                str(SET[i]) + ', HLD: ' + str(HLD[i]))

    ## Determine the SMP Value
    ## Find SMP values that corresponds to the 0-to-1 transition of the SEEK bit in the table.
    StartIndex = []
    EndIndex   = []
    IndexRange = []
    # npSeek = np.array(SEEK)
    # seekHigh = np.where(npSeek == 1)


    # if seekHigh.size:
    #     print("Elements with value 1 exists at following indices", seekHigh[0], sep='\n')


    start_index = 0
    end_index = 0
    while end_index <= len(SEEK):
        SeekBitGoesHigh = 0
        for start_index in range(end_index, len(SEEK) - 1):
            if SEEK[start_index] == '1':
                SeekBitGoesHigh = 1
                break
        if SeekBitGoesHigh == 1:
            aterm.logMessage(1, 'start_index = ' + str(start_index))
            for end_index in range(start_index + 1, len(SEEK) - 1):
                if SEEK[end_index + 1] == '0':
                    break
            aterm.logMessage(1, 'end_index = ' + str(end_index))
            IndexRange.append(end_index - start_index)
            StartIndex.append(start_index)
            EndIndex.append(end_index)
            aterm.logMessage(1, 'Length of IndexRange = ' + str(IndexRange[-1]))
        end_index += 1


    ## Print the index range
    for i in range(0, len(IndexRange)):
        aterm.logMessage(1, 'IndexRange: ' + str(IndexRange[i]) + ', StartIndex: ' +  str(StartIndex[i]) + ', EndIndex: ' + str(EndIndex[i]))

    if len(IndexRange) > 0:
        max_range = 0
        max_range_index = 0
        for r_index in range(0, len(IndexRange)):
            I_range = IndexRange[r_index]
            if I_range > max_range:
                max_range = I_range
                max_range_index = r_index

        start_index = StartIndex[max_range_index]
        end_index = EndIndex[max_range_index]
        aterm.logMessage(1, 'Best SMP Range: ' + str(start_index) + " - " + str(end_index))

        SMP_Optimal = int(round((end_index + start_index) / 2, 0))
        aterm.logMessage(1, "Optimal SMP Determined: " + str(SMP_Optimal))

        #Check for timing margin
        aterm.logMessage(1, "Configuration Being Verified for Margin:")
        SET_VALUE = 0
        HLD_VALUE = 0
        count += 1
        setSetAndHold(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, count, SET_VALUE, HLD_VALUE) # Write new value to SET Register (0x04, 7:4) and write 0 HLD Register (0x04, 3:0).
        SMP_VALUE = SMP_Optimal - 1
        count += 1
        UUTReg.writeRFRegister(count,
                                     mem_map.SPI_COM_FIFO_LO,
                                     genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x05, SMP_VALUE)) # write SMP value
        count += 1
        SEEK_VALUE = getSeekValue(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, count) # Get seek value
        if SEEK_VALUE == '1':
            aterm.logMessage(1, "   Low-side SMP(" + str(SMP_VALUE) + ") Passed.")
        else:
            aterm.logMessage(1, "   Low-side SMP(" + str(SMP_VALUE) + ") Failed!")

        SMP_VALUE = SMP_Optimal + 1
        count += 1
        UUTReg.writeRFRegister(count,
                                     mem_map.SPI_COM_FIFO_LO,
                                     genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x05, SMP_VALUE)) # write SMP value
        count += 1
        SEEK_VALUE = getSeekValue(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, count) # Get seek value
        if SEEK_VALUE == '1':
            aterm.logMessage(1, "   High-side SMP (" + str(SMP_VALUE) + ") Passed.")
        else:
            aterm.logMessage(1, "   High-side SMP (" + str(SMP_VALUE) + ") Failed!")

        # Write optimal value
        SMP_VALUE = SMP_Optimal
        count += 1
        UUTReg.writeRFRegister(count,
                                     mem_map.SPI_COM_FIFO_LO,
                                     genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x05, SMP_VALUE)) # write SMP value
        aterm.logMessage(1, "Optimal SMP (" + str(SMP_Optimal) + ") Written to DAC.")
        aterm.logMessage(1, "Procedure Ended")
        return SMP_Optimal

    else:
        aterm.logMessage(1, "SMP Determination Failed. Seek does not transition to 1.")
        # Write 0 value
        SMP_VALUE = 0
        count += 1
        UUTReg.writeRFRegister(count,
                                     mem_map.SPI_COM_FIFO_LO,
                                     genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x05, SMP_VALUE)) # write SMP value
        aterm.logMessage(1, "Default SMP (" + str(SMP_VALUE) + ") Written to DAC.")
        aterm.logMessage(1, "Procedure Ended")
        return SMP_VALUE

''' Write observation value '''
def setBusNumber(aterm, RF_OBS_SEL):
    UUTReg = aterm.instruments["UUTReg"]
    if (RF_OBS_SEL < 0 or RF_OBS_SEL > 31):
        raise ValueError("Illegal Observation Signal number. Acceptable range is 0-31")
    count = 0
    UUTReg.writeRFRegister(count, mem_map.DBG_BIT_RF_OBS_SEL, RF_OBS_SEL)


''' TX Frequency DAC BIST functions '''
def runRandomDACBist(aterm, N):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]
    LINK_ID = 2
    DEVICE_ID = 1
    WRITE = 0
    # READ = 1
    DATA_BYTE_COUNT = 0

    today = datetime.today()
    todayDate = today.strftime("%b-%d-%Y--%H-%M-%S")
    newFile = "C:\\Test Data\\TXD RF\\AD9783 BIST Results\\BIST_" + todayDate + ".csv"

    # Disable twos-compliment on the DAC:
    count = 1
    UUTReg.writeRFRegister(count, 
                                 mem_map.SPI_COM_FIFO_LO, 
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x02, 0x80))
    MaxValue = (math.pow(2, 16) - 1) # 16-bit ADC
    Iteration = []
    E1Difference = []
    E2Difference = []
    e1_list = []
    e2_list = []
    Failure = []
    for n in range(0, N):
        e1_sent = random.randint(0, MaxValue)
        e2_sent = random.randint(0, MaxValue)
        clear_bist(aterm)
        enable_bist(aterm)
        send_pattern(aterm, e1_sent, e2_sent)
        (e2, e1) = read_pattern(aterm)
        # Undo bit flip of AD9783 BIST function
        e1 = int(MaxValue - e1)
        e2 = int(MaxValue - e2)
        Iteration.append(n)
        e1_list.append(e1)
        e2_list.append(e2)
        E1Difference.append(e1_sent - e1)
        E2Difference.append(e2_sent - e2)
        Failure.append(E1Difference[-1] > 0 or E2Difference[-1] > 0)
        aterm.logMessage(1, 'Iteration: ' + str(Iteration[-1]) + ':')
        aterm.logMessage(1, '    e1_sent = ' + hex(e1_sent) + ', Bist e1 = ' + hex(e1) + ' Error = ' + hex(E1Difference[-1]))
        aterm.logMessage(1, '    e2_sent = ' + hex(e2_sent) + ', Bist e2 = ' + hex(e2) + ' Error = ' + hex(E2Difference[-1]))
        aterm.logMessage(1, '    Total Words with Errors in it so far: ' + str(sum(Failure)))
    with open(newFile,'w', newline='') as csvfile:
        writer = csv.writer(csvfile, delimiter = ',')
        rows = zip(Iteration, e1_list, e2_list, E1Difference, E2Difference, Failure)
        writer.writerows(rows)

    csvfile.close()
    failureRate = 100 * sum(Failure) / N
    # Enable twos-compliment on the DAC:
    count += 1
    UUTReg.writeRFRegister(count, 
                                 mem_map.SPI_COM_FIFO_LO, 
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, 0x02, 0x00))
    aterm.logMessage(1, 'Done with DAC BIST (Using Random Values). Word Error Rate = ' + str(failureRate) + '%')
    aterm.logMessage(1, "Procedure Ended")
    return failureRate

def enable_bist(aterm):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]
    LINK_ID = 2
    DEVICE_ID = 1
    WRITE = 0
    # READ = 1
    DATA_BYTE_COUNT = 0
    aterm.logMessage(1, "enable:")
    # enable BIST function
    SLAVE_ADDRESS = 0x1A
    DATA = 0x80
    UUTReg.writeRFRegister(3, 
                                 mem_map.SPI_COM_FIFO_LO,
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, DATA))
    aterm.logMessage(1, "end of enable")
    aterm.logMessage(1, "Procedure Ended")

def clear_bist(aterm):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]
    LINK_ID = 2
    DEVICE_ID = 1
    WRITE = 0
    # READ = 1
    DATA_BYTE_COUNT = 0
    SLAVE_ADDRESS = 0x1A
    aterm.logMessage(1, "clear")
    # per datasheet, for BIST function write 0x20 and 0x00 to clear BIST function
    DATA = 0x20
    UUTReg.writeRFRegister(1,
                                 mem_map.SPI_COM_FIFO_LO, 
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, DATA))
    SLAVE_ADDRESS = 0x1A
    DATA = 0x00
    UUTReg.writeRFRegister(2,
                                 mem_map.SPI_COM_FIFO_LO, 
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, DATA))
    aterm.logMessage(1, "end of clear")
    aterm.logMessage(1, "Procedure Ended")


def send_pattern(aterm, DAC_E1_PATTERN, DAC_E2_PATTERN):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]
    DAC_PATTERN_COUNT = 1
    DAC_DATA_WORD = DAC_E1_PATTERN
    DAC_DATA_WORD |= DAC_E2_PATTERN << 16
    DAC_DATA_WORD |= DAC_PATTERN_COUNT << 32
    DAC_DATA_WORD |= 1 << 48 # enable
    aterm.logMessage(1, "*****DAC DATA WORD *****")
    aterm.logMessage(1, hex(DAC_DATA_WORD))
    UUTReg.writeRFRegister(6, mem_map.DDC_DAC_TST_REG, DAC_DATA_WORD)
    # DAC_DATA_WORD |= 1 << 48 # enable
    # write(6, DDC_DAC_TST_REG, DAC_DATA_WORD)
    aterm.logMessage(1, "Procedure Ended")

def read_pattern(aterm):
    aterm.logMessage(1, "Procedure Started")
    UUTReg = aterm.instruments["UUTReg"]
    LINK_ID = 2
    DEVICE_ID = 1
    WRITE = 0
    READ = 1
    DATA_BYTE_COUNT = 0
    DATA = 0xC0
    SLAVE_ADDRESS = 0x1A
    UUTReg.writeRFRegister(7, 
                                 mem_map.SPI_COM_FIFO_LO, 
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT, SLAVE_ADDRESS, DATA))
    
    # rising edge data
    RISING_EDGE_DATA = 0
    DATA = 0
    SLAVE_ADDRESS = 0x1B
    UUTReg.writeRFRegister(8, 
                                 mem_map.SPI_COM_FIFO_LO, 
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, SLAVE_ADDRESS, DATA))
    RISING_EDGE_DATA |= int(UUTReg.readRFRegister(8, mem_map.SPI_REC_FIFO_LO)[10:], 16)
    SLAVE_ADDRESS = 0x1C
    UUTReg.writeRFRegister(9, 
                                 mem_map.SPI_COM_FIFO_LO, 
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, SLAVE_ADDRESS, DATA))
    RISING_EDGE_DATA |= int(UUTReg.readRFRegister(9, mem_map.SPI_REC_FIFO_LO)[10:], 16) << 8

    # falling edge data
    FALLING_EDGE_DATA = 0
    SLAVE_ADDRESS = 0x1D
    UUTReg.writeRFRegister(10, 
                                 mem_map.SPI_COM_FIFO_LO, 
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, SLAVE_ADDRESS, DATA))
    FALLING_EDGE_DATA |= int(UUTReg.readRFRegister(10, mem_map.SPI_REC_FIFO_LO)[10:], 16)
    SLAVE_ADDRESS = 0x1E
    UUTReg.writeRFRegister(11, 
                                 mem_map.SPI_COM_FIFO_LO, 
                                 genSPIQword(aterm, LINK_ID, DEVICE_ID, READ, DATA_BYTE_COUNT, SLAVE_ADDRESS, DATA))
    FALLING_EDGE_DATA |= int(UUTReg.readRFRegister(11, mem_map.SPI_REC_FIFO_LO)[10:], 16) << 8
    aterm.logMessage(1, "Procedure Ended")
    return (RISING_EDGE_DATA, FALLING_EDGE_DATA)
