# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Pulse Decoder Characterics, Section *******
			 
			 Step5: Pulse Duration Tolerances, Mode A/C Interrogations
             Vary the P1 duration within the required acceptance range (0.7-0.9usec),
             record minimum reply ratio.
             Vary the P1 duration to fall out of the accepatnce range, record 
             minimum reply ratio.
             Repeat previous two steps for P3.
             Perform tests above for ATCRBS ModeA and Mode C.
            
             
INPUTS:      RM, ATC, PathLoss
OUTPUTS:     ModeAS_Replies - array of reply rations for P1,P3 duration, min,max and out of bounds
             ModeA_Replies  - array of reply rations for P1,P3 duration, min,max and out of bounds
             ModeC_Replies  - array of reply rations for P1,P3 duration, min,max and out of bounds
HISTORY:

04/23/2020   MRS    Initial Release.
05/11/2020   MRS    Cleanup
03/03/2021   MRS    Updates for new Handlers and Lobster.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def vary_P1(atc,rm,duration):
    cmd = ":ATC:XPDR:PUL:P1WIDTH " + str(duration)
    print("P1Width Cmd: ",cmd)
    rm.logMessage(0,"Test_2_3_2_5_Step5" + cmd)   
    atc.gwrite(cmd)
    time.sleep(10)
    atc.waitforstatus()
    
    replyrate = atc.getPercentReply(1)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    
    print("Reply Rate: ",replyrate)
    
    rmax = max(replyrate)     #get the max reply rate
    return rmax
    
def vary_P3(atc,rm,duration):
    cmd = ":ATC:XPDR:PUL:P3WIDTH " + str(duration)
    print("P3Width Cmd: ",cmd)
    rm.logMessage(0,"Test_2_3_2_5_Step5" + cmd)   
    atc.gwrite(cmd)
    time.sleep(10)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(1)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1

    print("Reply Rate: ",replyrate)
    
    rmax = max(replyrate)    #get the max reply rate
    return rmax

##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_5_Step5(rm, atc,PathLoss):
    
    rm.logMessage(2,"*** DO-181E, Pulse Decoder Characterics: Sect *******_Step5 ***")
    
    
    #Results read by TestStand
    ModeAS_Replies = [0.0,0.0,0.0,0.0,0.0,0.0]                                #Values read by TestStand
    ModeC_Replies = [0.0,0.0,0.0,0.0,0.0,0.0]                                #Values read by TestStand
    ModeA_Replies = [0.0,0.0,0.0,0.0,0.0,0.0]                                #Values read by TestStand
    
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     
    rm.logMessage(0,"Test_2_3_2_5_Step5a - Mode A")   
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    atc.waitforstatus()
    
    #Vary P1
    ModeA_Replies[0] = vary_P1(atc,rm,0.7)    
    ModeA_Replies[1] = vary_P1(atc,rm,0.9)    
    ModeA_Replies[2] = vary_P1(atc,rm,1.5)
    atc.gwrite(":ATC:XPDR:PUL:P1WIDTH 0.8")    #restore P1
    time.sleep(1)
    #Vary P3
    ModeA_Replies[3] = vary_P3(atc,rm,0.7)    
    ModeA_Replies[4] = vary_P3(atc,rm,0.9)    
    ModeA_Replies[5] = vary_P3(atc,rm,1.5)      
    atc.gwrite(":ATC:XPDR:PUL:P3WIDTH 0.8")    #restore P3
    time.sleep(1)

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    rm.logMessage(0,"Test_2_3_2_5_Step5a - Done")    

    #Set Up Transponder -- MODE C
    atc.transponderModeC()
    rm.logMessage(0,"Test_2_3_2_5_Step5b - Mode C")   
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    atc.waitforstatus()
    
    #Vary P1
    ModeC_Replies[0] = vary_P1(atc,rm,0.7)    
    ModeC_Replies[1] = vary_P1(atc,rm,0.9)    
    ModeC_Replies[2] = vary_P1(atc,rm,1.5)    
    atc.gwrite(":ATC:XPDR:PUL:P1WIDTH 0.8")    #restore P1
    #Vary P3
    ModeC_Replies[3] = vary_P3(atc,rm,0.7)    
    ModeC_Replies[4] = vary_P3(atc,rm,0.9)    
    ModeC_Replies[5] = vary_P3(atc,rm,1.5)      
    atc.gwrite(":ATC:XPDR:PUL:P3WIDTH 0.8")    #restore P3

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    rm.logMessage(0,"Test_2_3_2_5_Step5b - Done")    
    
    #Set Up Transponder -- MODE AS
    atc.transponderModeAS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     
    rm.logMessage(0,"Test_2_3_2_5_Step5c - Mode AS")   
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    atc.waitforstatus()
    
    #Vary P1
    ModeAS_Replies[0] = vary_P1(atc,rm,0.7)    
    ModeAS_Replies[1] = vary_P1(atc,rm,0.9)    
    ModeAS_Replies[2] = vary_P1(atc,rm,1.5)    
    atc.gwrite(":ATC:XPDR:PUL:P1WIDTH 0.8")    #restore P1
    #Vary P3
    ModeAS_Replies[3] = vary_P3(atc,rm,0.7)    
    ModeAS_Replies[4] = vary_P3(atc,rm,0.9)    
    ModeAS_Replies[5] = vary_P3(atc,rm,1.5)      
    atc.gwrite(":ATC:XPDR:PUL:P3WIDTH 0.8")    #restore P3

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    rm.logMessage(0,"Test_2_3_2_5_Step5c - Done")    
    
    rm.logMessage(0,"Test_2_3_2_5_Step5 - Done")   

    rm.logMessage(2,"Done, closing session")
    
    return ModeA_Replies + ModeC_Replies + ModeAS_Replies
##########################################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

     
    res = Test_2_3_2_5_Step5(rm,atc_obj,12.0)
    
    atc_obj.close()

