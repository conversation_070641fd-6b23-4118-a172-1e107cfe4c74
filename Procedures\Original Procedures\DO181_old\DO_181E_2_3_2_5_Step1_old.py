# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Pulse Decoder Characterics, Section *******
             
             Step1: Pulse Level Tolerances, ATCRBS ModeA/ModeS All-Call.
             Use a ATCRBS Mode A interrogation followed by a 1.6usec P4 pulse
             in its nominal position.  Vary the level of the P4 pulse between
             -10 and 0 dB in 1 dB steps with respect to  P3.  Verify the 
             changeover from ATCRBS to Mode S replies at the relative P4 levels 
             specified in *******.1.a and *******.1.b when more than 90% of 
             replies are Mode S or ATCRBS.
             
             
             
INPUTS:      RM, ATC, PathLoss
OUTPUTS:     ChangOver_Pwr - Change Over P4 Power Level,

HISTORY:

04/23/2020   MRS    Initial Release.
05/11/2020   MRS    Cleanup
03/03/2021   MRS    Updates for new Handlers and Lobster.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_5_Step1(rm, atc,PathLoss):
    
    rm.logMessage(2,"*** DO-181E, Pulse Decoder Characterics: Sect *******_Step1 ***")
    
    
    #Results read by TestStand
    ChangOver_Pwr = 0.0                    # Change Over P4 Power Level,
    
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE A/S -All Call
    atc.transponderModeAS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     
       
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    atc.waitforstatus()
    

    P4_Power = -10
    for i in range(10):
        #set the P4 power level
        cmd = ":ATC:XPDR:PUL:P4POWER " + str(P4_Power)
        print("CMD: ",cmd)
        atc.gwrite(cmd)
        time.sleep(10)
        atc.waitforstatus()
        
        replyrate = atc.getPercentReply(2)
        # fix for erroneous reply rate
        count = 0
        while replyrate[1] == -1.0 and count < 10:
            replyrate = atc.getPercentReply(2)
            count = count + 1

        msg = "Pwr: " + str(P4_Power) + " Reply Rate: " + str(replyrate)
        rm.logMessage(0,("Test_2_3_2_5_Step1" + msg))   
        
        #check for ModeA to ModeS changeover
        if ( float(replyrate[3]) > 70.0):            #ModeS > 70%
            break
                    
        P4_Power = P4_Power + 1
     
    #Return Last Power Level and % Replies    
    ChangOver_Pwr = P4_Power
    rm.logMessage(0,("Results: " + str(ChangOver_Pwr) + str(replyrate)))    

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    rm.logMessage(0,"Test_2_3_2_5_Step1 - Done")    

    
    rm.logMessage(2,"Done, closing session")

    
    return ChangOver_Pwr

##########################################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

     
    res = Test_2_3_2_5_Step1(rm,atc_obj,12.0)
    
    atc_obj.close()

