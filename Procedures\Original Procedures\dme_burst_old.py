# json encoder python
import json
import os
import time
import glob
import socket

# DME_SEQ_TYPE Values
NOP_VALUE =	0
SQTTR_LIST_CMND_VALUE = 0
IDENT_LIST_CMND_VALUE = 1
INT_REPLY_CMND_VALUE = 2
SPACER_CMND_VALUE = 3

# X/Y receive channel
X_CHANNEL = 1
Y_CHANNEL = 0

#RX attenuator control values
APU_ATTEN_CTRL_VALUE_W_HIGH_ATTEN = 7
APU_ATTEN_CTRL_VALUE_W_LOW_ATTEN = 3
RPU_ATTEN_CTRL_VALUE = 2
RFFPGA_ATTEN_CTRL_VALUE = 4

def dme_transmit(aterm, requestId, frequency, dmeBurstNumber):
    aterm.logMessage(1, "Procedure Started")
    (channel) = decodeDmeStation(frequency)

    if channel < 1 or channel > 126:
        print("incorrect frequency, out of range")
        return -1

    r = {
        "dme_request": {
            "requestId": requestId,
            "Command": INT_REPLY_CMND_VALUE,
            "Channel": channel,
            "X_Y_Channel": X_CHANNEL,
            "Rx_Attn_Ctrl": APU_ATTEN_CTRL_VALUE_W_LOW_ATTEN,
            "Tracked_Amplitude": 0,
            "Tx_If_Sl_Ok": 0,
            "Large_Jitter": 0,
            "Optional": 0,
            "Audio_Cease": 0,
            "Audio_Toggle": 0,
            "Back_To_Back_Tx": 1,
            "Noise_Threshold_Rx": 0,
            "Noise_Threshold_Rx_Valid": 0,
            "Noise_Threshold_Tx": 0,
            "Noise_Threshold_Tx_Valid": 0,
            "dmeBurstNumber": dmeBurstNumber
        }
    }
    UDP_IP = "************"
    UDP_PORT = 5121

    sock = socket.socket(socket.AF_INET, # Internet
                      socket.SOCK_DGRAM) # UDP

    sock.bind((UDP_IP, UDP_PORT))
    
    r = str.encode(json.dumps(r))
    sock.sendto(r,('*************',UDP_PORT))
    sock.close()
    aterm.logMessage(1, "Procedure Ended")
    return 0

def dme_transmit_old(aterm, requestId, frequency, dmeBurstNumber):
    aterm.logMessage(1, "Procedure Started")
    (channel) = decodeDmeStation(frequency)

    if channel < 1 or channel > 126:
        print("incorrect frequency, out of range")
        return -1

    os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\request")

    for removable_file in glob.glob("*.json_processed"):
        os.remove(removable_file)

    data = {
        "dme_request": {
            "requestId": requestId,
            "Command": INT_REPLY_CMND_VALUE,
            "Channel": channel,
            "X_Y_Channel": X_CHANNEL,
            "Rx_Attn_Ctrl": APU_ATTEN_CTRL_VALUE_W_LOW_ATTEN,
            "Tracked_Amplitude": 0,
            "Tx_If_Sl_Ok": 0,
            "Large_Jitter": 0,
            "Optional": 0,
            "Audio_Cease": 0,
            "Audio_Toggle": 0,
            "Back_To_Back_Tx": 1,
            "Noise_Threshold_Rx": 0,
            "Noise_Threshold_Rx_Valid": 0,
            "Noise_Threshold_Tx": 0,
            "Noise_Threshold_Tx_Valid": 0,
            "dmeBurstNumber": dmeBurstNumber
        }
    }

    filename = "dme_msg_" + str(requestId) + ".json"
    with open(filename, "w") as write_file:
        json.dump(data, write_file)
    processed_file = (glob.glob("dme_msg_" + str(requestId) + "_*.json_processed"))
    count_limit = 50 # 5 second timer
    while ((len(processed_file) == 0) and (count_limit != 0)):
        processed_file = (glob.glob("dme_msg_" + str(requestId) + "_*.json_processed"))
        time.sleep(0.1)
        count_limit -= 1
    if count_limit == 0:
        print("dme_api ---> error somewhere in request")
        return -1
    aterm.logMessage(1, "Procedure Ended")
    return 0

def decodeDmeStation(frequency):
    return frequency - 1024
