# -*- coding: utf-8 -*-
"""
Created on 3/25/2020

@author: H118396
         <PERSON>
         CNS QUALIFICATION TEST GROUP
         
DESCRIPTION: Measures Mode S Transmit Frequency per DO185B section *******

            The transmission frequency shall be 1030 ±0.01 MHz.

INPUTS: Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Spectrum Analyzer
OUTPUTS: Frequency (From 'measureFreq' routine) 
         and Limit Pass/Fail (from SpecAnz Limit test)

HISTORY:
05/12/2021  MRS  Added main routine, test for limits and coments. 
                 Updates for Lobster.


"""


#Required Libraries
import time
import math

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers.RGS2000NG import RGS2000NG  
from TXDLib.Handlers import N9010BSpecAn 



##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def createLimit(spec_obj):
    """ Creates Limits for the SpecAnz based on DO385 Transmit requirements."""
    limit = [
        "-9.000000000E+07", "-5.000000000E+01", "0.000000000E+00",
        "-6.000000000E+07", "-5.000000000E+01", "1.000000000E+00",
        "-6.000000000E+07", "-4.700000000E+01", "1.000000000E+00",
        "-5.000000000E+07", "-4.700000000E+01", "1.000000000E+00",
        "-5.000000000E+07", "-4.300000000E+01", "1.000000000E+00",
        "-4.000000000E+07", "-4.300000000E+01", "1.000000000E+00",
        "-4.000000000E+07", "-3.900000000E+01", "1.000000000E+00",
        "-3.000000000E+07", "-3.900000000E+01", "1.000000000E+00",
        "-3.000000000E+07", "-3.100000000E+01", "1.000000000E+00",
        "-2.000000000E+07", "-3.100000000E+01", "1.000000000E+00",
        "-2.000000000E+07", "-1.900000000E+01", "1.000000000E+00",
        "-1.000000000E+07", "-1.900000000E+01", "1.000000000E+00",
        "-1.000000000E+07", "-1.500000000E+01", "1.000000000E+00",
        "-8.000000000E+06", "-1.500000000E+01", "1.000000000E+00",
        "-8.000000000E+06", "-1.100000000E+01", "1.000000000E+00",
        "-6.000000000E+06", "-1.100000000E+01", "1.000000000E+00",
        "-6.000000000E+06", "-6.000000000E+00", "1.000000000E+00",
        "-4.000000000E+06", "-6.000000000E+00", "1.000000000E+00",
        "-4.000000000E+06", "0.100000000E+00", "1.000000000E+00",
        "4.000000000E+06", "0.010000000E+00", "1.000000000E+00",
        "4.000000000E+06", "-6.000000000E+00", "1.000000000E+00",
        "6.000000000E+06", "-6.000000000E+00", "1.000000000E+00",
        "6.000000000E+06", "-1.100000000E+01", "1.000000000E+00",
        "8.000000000E+06", "-1.100000000E+01", "1.000000000E+00",
        "8.000000000E+06", "-1.500000000E+01", "1.000000000E+00",
        "1.000000000E+07", "-1.500000000E+01", "1.000000000E+00",
        "1.000000000E+07", "-1.900000000E+01", "1.000000000E+00",
        "2.000000000E+07", "-1.900000000E+01", "1.000000000E+00",
        "2.000000000E+07", "-3.100000000E+01", "1.000000000E+00",
        "3.000000000E+07", "-3.100000000E+01", "1.000000000E+00",
        "3.000000000E+07", "-3.900000000E+01", "1.000000000E+00",
        "4.000000000E+07", "-3.900000000E+01", "1.000000000E+00",
        "4.000000000E+07", "-4.300000000E+01", "1.000000000E+00",
        "5.000000000E+07", "-4.300000000E+01", "1.000000000E+00",
        "5.000000000E+07", "-4.700000000E+01", "1.000000000E+00",
        "6.000000000E+07", "-4.700000000E+01", "1.000000000E+00",
        "6.000000000E+07", "-5.000000000E+01", "1.000000000E+00",
        "9.000000000E+07", "-5.000000000E+01", "1.000000000E+00",
        ]
    spec_obj.LimitSetLine(limit)
    time.sleep(1)

def re_init_specAn(specAn_obj):
    specAn_obj.Reset()
    specAn_obj.SweepContSet("ON")

def specAnSetup(spec_obj):
    spec_obj.CenterFreqSet(1030, "MHz")
    spec_obj.SpanSet(80, "MHz")
    spec_obj.ResBandwidthSet(100, "kHz")
    spec_obj.SweepTimeSet(10, "s")
    
    # Set to single sweep capture
    time.sleep(0.5)
    spec_obj.SweepContSet("OFF")
    time.sleep(10)

    # Zoom in and get Peak at 1030MHz
    spec_obj.SpanSet(5, "MHz")
    ref_level = float(spec_obj.GetMaxPeakPower())
    #ref_level = -40.0
    spec_obj.setRefLevel(ref_level, "dBm")

    # Zoom out
    spec_obj.SpanSet(80, "MHz")
    time.sleep(.5)


def passFail(spec_obj):
    """ Returns the result of the Spec Anz Limit Test. """
    res = spec_obj.LimitResult()    #returns a boolean

    if res: val = 1.0
    else: val = 0.0

    return val


def re_init_specAn(specAn_obj):
    """ Initialize the Spectrum Analyzer. """

    specAn_obj.Reset()
    specAn_obj.SweepContSet("ON")
    # Ensure time to Sweep
    time.sleep(2)

def setupSpecAn(specAn_obj):
    """ Set up the Spec Anz to 1030 MHz, 5MHz span, MaxHold.  """
    specAn_obj.TraceTypeSet("WRITe")
    specAn_obj.CenterFreqSet(1030, 'MHz')
    specAn_obj.SpanSet(5, 'mHz')
    #specAn_obj.ResBandwidthSet(1, "kHz")
    specAn_obj.TraceTypeSet('MAXHold')
    # Ensure time for Spec Sweep
    time.sleep(20)       #give it plenty of time to average traces


def measureFreq(specAn_obj):
    """ Get the Peak Frequency. """
    return float(specAn_obj.GetMaxPeakFreq())

def captureData(specAn_obj):
    """ Capture the Waveform Display data. """
    freq, pwr = specAn_obj.dataSpecFetch()
    container = [freq, pwr]
    return container

def plotData(specAn_obj):
    """ Plotting the Waveform data. """
    # Plotting currently does not function within Teststand
    freq, pwr = specAn_obj.dataSpecFetch()
    specAn_obj.plotSave("TXFreq", freq, pwr)
    
##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_2_3_5(rm,rgs,specAn_obj):
    """ DO385 2_2_3_5: Transmit Frequency. """

    rm.logMessage(0,"*Test_2.2.3..5: Transmit Frequency")  

    #init results
    Frq = -1.0
    Lmt = -1.0

    #Load ModeS Bearing Scenario (< +/- 10 deg)
    rm.logMessage(0,"*Test_2.2.3..5: Start Scenario")  
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('Brglt10ModeS.csv')
    time.sleep(5)
    # Start Scenario
    rgs.startScen()
    time.sleep(20)

    #SetUp SpecAnz and capture data
    rm.logMessage(0,"*Test_2.2.3..5: SetUp SpecAnz, Capture Data")  
    re_init_specAn(specAn_obj)
    specAnSetup(specAn_obj)
    createLimit(specAn_obj)
    Lmt = passFail(specAn_obj)
    Frq = measureFreq(specAn_obj)
    rm.logMessage(0,"Frq: " + str(Frq) + " Lmt: " + str(Lmt))
   
    #plot 
    #captureData(specAn_obj)
    #plotData(specAn_obj)


    rm.logMessage(0,"Test_2.2.3..5: Transmit Frequency, DONE.")  

    #Convert to MHz
    Frq = Frq * 10e-6
    return [Frq,Lmt]
   
    

    

#run as main from command line
if __name__ == "__main__":

    #SetUP Resource Manager
    rm = ate_rm()
    
  
    #Initiazlie the RGS
    rgs = RGS2000NG(rm)
	
	#Initialzie the Spectrum Analyzer
    spanz_obj = N9010BSpecAn(rm)
    spanz_obj.Reset()

     
    res = Test_2_2_3_5(rm,rgs,spanz_obj)
    


    spanz_obj.close()
    rgs.close()
    rm.cleanup()