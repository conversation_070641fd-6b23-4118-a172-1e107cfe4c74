#!/usr/bin/env python3
"""
Mock RFBOB Handler for Testing
Simulates the behavior of the RF Breakout Box (RFBOB)
"""

import time
import random
from unittest.mock import Mock

class MockRFBOB:
    """Mock implementation of RFBOB handler"""
    
    def __init__(self, resource_manager=None):
        self.resourceManager = resource_manager or Mock()
        self.connected = False
        self.switch_states = {}
        self.attenuation_values = {}
        self.path_loss = 0.0
        
        # Initialize default switch states
        for i in range(1, 17):  # 16 switches
            self.switch_states[i] = "OPEN"
            
        # Initialize default attenuation values
        for i in range(1, 5):  # 4 attenuators
            self.attenuation_values[i] = 0.0
            
    def connect(self, address="USB0::0x1234::0x5678::INSTR"):
        """Simulate connection to RFBOB"""
        time.sleep(0.1)  # Simulate connection time
        self.connected = True
        self.resourceManager.logMessage(1, f"Mock RFBOB connected to {address}")
        return True
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        self.resourceManager.logMessage(1, "Mock RFBOB disconnected")
        
    def write(self, command):
        """Simulate writing command to RFBOB"""
        if not self.connected:
            raise Exception("RFBOB not connected")
            
        time.sleep(0.01)  # Simulate command processing
        self.resourceManager.logMessage(1, f"Mock RFBOB command: {command}")
        
        # Parse and simulate command effects
        if "SWITCH" in command and "CLOSE" in command:
            # Extract switch number
            parts = command.split()
            for part in parts:
                if part.isdigit():
                    switch_num = int(part)
                    if 1 <= switch_num <= 16:
                        self.switch_states[switch_num] = "CLOSE"
                        break
                        
        elif "SWITCH" in command and "OPEN" in command:
            # Extract switch number
            parts = command.split()
            for part in parts:
                if part.isdigit():
                    switch_num = int(part)
                    if 1 <= switch_num <= 16:
                        self.switch_states[switch_num] = "OPEN"
                        break
                        
        elif "ATTEN" in command:
            # Extract attenuator number and value
            parts = command.split()
            atten_num = None
            atten_val = None
            
            for i, part in enumerate(parts):
                if part.isdigit() and atten_num is None:
                    atten_num = int(part)
                elif "." in part or part.replace(".", "").isdigit():
                    atten_val = float(part)
                    
            if atten_num and atten_val is not None:
                if 1 <= atten_num <= 4:
                    self.attenuation_values[atten_num] = atten_val
                    
    def query(self, command):
        """Simulate querying RFBOB for data"""
        if not self.connected:
            raise Exception("RFBOB not connected")
            
        time.sleep(0.01)
        self.resourceManager.logMessage(1, f"Mock RFBOB query: {command}")
        
        # Simulate responses
        if "SWITCH" in command and "?" in command:
            # Return switch state
            parts = command.split()
            for part in parts:
                if part.replace("?", "").isdigit():
                    switch_num = int(part.replace("?", ""))
                    if 1 <= switch_num <= 16:
                        return self.switch_states[switch_num]
            return "OPEN"
            
        elif "ATTEN" in command and "?" in command:
            # Return attenuation value
            parts = command.split()
            for part in parts:
                if part.replace("?", "").isdigit():
                    atten_num = int(part.replace("?", ""))
                    if 1 <= atten_num <= 4:
                        return str(self.attenuation_values[atten_num])
            return "0.0"
            
        elif "IDN?" in command:
            return "Mock RFBOB,Model 1234,SN123456,FW1.0"
            
        return "OK"
        
    def setSwitch(self, switch_num, state):
        """Set switch state (OPEN/CLOSE)"""
        if 1 <= switch_num <= 16:
            self.switch_states[switch_num] = state
            self.write(f"SWITCH {switch_num} {state}")
            self.resourceManager.logMessage(1, f"Mock RFBOB Switch {switch_num} set to {state}")
        else:
            raise ValueError(f"Invalid switch number: {switch_num}")
            
    def getSwitch(self, switch_num):
        """Get switch state"""
        if 1 <= switch_num <= 16:
            return self.switch_states[switch_num]
        else:
            raise ValueError(f"Invalid switch number: {switch_num}")
            
    def setAttenuation(self, atten_num, value):
        """Set attenuation value in dB"""
        if 1 <= atten_num <= 4:
            if 0.0 <= value <= 31.75:  # Typical range for RF attenuators
                self.attenuation_values[atten_num] = value
                self.write(f"ATTEN {atten_num} {value}")
                self.resourceManager.logMessage(1, f"Mock RFBOB Attenuator {atten_num} set to {value} dB")
            else:
                raise ValueError(f"Attenuation value out of range: {value}")
        else:
            raise ValueError(f"Invalid attenuator number: {atten_num}")
            
    def getAttenuation(self, atten_num):
        """Get attenuation value"""
        if 1 <= atten_num <= 4:
            return self.attenuation_values[atten_num]
        else:
            raise ValueError(f"Invalid attenuator number: {atten_num}")
            
    def setPathLoss(self, loss_db):
        """Set path loss compensation"""
        self.path_loss = loss_db
        self.resourceManager.logMessage(1, f"Mock RFBOB Path loss set to {loss_db} dB")
        
    def getPathLoss(self):
        """Get path loss compensation"""
        return self.path_loss
        
    def reset(self):
        """Reset RFBOB to default state"""
        self.resourceManager.logMessage(1, "Mock RFBOB Reset")
        
        # Reset all switches to OPEN
        for i in range(1, 17):
            self.switch_states[i] = "OPEN"
            
        # Reset all attenuators to 0 dB
        for i in range(1, 5):
            self.attenuation_values[i] = 0.0
            
        self.path_loss = 0.0
        time.sleep(0.1)  # Simulate reset time
        
    def close(self):
        """Close connection to RFBOB"""
        self.disconnect()
