#!/usr/bin/env python3
"""
Mock UAT Connection for Testing
Simulates the behavior of the UAT (Universal Access Transceiver) connection
"""

import time
import random
from unittest.mock import Mock

class MockUATConnection:
    """Mock implementation of UAT connection"""
    
    def __init__(self):
        self.connected = False
        self.scenario_loaded = False
        self.scenario_running = False
        self.status = "IDLE"
        self.last_command = ""
        
        # Simulation parameters
        self.scenario_load_time = 0.1  # Simulated scenario load time
        self.command_delay = 0.01
        
    def connect(self, address, port=None):
        """Simulate connection to UAT"""
        time.sleep(0.1)
        self.connected = True
        print(f"Mock UAT connected to {address}")
        return True
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        self.scenario_loaded = False
        self.scenario_running = False
        print("Mock UAT disconnected")
        
    def send(self, command):
        """Simulate sending command to UAT"""
        if not self.connected:
            raise Exception("UAT not connected")
            
        time.sleep(self.command_delay)
        self.last_command = command
        print(f"Mock UAT command: {command}")
        
        # Simulate command responses
        if ":ATC:STATUS?" in command:
            if self.scenario_loaded and not self.scenario_running:
                return "READY"
            elif self.scenario_running:
                return "RUNNING"
            else:
                return "IDLE"
        else:
            return "OK"
            
    def scenario_load(self, scenario_file):
        """Simulate loading a scenario with optimized timing"""
        print(f"Mock UAT loading scenario: {scenario_file}")
        self.status = "LOADING"
        
        # Simulate optimized scenario loading (adaptive timing instead of fixed 50s)
        # In real implementation, this would poll for readiness
        load_time = random.uniform(0.05, 0.15)  # Simulate variable load time
        time.sleep(load_time)
        
        self.scenario_loaded = True
        self.status = "READY"
        print(f"Mock UAT scenario loaded in {load_time:.2f}s")
        return True
        
    def scenario_start(self, start=1):
        """Simulate starting a scenario with optimized timing"""
        if not self.scenario_loaded:
            raise Exception("No scenario loaded")
            
        print("Mock UAT starting scenario")
        self.status = "STARTING"
        
        # Simulate optimized scenario start (adaptive timing instead of fixed 30s)
        start_time = random.uniform(0.03, 0.08)  # Simulate variable start time
        time.sleep(start_time)
        
        self.scenario_running = True
        self.status = "RUNNING"
        print(f"Mock UAT scenario started in {start_time:.2f}s")
        return True
        
    def scenario_stop(self):
        """Simulate stopping a scenario"""
        print("Mock UAT stopping scenario")
        self.scenario_running = False
        self.status = "READY"
        return True
        
    def get_status(self):
        """Get current UAT status"""
        return self.status
        
    def is_scenario_ready(self):
        """Check if scenario is ready (used by optimized polling)"""
        return self.scenario_loaded and self.status == "READY"
        
    def is_scenario_running(self):
        """Check if scenario is running"""
        return self.scenario_running
        
    def wait_for_ready(self, timeout=60):
        """Wait for UAT to be ready (used by optimization polling)"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                status = self.send(":ATC:STATUS?\r\n")
                if status == "READY":
                    return True
                time.sleep(0.1)  # Poll every 100ms
            except:
                time.sleep(0.1)
                
        return False  # Timeout
        
    def simulate_scenario_loading_optimization(self):
        """
        Simulate the optimized scenario loading behavior
        This demonstrates the HIGH PRIORITY optimization in action
        """
        print("=== Simulating Optimized Scenario Loading ===")
        
        # Original behavior would be: time.sleep(50)
        # Optimized behavior: adaptive polling with status checking
        
        start_time = time.time()
        
        # Simulate scenario load command
        self.scenario_load("test_scenario.xml")
        
        # Simulate optimized polling (instead of fixed 50s delay)
        ready = self.wait_for_ready(timeout=60)
        
        elapsed_time = time.time() - start_time
        
        if ready:
            print(f"Scenario ready in {elapsed_time:.2f}s (vs 50s fixed delay)")
            print(f"Time savings: {50 - elapsed_time:.2f}s")
        else:
            print("Scenario loading timed out")
            
        return ready, elapsed_time
        
    def simulate_scenario_start_optimization(self):
        """
        Simulate the optimized scenario start behavior
        """
        print("=== Simulating Optimized Scenario Start ===")
        
        if not self.scenario_loaded:
            print("No scenario loaded")
            return False, 0
            
        start_time = time.time()
        
        # Start scenario
        self.scenario_start()
        
        # Simulate optimized polling (instead of fixed 30s delay)
        ready = self.wait_for_ready(timeout=40)
        
        elapsed_time = time.time() - start_time
        
        if ready:
            print(f"Scenario started in {elapsed_time:.2f}s (vs 30s fixed delay)")
            print(f"Time savings: {30 - elapsed_time:.2f}s")
        else:
            print("Scenario start timed out")
            
        return ready, elapsed_time


class MockUATClient:
    """Mock UAT Client wrapper (alternative interface)"""
    
    def __init__(self):
        self.connection = MockUATConnection()
        
    def connect(self, host, port):
        """Connect to UAT"""
        return self.connection.connect(f"{host}:{port}")
        
    def disconnect(self):
        """Disconnect from UAT"""
        self.connection.disconnect()
        
    def send_command(self, command):
        """Send command to UAT"""
        return self.connection.send(command)
        
    def load_scenario(self, scenario_path):
        """Load scenario file"""
        return self.connection.scenario_load(scenario_path)
        
    def start_scenario(self):
        """Start loaded scenario"""
        return self.connection.scenario_start()
        
    def stop_scenario(self):
        """Stop running scenario"""
        return self.connection.scenario_stop()
        
    def get_scenario_status(self):
        """Get scenario status"""
        return self.connection.get_status()


# Test function to demonstrate optimization benefits
def test_optimization_comparison():
    """
    Test function to compare original vs optimized timing
    """
    print("=== UAT Connection Optimization Test ===")
    
    uat = MockUATConnection()
    uat.connect("192.168.1.100")
    
    # Test scenario loading optimization
    ready, load_time = uat.simulate_scenario_loading_optimization()
    
    if ready:
        # Test scenario start optimization
        ready, start_time = uat.simulate_scenario_start_optimization()
        
        total_optimized_time = load_time + start_time
        total_original_time = 50 + 30  # Original fixed delays
        
        print(f"\n=== Optimization Summary ===")
        print(f"Original total time: {total_original_time}s")
        print(f"Optimized total time: {total_optimized_time:.2f}s")
        print(f"Total time savings: {total_original_time - total_optimized_time:.2f}s")
        print(f"Performance improvement: {((total_original_time - total_optimized_time) / total_original_time) * 100:.1f}%")
    
    uat.disconnect()


if __name__ == "__main__":
    test_optimization_comparison()
