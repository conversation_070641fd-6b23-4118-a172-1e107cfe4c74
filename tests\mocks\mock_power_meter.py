#!/usr/bin/env python3
"""
Mock B4500C Power Meter for Testing
Simulates the behavior of the real B4500C power meter
"""

import time
import random
import numpy as np
from unittest.mock import Mock

class MockB4500CPwrMeter:
    """Mock implementation of B4500C Power Meter"""
    
    def __init__(self, resource_manager=None):
        self.resourceManager = resource_manager or Mock()
        self.pwrmeterIP = "TCPIP0::************::INSTR"
        self.connected = False
        
        # Simulated measurement parameters
        self.frequency = 1030.0e6  # Hz
        self.power_level = -21.0   # dBm
        self.measurement_accuracy = 0.1  # ±0.1 dB
        
        # Storage arrays (simulated)
        self.t = np.arange(500, dtype=np.float)
        self.y = np.arange(500, dtype=np.float)
        self.s_time = np.arange(128, dtype=np.int)
        self.e_time = np.arange(128, dtype=np.int)
        self.npulse = 0
        
        # Timing simulation
        self.measurement_delay = 0.05  # Simulated measurement time
        self.setup_delay = 0.02       # Simulated setup time
        
        # Connect automatically
        self.connect()
        
    def connect(self):
        """Simulate connection to power meter"""
        time.sleep(0.1)
        self.connected = True
        self.basicTvlMsg("Mock Power Meter Resource Opened.")
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        self.basicTvlMsg("Mock Power Meter disconnected")
        
    def basicWrite(self, cmd):
        """Simulate writing command to power meter"""
        if not self.connected:
            raise Exception("Power meter not connected")
            
        time.sleep(self.setup_delay)
        self.basicTvlMsg(f"> {cmd}")
        
        # Simulate command effects
        if "FREQ" in cmd:
            try:
                freq_str = cmd.split()[-1]
                self.frequency = float(freq_str)
            except:
                pass
        elif "POW" in cmd or "LEVEL" in cmd:
            try:
                power_str = cmd.split()[-1]
                self.power_level = float(power_str)
            except:
                pass
                
    def basicQuery(self, cmd, logEnable=False):
        """Simulate querying power meter for data"""
        if not self.connected:
            raise Exception("Power meter not connected")
            
        time.sleep(self.measurement_delay)
        
        if logEnable:
            self.basicTvlMsg(f"> {cmd}")
            
        # Simulate responses based on command
        if "*IDN?" in cmd:
            response = "Agilent Technologies,B4500C,MY12345678,A.01.02"
        elif "FETCh1:ARRay:POWer?" in cmd:
            # Simulate power measurement with realistic variation
            base_power = self.power_level
            variation = random.uniform(-self.measurement_accuracy, self.measurement_accuracy)
            measured_power = base_power + variation
            response = f"{measured_power:.3f}"
        elif "FETCh1:ARRay:MARKer:POWer?" in cmd:
            # Simulate marker power measurements
            powers = []
            for i in range(6):  # Typical marker measurements
                base_power = self.power_level + random.uniform(-0.5, 0.5)
                variation = random.uniform(-self.measurement_accuracy, self.measurement_accuracy)
                powers.append(f"{base_power + variation:.3f}")
            response = ",".join(powers)
        elif "FETCh1:ARRay:AMEAsure:POWer?" in cmd:
            # Simulate array power measurements
            measurements = []
            for param in ["PulsePeak", "PulseCycleAvg", "PulseOnAvg", "IEEE_Top", "IEEE_Bot", "Overshoot"]:
                base_value = self.power_level + random.uniform(-1.0, 1.0)
                variation = random.uniform(-self.measurement_accuracy, self.measurement_accuracy)
                measurements.extend(["CC", f"{base_value + variation:.3f}"])
            response = ",".join(measurements)
        else:
            response = "OK"
            
        if logEnable:
            self.basicTvlMsg(f"< {response}")
            
        return response
        
    def basicTvlMsg(self, tvlTxt):
        """Log message through resource manager"""
        if self.resourceManager:
            self.resourceManager.logMessage(1, tvlTxt)
        else:
            print(f"Mock Power Meter: {tvlTxt}")
            
    def Ident(self):
        """Returns Instrument Ident"""
        return self.basicQuery("*IDN?")
        
    def close(self):
        """Close power meter connection"""
        self.disconnect()
        
    # Measurement methods with optimization simulation
    def fetch1_pwr(self):
        """Returns Ch1 power measurements with optimized timing"""
        # Simulate optimized measurement settling (reduced from 1s to 0.5s)
        time.sleep(0.02)  # Simulated optimized delay
        return self.basicQuery("FETCh1:ARRay:AMEAsure:POWer?")
        
    def CH2_Marker_Power(self):
        """Returns Ch2 marker power measurements"""
        time.sleep(0.02)
        power_str = self.basicQuery("FETCh1:ARRay:MARKer:POWer?")
        return [float(x) for x in power_str.split(",")]
        
    def getpwrmeasuremet(self):
        """Gets power measurements with optimized timing"""
        # Simulate optimized measurement timing
        time.sleep(0.02)  # Reduced from original delays
        return self.basicQuery("FETCh1:ARRay:MARKer:POWer?")
        
    def initiate_cont(self, cont_onoff):
        """Turns continuous measurements on/off"""
        self.basicWrite(f"INITiate:CONTinuous {str(cont_onoff)}")
        
    def simulate_measurement_optimization(self):
        """
        Simulate the MEDIUM PRIORITY measurement settling optimization
        """
        print("=== Simulating Power Meter Measurement Optimization ===")
        
        original_settling_time = 1.0  # Original measurement settling
        optimized_settling_time = 0.5  # Optimized settling
        
        print(f"Original measurement settling: {original_settling_time}s")
        print(f"Optimized measurement settling: {optimized_settling_time}s")
        
        # Simulate optimized measurement
        start_time = time.time()
        
        # Setup measurement
        self.basicWrite("FREQ 1030.0E6")
        self.basicWrite("POW:LEVEL -21")
        
        # Optimized settling time
        time.sleep(optimized_settling_time / 10)  # Scale for simulation
        
        # Take measurement
        power_result = self.fetch1_pwr()
        
        actual_time = time.time() - start_time
        time_savings = original_settling_time - optimized_settling_time
        
        print(f"Measurement completed in {actual_time:.3f}s")
        print(f"Time savings per measurement: {time_savings:.1f}s")
        print(f"Performance improvement: {(time_savings / original_settling_time) * 100:.1f}%")
        print(f"Power measurement result: {power_result}")
        
        return time_savings, actual_time
        
    def test_measurement_accuracy(self, num_measurements=20):
        """
        Test measurement accuracy with optimized timing
        """
        print(f"=== Testing Measurement Accuracy ({num_measurements} samples) ===")
        
        measurements = []
        start_time = time.time()
        
        for i in range(num_measurements):
            # Set known power level
            test_power = -21.0 + random.uniform(-5.0, 5.0)
            self.power_level = test_power
            
            # Take measurement with optimized timing
            result_str = self.basicQuery("FETCh1:ARRay:POWer?")
            measured_power = float(result_str)
            
            measurements.append({
                'expected': test_power,
                'measured': measured_power,
                'error': abs(measured_power - test_power)
            })
            
            # Optimized measurement interval
            time.sleep(0.01)
            
        test_time = time.time() - start_time
        
        # Analyze accuracy
        errors = [m['error'] for m in measurements]
        avg_error = sum(errors) / len(errors)
        max_error = max(errors)
        
        within_spec = sum(1 for e in errors if e <= self.measurement_accuracy)
        accuracy_percentage = (within_spec / len(errors)) * 100
        
        print(f"Test completed in {test_time:.2f}s")
        print(f"Average error: {avg_error:.3f} dB")
        print(f"Maximum error: {max_error:.3f} dB")
        print(f"Measurements within spec: {accuracy_percentage:.1f}%")
        print(f"Measurement rate: {num_measurements / test_time:.1f} measurements/second")
        
        return {
            'avg_error': avg_error,
            'max_error': max_error,
            'accuracy_percentage': accuracy_percentage,
            'measurement_rate': num_measurements / test_time
        }


# Test function to demonstrate optimization benefits
def test_power_meter_optimization():
    """
    Test function to compare original vs optimized power meter operations
    """
    print("=== Power Meter Optimization Test ===")
    
    # Create mock resource manager
    mock_rm = Mock()
    
    # Create power meter instance
    pm = MockB4500CPwrMeter(mock_rm)
    
    # Test measurement optimization
    time_savings, actual_time = pm.simulate_measurement_optimization()
    
    # Test measurement accuracy
    accuracy_results = pm.test_measurement_accuracy(10)
    
    print(f"\n=== Optimization Summary ===")
    print(f"Measurement settling time savings: {time_savings:.1f}s per measurement")
    print(f"Measurement accuracy maintained: {accuracy_results['accuracy_percentage']:.1f}% within spec")
    print(f"Measurement rate improvement: {accuracy_results['measurement_rate']:.1f} measurements/second")
    
    pm.close()


if __name__ == "__main__":
    test_power_meter_optimization()
