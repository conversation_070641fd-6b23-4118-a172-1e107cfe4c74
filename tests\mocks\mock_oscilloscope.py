#!/usr/bin/env python3
"""
Mock Oscilloscope for Testing
Simulates the behavior of oscilloscope instruments
"""

import time
import random
import math
from unittest.mock import Mock

class MockOscilloscope:
    """Mock implementation of oscilloscope"""
    
    def __init__(self):
        self.connected = False
        self.time_scale = 1.0e-6  # 1 us/div
        self.time_position = 0.0
        self.voltage_scale = 0.1  # 100 mV/div
        self.trigger_level = 0.0
        self.measure_source = 1
        
        # Simulated waveform parameters
        self.pulse_width = 3.5e-6  # 3.5 microseconds
        self.rise_time = 2.5e-6    # 2.5 microseconds
        self.fall_time = 3.0e-6    # 3.0 microseconds
        self.amplitude = 1.0       # 1 volt
        self.noise_level = 0.01    # 10 mV noise
        
        # Timing simulation
        self.config_delay = 0.01   # Simulated configuration delay
        self.measurement_delay = 0.05  # Simulated measurement delay
        
    def connect(self, address):
        """Simulate connection to oscilloscope"""
        time.sleep(0.1)
        self.connected = True
        print(f"Mock Oscilloscope connected to {address}")
        return True
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        print("Mock Oscilloscope disconnected")
        
    def timeScale(self, scale, unit='s'):
        """Simulate setting time scale with optimized timing"""
        if not self.connected:
            raise Exception("Oscilloscope not connected")
            
        # Simulate optimized configuration (reduced from 0.3s to 0.1s)
        time.sleep(self.config_delay)
        
        if unit == 'us':
            self.time_scale = scale * 1e-6
        elif unit == 's':
            self.time_scale = scale
        else:
            self.time_scale = scale
            
        print(f"Mock Scope: Time scale set to {scale} {unit}")
        
    def setTimePosition(self, position):
        """Simulate setting time position with optimized timing"""
        if not self.connected:
            raise Exception("Oscilloscope not connected")
            
        # Simulate optimized positioning (reduced from 1s to 0.5s for measurements)
        time.sleep(self.config_delay)
        self.time_position = position
        print(f"Mock Scope: Time position set to {position}")
        
    def setMeasureSource(self, channel):
        """Simulate setting measurement source"""
        if not self.connected:
            raise Exception("Oscilloscope not connected")
            
        time.sleep(self.config_delay)
        self.measure_source = channel
        print(f"Mock Scope: Measurement source set to channel {channel}")
        
    def measPWidth(self):
        """Simulate measuring pulse width with optimized timing"""
        if not self.connected:
            raise Exception("Oscilloscope not connected")
            
        # Simulate optimized measurement timing (reduced from 1s to 0.5s)
        time.sleep(self.measurement_delay)
        
        # Add realistic measurement variation
        variation = random.uniform(-0.1e-6, 0.1e-6)
        measured_width = self.pulse_width + variation
        
        print(f"Mock Scope: Pulse width measured: {measured_width*1e6:.2f} µs")
        return measured_width
        
    def measRiseTime(self):
        """Simulate measuring rise time"""
        if not self.connected:
            raise Exception("Oscilloscope not connected")
            
        time.sleep(self.measurement_delay)
        
        variation = random.uniform(-0.1e-6, 0.1e-6)
        measured_rise = self.rise_time + variation
        
        print(f"Mock Scope: Rise time measured: {measured_rise*1e6:.2f} µs")
        return measured_rise
        
    def measFallTime(self):
        """Simulate measuring fall time"""
        if not self.connected:
            raise Exception("Oscilloscope not connected")
            
        time.sleep(self.measurement_delay)
        
        variation = random.uniform(-0.1e-6, 0.1e-6)
        measured_fall = self.fall_time + variation
        
        print(f"Mock Scope: Fall time measured: {measured_fall*1e6:.2f} µs")
        return measured_fall
        
    def measVMax(self):
        """Simulate measuring maximum voltage"""
        if not self.connected:
            raise Exception("Oscilloscope not connected")
            
        time.sleep(self.measurement_delay)
        
        variation = random.uniform(-0.01, 0.01)
        measured_vmax = self.amplitude + variation
        
        print(f"Mock Scope: Maximum voltage measured: {measured_vmax:.3f} V")
        return measured_vmax
        
    def measVMin(self):
        """Simulate measuring minimum voltage"""
        if not self.connected:
            raise Exception("Oscilloscope not connected")
            
        time.sleep(self.measurement_delay)
        
        variation = random.uniform(-0.005, 0.005)
        measured_vmin = self.noise_level + variation
        
        print(f"Mock Scope: Minimum voltage measured: {measured_vmin:.3f} V")
        return measured_vmin
        
    def measXMax(self):
        """Simulate measuring X position of maximum"""
        if not self.connected:
            raise Exception("Oscilloscope not connected")
            
        time.sleep(self.measurement_delay)
        
        # Simulate pulse center position
        variation = random.uniform(-0.1e-6, 0.1e-6)
        measured_xmax = self.time_position + self.pulse_width/2 + variation
        
        print(f"Mock Scope: X max position measured: {measured_xmax*1e6:.2f} µs")
        return measured_xmax
        
    def getEdges(self, threshold=0.5):
        """Simulate edge detection"""
        if not self.connected:
            raise Exception("Oscilloscope not connected")
            
        time.sleep(self.measurement_delay * 2)  # Edge detection takes longer
        
        # Simulate finding pulse edges
        num_pulses = random.randint(1, 3)
        positive_edges = []
        negative_edges = []
        
        for i in range(num_pulses):
            # Simulate pulse timing
            pulse_start = i * 10e-6 + random.uniform(-0.5e-6, 0.5e-6)
            pulse_end = pulse_start + self.pulse_width + random.uniform(-0.1e-6, 0.1e-6)
            
            positive_edges.append(pulse_start)
            negative_edges.append(pulse_end)
            
        print(f"Mock Scope: Found {len(positive_edges)} positive edges, {len(negative_edges)} negative edges")
        return positive_edges, negative_edges
        
    def simulate_optimized_measurements(self):
        """
        Simulate the optimized oscilloscope measurement sequence
        This demonstrates the MEDIUM PRIORITY optimization in action
        """
        print("=== Simulating Optimized Oscilloscope Measurements ===")
        
        # Original measurement sequence with individual delays
        print("Original measurement method:")
        original_delays = [
            ("timeScale", 0.3),
            ("setTimePosition", 1.0),
            ("measPWidth", 1.0),
            ("timeScale", 0.3),
            ("measRiseTime", 0.3),
            ("setTimePosition", 0.3),
            ("measFallTime", 0.3)
        ]
        
        original_time = sum(delay for _, delay in original_delays)
        print(f"Original method would take: {original_time}s")
        
        # Optimized measurement sequence
        print("\nOptimized measurement method:")
        start_time = time.time()
        
        # Set up scope with optimized timing
        self.timeScale(0.5, "us")  # Reduced from 0.3s to 0.01s delay
        self.setTimePosition(0.0)  # Reduced from 1s to 0.01s delay
        
        # Measure pulse width with optimized timing
        pulse_width = self.measPWidth()  # Reduced from 1s to 0.05s delay
        
        # Additional measurements with optimized timing
        self.timeScale(0.5, "us")  # Reduced delay
        rise_time = self.measRiseTime()  # Reduced delay
        
        self.setTimePosition(5e-6)  # Move to pulse end
        fall_time = self.measFallTime()  # Reduced delay
        
        optimized_time = time.time() - start_time
        
        print(f"Optimized method took: {optimized_time:.2f}s")
        print(f"Time savings: {original_time - optimized_time:.2f}s")
        print(f"Performance improvement: {((original_time - optimized_time) / original_time) * 100:.1f}%")
        
        print(f"\nMeasurement Results:")
        print(f"Pulse Width: {pulse_width*1e6:.2f} µs")
        print(f"Rise Time: {rise_time*1e6:.2f} µs")
        print(f"Fall Time: {fall_time*1e6:.2f} µs")
        
        return original_time, optimized_time, {
            'pulse_width': pulse_width,
            'rise_time': rise_time,
            'fall_time': fall_time
        }
        
    def simulate_pulse_noise_measurement(self):
        """
        Simulate pulse noise measurement with optimized timing
        """
        print("=== Simulating Pulse Noise Measurement ===")
        
        start_time = time.time()
        
        # Set measurement source
        self.setMeasureSource(3)
        
        # Set time scale with optimized delay
        self.timeScale(0.5, "us")
        
        # Position scope at pulse center
        pulse_center = self.measXMax()
        self.setTimePosition(pulse_center)
        
        # Measure pulse top noise (optimized from 5s to 1s delay)
        vmax = self.measVMax()
        vmin = self.measVMin()
        
        # Calculate noise percentage
        noise_percent = abs(((0.95 * vmax) - vmin) / (0.95 * vmax)) * 100
        
        # Simulate optimized completion delay (1s instead of 5s)
        time.sleep(0.1)
        
        measurement_time = time.time() - start_time
        
        print(f"Pulse noise measurement completed in {measurement_time:.2f}s")
        print(f"Pulse noise: {noise_percent:.2f}%")
        print(f"Time savings vs original: {5.0 - measurement_time:.2f}s")
        
        return noise_percent, measurement_time


class MockOscilloscopeTDS:
    """Mock Tektronix TDS series oscilloscope"""
    
    def __init__(self):
        self.scope = MockOscilloscope()
        self.model = "TDS"
        self.manufacturer = "Tektronix"
        
    def connect(self, address):
        return self.scope.connect(address)
        
    def disconnect(self):
        return self.scope.disconnect()
        
    def timeScale(self, scale, unit='s'):
        return self.scope.timeScale(scale, unit)
        
    def setTimePosition(self, position):
        return self.scope.setTimePosition(position)
        
    def setMeasureSource(self, channel):
        return self.scope.setMeasureSource(channel)
        
    def measPWidth(self):
        return self.scope.measPWidth()
        
    def measRiseTime(self):
        return self.scope.measRiseTime()
        
    def measFallTime(self):
        return self.scope.measFallTime()


# Test function to demonstrate optimization benefits
def test_oscilloscope_optimization():
    """
    Test function to compare original vs optimized oscilloscope measurements
    """
    print("=== Oscilloscope Optimization Test ===")
    
    scope = MockOscilloscope()
    scope.connect("USB::0x0699::0x0368::C012345::INSTR")
    
    # Test optimized measurement sequence
    original_time, optimized_time, results = scope.simulate_optimized_measurements()
    
    print(f"\n=== Testing Pulse Noise Measurement ===")
    
    # Test pulse noise measurement
    noise_percent, noise_time = scope.simulate_pulse_noise_measurement()
    
    print(f"\n=== Optimization Summary ===")
    print(f"Measurement sequence optimization: {original_time - optimized_time:.2f}s saved")
    print(f"Pulse noise measurement optimization: {5.0 - noise_time:.2f}s saved")
    print(f"Total time savings: {(original_time - optimized_time) + (5.0 - noise_time):.2f}s")
    
    scope.disconnect()


if __name__ == "__main__":
    test_oscilloscope_optimization()
