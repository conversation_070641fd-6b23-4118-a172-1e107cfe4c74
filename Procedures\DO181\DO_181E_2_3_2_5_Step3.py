# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Pulse Decoder Characterics, Section *******
             
             Step3: Pulse Position Tolerances, P1/P3 ATCRBS Type Interrogations.
             For each interrogation type (ModeA, ModeC/S All Call) vary
             the P1-P3 spacing within the required acceptable range and
             verify the reply ration is at least 90%.
             
           
             
INPUTS:      RM, ATC, PathLoss
OUTPUTS:     ModeA_Replies - array of reply ratios at min,max and out of bounds P1/P3 Spacing
             ModeCS_Replies - array of reply ratios at min,max and out of bounds P1/P3 Spacing


HISTORY:

04/23/2020   MRS    Initial Release.
05/11/2020   MRS    Cleanup
03/03/2021   MRS    Updates for new Handlers and Lobster.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_5_Step3(rm, atc,PathLoss):
    
    rm.logMessage(2,"*** DO-181E, Pulse Decoder Characterics: Sect *******_Step3 ***")
    
    
    #Results read by TestStand
    ModeA_Replies = [0.0,0.0,0.0]                                #Values read by TestStand
    ModeCS_Replies = [0.0,0.0,0.0]                                #Values read by TestStand
    
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     

    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    atc.waitforstatus()

    
    rm.logMessage(0,"Test_2_3_2_5_Step3a - ModeA P1P3 7.8usec")   
    atc.gwrite(":ATC:XPDR:PUL:P13SPACING 7.8")
    time.sleep(5)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1

    
    print("Reply Rate: ",replyrate)
    ModeA_Replies[0] = replyrate[1]    # %Bot Mode A
    

    rm.logMessage(0,"Test_2_3_2_5_Step3a - ModeA P1P3 8.2usec")   
    atc.gwrite(":ATC:XPDR:PUL:P13SPACING 7.8")
    time.sleep(2)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    print("Reply Rate: ",replyrate)

    ModeA_Replies[1] = replyrate[1]   # %Bot Mode A
 
    rm.logMessage(0,"Test_2_3_2_5_Step3a - ModeA P1P3 9.0usec")   
    atc.gwrite(":ATC:XPDR:PUL:P13SPACING 9.0")
    time.sleep(2)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    print("Reply Rate: ",replyrate)
    
    ModeA_Replies[2] = replyrate[1]    # %Bot Mode A, should be 0%, out of toleranc

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    rm.logMessage(0,"Test_2_3_2_5_Step3 - Done")    

    #Set Up Transponder -- MODE C/S All Call
    atc.transponderModeCS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(5)
    atc.waitforstatus()

    
    rm.logMessage(0,"Test_2_3_2_5_Step3b - ModeCS P1P3 21.2usec")   
    atc.gwrite(":ATC:XPDR:PUL:P13SPACING 21.2")
    time.sleep(2)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    print("Reply Rate: ",replyrate)
    
    ModeCS_Replies[0] = replyrate[3]    # %Bot Mode CS
    

    rm.logMessage(0,"Test_2_3_2_5_Step3b - ModeCS P1P3 20.8usec")   
    atc.gwrite(":ATC:XPDR:PUL:P13SPACING 20.8")
    time.sleep(2)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    print("Reply Rate: ",replyrate)
    
    ModeCS_Replies[1] = replyrate[3]    # %Bot Mode CS
 
    rm.logMessage(0,"Test_2_3_2_5_Step3b - ModeCS P1P3 18.0usec")   
    atc.gwrite(":ATC:XPDR:PUL:P13SPACING 18.0")
    time.sleep(10)
    atc.waitforstatus()
    
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    print("Reply Rate: ",replyrate)

    ModeCS_Replies[2] = replyrate[3]    # %Bot Mode CS, should be 0% out of tolerance

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    rm.logMessage(0,"Test_2_3_2_5_Step3 - Done: " + str(ModeA_Replies) + str(ModeCS_Replies))       
    rm.logMessage(2,"Done, closing session")
   
    return ModeA_Replies + ModeCS_Replies

##########################################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

     
    res = Test_2_3_2_5_Step3(rm,atc_obj,12.0)
    
    atc_obj.close()

