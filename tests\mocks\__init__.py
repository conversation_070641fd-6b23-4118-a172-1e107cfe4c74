"""
TXD Qualification Test System - Mock Interface Package
This package contains mock implementations for all hardware interfaces

Mock Interfaces Available:
- MockATC5000NG: Aviation test controller simulation
- MockUATConnection: UAT communication simulation
- MockARINC429: ARINC 429 bus communication simulation
- MockSpectrumAnalyzer: Spectrum analyzer simulation (N9010B)
- MockOscilloscope: Oscilloscope simulation (D3054, MSO56, NI5110)
- MockPowerMeter: Power meter simulation (B4500C)
- MockSignalGenerator: Signal generator simulation (N5172B)
- MockResourceManager: Resource management simulation
- MockRFBOB: RF breakout box simulation
- MockPickering: Pickering switch matrix simulation
- MockNI6363MultiIO: NI multifunction I/O simulation
- MockNI6528Discretes: NI discrete I/O simulation
- MockDCPowerSupply: DC power supply simulation (N6700)

All mocks simulate realistic response times and data patterns
for comprehensive testing of the TXD qualification system.
"""

# Import all mock interfaces for easy access
from .mock_atc5000ng import MockATC5000NG
from .mock_uat_connection import MockUAT<PERSON>onnection, MockUATClient
from .mock_arinc429 import <PERSON>ckARINC429
from .mock_spectrum_analyzer import MockSpectrumAnalyzer
from .mock_oscilloscope import MockOscilloscope
from .mock_power_meter import MockPowerMeter
from .mock_signal_generator import MockSignalGenerator
from .mock_resource_manager import MockResourceManager
from .mock_rfbob import MockRFBOB
from .mock_pickering import MockPickering
from .mock_ni_multiio import MockNI6363MultiIO
from .mock_ni_discretes import MockNI6528Discretes
from .mock_dc_power_supply import MockDCPowerSupply

__all__ = [
    'MockATC5000NG',
    'MockUATConnection',
    'MockUATClient',
    'MockARINC429',
    'MockSpectrumAnalyzer',
    'MockOscilloscope',
    'MockPowerMeter',
    'MockSignalGenerator',
    'MockResourceManager',
    'MockRFBOB',
    'MockPickering',
    'MockNI6363MultiIO',
    'MockNI6528Discretes',
    'MockDCPowerSupply'
]
