"""
TXD Qualification Test System - Mock Interface Package
This package contains mock implementations for all hardware interfaces

Mock Interfaces Available:
- MockATC5000NG: Aviation test controller simulation
- MockUATConnection: UAT communication simulation
- MockARINC429: ARINC 429 bus communication simulation
- MockSpectrumAnalyzer: Spectrum analyzer simulation
- MockOscilloscope: Oscilloscope simulation
- MockPowerMeter: Power meter simulation
- MockSignalGenerator: Signal generator simulation
- MockResourceManager: Resource management simulation

All mocks simulate realistic response times and data patterns
for comprehensive testing of the TXD qualification system.
"""

# Import all mock interfaces for easy access
from .mock_atc5000ng import MockATC5000NG
from .mock_uat_connection import MockUATConnection, MockUATClient
from .mock_arinc429 import MockARINC429
from .mock_spectrum_analyzer import MockSpectrumAnalyzer
from .mock_oscilloscope import MockOscilloscope
from .mock_power_meter import MockPowerMeter
from .mock_signal_generator import MockSignalGenerator
from .mock_resource_manager import MockResourceManager

__all__ = [
    'MockATC5000NG',
    'MockUATConnection',
    'Mock<PERSON>ATClient',
    'Mock<PERSON>INC429',
    'MockSpectrumAnalyzer',
    '<PERSON>ckOscilloscope',
    'MockPowerMeter',
    'MockSignalGenerator',
    'MockResourceManager'
]
