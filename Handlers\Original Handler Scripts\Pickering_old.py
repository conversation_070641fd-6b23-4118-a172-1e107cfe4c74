''' Instrument class for PXI 26XSPDT 2AMP RELAY CARD AND 40-670A Multiplexor Module '''

import time, pilpxi, ctypes

class Pickering():
    def __init__(self, ate_rm):
        self.resourceManager = ate_rm
        self.base = pilpxi.pilpxi_base()
        self.relayCard = pilpxi.pilpxi_card(12, 12) # PXI 26XSPDT (40-131-201) (1-27)
        self.muxCard   = pilpxi.pilpxi_card(12, 11) # PXI 40-670A (1-99)
        (relayInfo, muxInfo) = self.ident()
        self.resourceManager.logMessage(0, "Card ID {} Opened".format(str(relayInfo)))
        self.resourceManager.logMessage(0, "Card ID {} Opened".format(str(muxInfo)))
    
    def ident(self):
        err, relayInfo = self.relayCard.CardId()
        if err != 0:
            errStr = str(self.relayCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            self.resourceManager.logMessage(1, "Relay Card ID: " + str(relayInfo))
        muxInfo = self.muxCard.CardId()
        if err != 0:
            errStr = str(self.muxCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            self.resourceManager.logMessage(1, "Mux Card ID: " + str(muxInfo))
        return (relayInfo, muxInfo)

    def close(self):
        err = self.relayCard.CloseSpecifiedCard()
        if err != 0:
            errStr = str(self.relayCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        self.resourceManager.logMessage(2, "Relay Card Closed")

        err = self.muxCard.CloseSpecifiedCard()
        if err != 0:
            errStr = str(self.muxCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        self.resourceManager.logMessage(2, "Mux Card Closed")

    # PXI 26XSPDT Relay Card Operations

    ''' Clears (de-energises) all outputs on relay card '''
    def clearRelayCard(self):
        err = self.relayCard.ClearCard()
        if err != 0:
            errStr = str(self.relayCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            self.resourceManager.logMessage(1, "Outputs cleared on Relay Card")

    ''' Energise/De-energise selected relay switch on 26XSPDT'''
    def setRelay(self, switchBit, state):
        err = self.relayCard.OpBit(1, switchBit, state)
        if err != 0:
            errStr = str(self.relayCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            self.resourceManager.logMessage(1, "Relay " + str(switchBit) + " set to " + str(state))

    ''' Get state of a relay. Relay number must be 1-26 '''
    def readRelay(self, switchBit):
        err, state = self.relayCard.ViewBit(1, switchBit)
        if err != 0:
            errStr = str(self.relayCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            self.resourceManager.logMessage(1, "Relay " + str(switchBit) + " currently is " + str(state))
        return state

    ''' Energise/De-energise multiple relays simultaneously on 26XSPDT'''
    def setRelayCard(self, mask):
        maskVal = ctypes.c_uint(mask)
        err = self.relayCard.WriteSub(1, ctypes.byref(maskVal))
        if err != 0:
            errStr = str(self.relayCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            self.resourceManager.logMessage(1, "Relay Mask " + str(mask) + " written")

    ''' Retrieve array of states representing entire relay card '''
    def readRelayCard(self):
        err, dword, subArray = self.relayCard.ViewSub(1)
        if err != 0:
            errStr = str(self.relayCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            data = [int(i) for i in '{0:026b}'.format(list(subArray)[0])] # Split mask value into 26 switch states
            data.reverse()
            return data

    # PXI 40-670A Multiplexor Card Operations

    ''' Clears (de-energises) all outputs on mux card '''
    def clearMuxCard(self):
        err = self.muxCard.ClearCard()
        if err != 0:
            errStr = str(self.muxCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            self.resourceManager.logMessage(1, "Outputs cleared on Mux Card")

    ''' Set single switch on mux '''
    def setMux(self, channel, com):
        #self.clearMuxCard() # Open any closed mux channels first
        err = self.muxCard.OpBit(1, channel, com)
        if err != 0:
            errStr = str(self.muxCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            self.resourceManager.logMessage(1, "Mux Channel " + str(channel) + " set to " + str(com))

    ''' Set multiple switches on mux simultaneously on 40-670A''' #Note: this function does not work
    def setMuxCard(self, mask):
        maskVal = ctypes.c_uint(mask)
        err = self.muxCard.WriteSub(1, ctypes.byref(maskVal))
        if err != 0:
            errStr = str(self.muxCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            self.resourceManager.logMessage(1, "Mux Mask " + str(mask) + " written")

    ''' Get state of a mux channel. Channel number must be 1-99 '''
    def readMux(self, channel):
        err, state = self.muxCard.ViewBit(1, channel)
        if err != 0:
            errStr = str(self.muxCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            self.resourceManager.logMessage(1, "Mux channel " + str(channel) + " currently is " + str(state))
        return state

    ''' Retrieve array of states representing entire mux card '''   #Note: this does not find any mux swithes over 32
    def readMuxCard(self):
        err, dword, subArray = self.muxCard.ViewSub(1)
        if err != 0:
            errStr = str(self.muxCard.ErrorMessage(err)[1])
            self.resourceManager.logMessage(3, "{} - Error Code {} ".format(errStr, err))
            raise(ValueError(errStr))
        else:
            data = [int(i) for i in '{0:099b}'.format(list(subArray)[0])] # Split mask value into 99 mux states
            data.reverse()
            return data