#!/usr/bin/env python3
"""
Mock Pickering Switch Matrix Handler for Testing
Simulates the behavior of Pickering switch matrix systems
"""

import time
import random
from unittest.mock import Mock

class MockPickering:
    """Mock implementation of Pickering switch matrix handler"""
    
    def __init__(self, resource_manager=None):
        self.resourceManager = resource_manager or Mock()
        self.connected = False
        self.switch_states = {}
        self.relay_states = {}
        self.card_info = {}
        
        # Initialize default card configuration
        self.card_info = {
            1: {"type": "40-785", "channels": 64, "description": "RF Switch Matrix"},
            2: {"type": "40-786", "channels": 32, "description": "RF Multiplexer"},
            3: {"type": "40-787", "channels": 16, "description": "RF Attenuator"}
        }
        
        # Initialize all switches to open state
        for card in self.card_info:
            self.switch_states[card] = {}
            self.relay_states[card] = {}
            for channel in range(1, self.card_info[card]["channels"] + 1):
                self.switch_states[card][channel] = "OPEN"
                self.relay_states[card][channel] = False
                
    def connect(self, address="PXI0::CHASSIS1::SLOT1::INSTR"):
        """Simulate connection to Pickering system"""
        time.sleep(0.2)  # Simulate connection time
        self.connected = True
        self.resourceManager.logMessage(1, f"Mock Pickering connected to {address}")
        return True
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        self.resourceManager.logMessage(1, "Mock Pickering disconnected")
        
    def write(self, command):
        """Simulate writing command to Pickering"""
        if not self.connected:
            raise Exception("Pickering not connected")
            
        time.sleep(0.01)  # Simulate command processing
        self.resourceManager.logMessage(1, f"Mock Pickering command: {command}")
        
        # Parse and simulate command effects
        if "CLOSE" in command:
            self._parse_switch_command(command, "CLOSE")
        elif "OPEN" in command:
            self._parse_switch_command(command, "OPEN")
        elif "RESET" in command:
            self.reset()
            
    def _parse_switch_command(self, command, action):
        """Parse switch command and update states"""
        # Example: "CLOSE (@1001:1064)" or "OPEN (@2001)"
        if "@" in command:
            channel_spec = command.split("@")[1].replace(")", "").replace("(", "")
            
            if ":" in channel_spec:
                # Range specification
                start_str, end_str = channel_spec.split(":")
                start_card = int(start_str[0])
                start_channel = int(start_str[1:])
                end_card = int(end_str[0])
                end_channel = int(end_str[1:])
                
                if start_card == end_card:
                    for channel in range(start_channel, end_channel + 1):
                        if channel in self.switch_states[start_card]:
                            self.switch_states[start_card][channel] = action
                            self.relay_states[start_card][channel] = (action == "CLOSE")
            else:
                # Single channel
                card = int(channel_spec[0])
                channel = int(channel_spec[1:])
                if card in self.switch_states and channel in self.switch_states[card]:
                    self.switch_states[card][channel] = action
                    self.relay_states[card][channel] = (action == "CLOSE")
                    
    def query(self, command):
        """Simulate querying Pickering for data"""
        if not self.connected:
            raise Exception("Pickering not connected")
            
        time.sleep(0.01)
        self.resourceManager.logMessage(1, f"Mock Pickering query: {command}")
        
        # Simulate responses
        if "*IDN?" in command:
            return "Pickering Interfaces Ltd,Mock Switch Matrix,SN123456,FW1.0"
        elif "SWITCH:STATE?" in command:
            # Return switch states
            if "@" in command:
                channel_spec = command.split("@")[1].replace(")", "").replace("(", "")
                card = int(channel_spec[0])
                channel = int(channel_spec[1:])
                if card in self.switch_states and channel in self.switch_states[card]:
                    return "1" if self.switch_states[card][channel] == "CLOSE" else "0"
            return "0"
        elif "CARD:INFO?" in command:
            # Return card information
            info_str = ""
            for card, info in self.card_info.items():
                info_str += f"Card {card}: {info['type']}, {info['channels']} channels, {info['description']}\\n"
            return info_str.strip()
        else:
            return "OK"
            
    def closeSwitch(self, card, channel):
        """Close a specific switch"""
        if card in self.switch_states and channel in self.switch_states[card]:
            self.switch_states[card][channel] = "CLOSE"
            self.relay_states[card][channel] = True
            self.write(f"CLOSE (@{card}{channel:03d})")
            self.resourceManager.logMessage(1, f"Mock Pickering Switch {card}-{channel} CLOSED")
        else:
            raise ValueError(f"Invalid card/channel: {card}/{channel}")
            
    def openSwitch(self, card, channel):
        """Open a specific switch"""
        if card in self.switch_states and channel in self.switch_states[card]:
            self.switch_states[card][channel] = "OPEN"
            self.relay_states[card][channel] = False
            self.write(f"OPEN (@{card}{channel:03d})")
            self.resourceManager.logMessage(1, f"Mock Pickering Switch {card}-{channel} OPENED")
        else:
            raise ValueError(f"Invalid card/channel: {card}/{channel}")
            
    def getSwitchState(self, card, channel):
        """Get switch state"""
        if card in self.switch_states and channel in self.switch_states[card]:
            return self.switch_states[card][channel]
        else:
            raise ValueError(f"Invalid card/channel: {card}/{channel}")
            
    def closeRange(self, card, start_channel, end_channel):
        """Close a range of switches"""
        for channel in range(start_channel, end_channel + 1):
            if channel in self.switch_states[card]:
                self.switch_states[card][channel] = "CLOSE"
                self.relay_states[card][channel] = True
                
        self.write(f"CLOSE (@{card}{start_channel:03d}:{card}{end_channel:03d})")
        self.resourceManager.logMessage(1, f"Mock Pickering Switches {card}-{start_channel} to {card}-{end_channel} CLOSED")
        
    def openRange(self, card, start_channel, end_channel):
        """Open a range of switches"""
        for channel in range(start_channel, end_channel + 1):
            if channel in self.switch_states[card]:
                self.switch_states[card][channel] = "OPEN"
                self.relay_states[card][channel] = False
                
        self.write(f"OPEN (@{card}{start_channel:03d}:{card}{end_channel:03d})")
        self.resourceManager.logMessage(1, f"Mock Pickering Switches {card}-{start_channel} to {card}-{end_channel} OPENED")
        
    def setPath(self, input_card, input_channel, output_card, output_channel):
        """Set a signal path through the matrix"""
        # Close the input and output switches
        self.closeSwitch(input_card, input_channel)
        self.closeSwitch(output_card, output_channel)
        
        self.resourceManager.logMessage(1, 
            f"Mock Pickering Path set: {input_card}-{input_channel} to {output_card}-{output_channel}")
            
    def clearPath(self, input_card, input_channel, output_card, output_channel):
        """Clear a signal path through the matrix"""
        # Open the input and output switches
        self.openSwitch(input_card, input_channel)
        self.openSwitch(output_card, output_channel)
        
        self.resourceManager.logMessage(1, 
            f"Mock Pickering Path cleared: {input_card}-{input_channel} to {output_card}-{output_channel}")
            
    def reset(self):
        """Reset all switches to open state"""
        self.resourceManager.logMessage(1, "Mock Pickering Reset")
        
        for card in self.switch_states:
            for channel in self.switch_states[card]:
                self.switch_states[card][channel] = "OPEN"
                self.relay_states[card][channel] = False
                
        time.sleep(0.2)  # Simulate reset time
        
    def getCardInfo(self, card):
        """Get information about a specific card"""
        if card in self.card_info:
            return self.card_info[card]
        else:
            raise ValueError(f"Invalid card number: {card}")
            
    def getAllSwitchStates(self):
        """Get all switch states"""
        return self.switch_states.copy()
        
    def close(self):
        """Close connection to Pickering"""
        self.disconnect()
