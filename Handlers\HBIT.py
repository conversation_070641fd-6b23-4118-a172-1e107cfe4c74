# -*- coding: utf-8 -*-
"""
Created on 12/17/2020

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
DESCRIPTION:
HBIT limited function and attribute class.  Provides Connections and Command capibilites for HBIT.

INPUTS: ATE Resource Manager obj (rm), Connection String (connect_str)
OUTPUTS: N/A

HISTORY:
    
12/17/2020  MRS  Initial Release


"""

import time
import socket


class HBIT():
    def __init__(self, ate_rm, connect_str):
        
        self.resourceManager = ate_rm
 
        #Connection 
        self.HOST = connect_str  # HBIT IP Address ('*************')

        #TimeOut for Connections
        self.TimeOut = 1
        
        #Create a Sockets and Connections, one socket/connection for each port
        self.PORT_APU1 = 5001    # Port to listen on (non-privileged ports are > 1023)
        self.PORT_APU2 = 5002
        self.PORT_APU3 = 5003
        self.PORT_RPU0 = 5004
        self.PORT_RPU1 = 5005
        self.PORT_STAT = 5006

        #Sockets and Connections
        #  APU1 Socket
        try:
          self.HBit_APU1 = socket.socket(socket.AF_INET, socket.SOCK_STREAM) 
          self.resourceManager.logMessage(1,"APU1 Socket successfully created")
        except socket.error as err: 
          echo_str = ("APU1 Socket creation failed with error %s" % (err))
          self.resourceManager.logMessage(3,echo_str)
          return    
        self.HBit_APU1.settimeout(self.TimeOut) #set the socket to timeout after 1 second
        #  APU1 Connection
        try:
            self.HBit_APU1.connect((self.HOST, self.PORT_APU1))
            self.resourceManager.logMessage(1,"APU1 Connection Success")
        except socket.error as err:
            echo_str = ("APU1 socket connection failed with error %s" %(err))
            self.resourceManager.logMessage(3,echo_str)
            self.HBit_APU1.close()
            return
            
        #  APU2 Socket
        try:
          self.HBit_APU2 = socket.socket(socket.AF_INET, socket.SOCK_STREAM) 
          self.resourceManager.logMessage(1,"APU2 Socket successfully created")
        except socket.error as err: 
          echo_str = ("APU2 Socket creation failed with error %s" %(err))
          self.resourceManager.logMessage(3,echo_str)
          return    
        self.HBit_APU2.settimeout(self.TimeOut) #set the socket to timeout after 1 second
        #  APU2 Connection
        try:
            self.HBit_APU2.connect((self.HOST, self.PORT_APU2))
            self.resourceManager.logMessage(1,"APU2 Connection Success")
        except socket.error as err:
            echo_str = ("APU2 socket connection failed with error %s" %(err))
            self.resourceManager.logMessage(3,echo_str)
            self.HBit_APU2.close()
            return
            
        #  APU3 Socket
        try:
          self.HBit_APU3 = socket.socket(socket.AF_INET, socket.SOCK_STREAM) 
          self.resourceManager.logMessage(1,"APU3 Socket successfully created")
        except socket.error as err: 
          echo_str = ("APU3 Socket creation failed with error %s" %(err))
          self.resourceManager.logMessage(3,echo_str)
          return    
        self.HBit_APU3.settimeout(self.TimeOut) #set the socket to timeout after 1 second
        #  APU3 Connection
        try:
            self.HBit_APU3.connect((self.HOST, self.PORT_APU3))
            self.resourceManager.logMessage(1,"APU3 Connection Success")
        except socket.error as err:
            echo_str = ("APU3 socket connection failed with error %s" %(err))
            self.resourceManager.logMessage(3,echo_str)
            self.HBit_APU3.close()
            return

        #  RPU0 Socket
        try:
          self.HBit_RPU0 = socket.socket(socket.AF_INET, socket.SOCK_STREAM) 
          self.resourceManager.logMessage(1,"RPU0 Socket successfully created")
        except socket.error as err: 
          echo_str = ("RPU0 Socket creation failed with error %s" %(err))
          self.resourceManager.logMessage(3,echo_str)
          return    
        self.HBit_RPU0.settimeout(self.TimeOut) #set the socket to timeout after 1 second
        #  RPU0 Connection
        try:
            self.HBit_RPU0.connect((self.HOST, self.PORT_RPU0))
            self.resourceManager.logMessage(1,"RPU0 Connection Success")
        except socket.error as err:
            echo_str = ("RPU0 socket connection failed with error %s" %(err))
            self.resourceManager.logMessage(3,echo_str)
            self.HBit_RPU0.close()
            return

        #  RPU1 Socket
        try:
          self.HBit_RPU1 = socket.socket(socket.AF_INET, socket.SOCK_STREAM) 
          self.resourceManager.logMessage(1,"RPU1 Socket successfully created")
        except socket.error as err: 
          echo_str = ("RPU1 Socket creation failed with error %s" %(err))
          self.resourceManager.logMessage(3,echo_str)
          return    
        self.HBit_RPU1.settimeout(self.TimeOut) #set the socket to timeout after 1 second
        #  RPU1 Connection
        try:
            self.HBit_RPU1.connect((self.HOST, self.PORT_RPU1))
            self.resourceManager.logMessage(1,"RPU1 Connection Success")
        except socket.error as err:
            echo_str = ("RPU1 socket connection failed with error %s" %(err))
            self.resourceManager.logMessage(3,echo_str)
            self.HBit_RPU1.close()
            return

        #  STAT Socket
        try:
          self.HBit_STAT = socket.socket(socket.AF_INET, socket.SOCK_STREAM) 
          self.resourceManager.logMessage(1,"STAT Socket successfully created")
        except socket.error as err: 
          echo_str = ("STAT Socket creation failed with error %s" %(err))
          self.resourceManager.logMessage(3,echo_str)
          return    
        self.HBit_STAT.settimeout(self.TimeOut) #set the socket to timeout after 1 second
        #  STAT Connection
        try:
            self.HBit_STAT.connect((self.HOST, self.PORT_STAT))
            self.resourceManager.logMessage(1,"STAT Connection Success")
        except socket.error as err:
            echo_str = ("STAT socket connection failed with error %s" %(err))
            self.resourceManager.logMessage(3,echo_str)
            self.HBit_STAT.close()
            return

##################################################
# Basic Commands
##################################################            
        
    def close(self):
        """ Closes All HBIT Connections. """
        self.HBit_APU1.close()
        self.HBit_APU2.close()
        self.HBit_APU3.close()
        self.HBit_RPU0.close()
        self.HBit_RPU1.close()
        self.HBit_STAT.close()
        
        
    def query(self,port,cmd_str):
        """ This function is used to query the HBIT Host, port is the HBit port, 
        cmd_str is the actual command, returns string with the reply. """        
        echo_str = "Port: " + str(port) + " Cmd: " + cmd_str
        self.resourceManager.logMessage(1,echo_str)
        #add termination to command string
        cmd_str = cmd_str + "\r\n"
        if (port == self.PORT_APU1):
            self.HBit_APU1.sendall(bytes(cmd_str, encoding = "utf8"))
            try: 
                data = self.HBit_APU1.recv(256)    
                #convert bytes to back to string
                data_s = data.decode('utf-8')
                data_s = data_s.strip()
            except socket.timeout: #fail after 1 second
                data_s = ''
                echo_str = "Fail Cmd: " + cmd_str 
                self.resourceManager.logMessage(3,echo_str)    
            return data_s    
            
        if (port == self.PORT_APU2):
            self.HBit_APU2.sendall(bytes(cmd_str, encoding = "utf8"))
            try: 
                data = self.HBit_APU2.recv(256)    
                #convert bytes to back to string
                data_s = data.decode('utf-8')
                data_s = data_s.strip()
            except socket.timeout: #fail after 1 second
                data_s = ''
                echo_str = "Fail Cmd: " + cmd_str 
                self.resourceManager.logMessage(3,echo_str)    
            return data_s    
            
        if (port == self.PORT_APU3):
            self.HBit_APU3.sendall(bytes(cmd_str, encoding = "utf8"))
            try: 
                data = self.HBit_APU3.recv(256)    
                #convert bytes to back to string
                data_s = data.decode('utf-8')
                data_s = data_s.strip()
            except socket.timeout: #fail after 1 second
                data_s = ''
                echo_str = "Fail Cmd: " + cmd_str 
                self.resourceManager.logMessage(3,echo_str)    
            return data_s    
            
        if (port == self.PORT_RPU0):
            self.HBit_RPU0.sendall(bytes(cmd_str, encoding = "utf8"))
            try: 
                data = self.HBit_RPU0.recv(256)    
                #convert bytes to back to string
                data_s = data.decode('utf-8')
                data_s = data_s.strip()
            except socket.timeout: #fail after 1 second
                data_s = ''
                echo_str = "Fail Cmd: " + cmd_str 
                self.resourceManager.logMessage(3,echo_str)    
            return data_s    
            
        if (port == self.PORT_RPU1):
            self.HBit_RPU1.sendall(bytes(cmd_str, encoding = "utf8"))
            try: 
                data = self.HBit_RPU1.recv(256)    
                #convert bytes to back to string
                data_s = data.decode('utf-8')
                data_s = data_s.strip()
            except socket.timeout: #fail after 1 second
                data_s = ''
                echo_str = "Fail Cmd: " + cmd_str 
                self.resourceManager.logMessage(3,echo_str)    
            return data_s    
            
        if (port == self.PORT_STAT):
            self.HBit_STAT.sendall(bytes(cmd_str, encoding = "utf8"))
            try: 
                data = self.HBit_STAT.recv(256)    
                #convert bytes to back to string
                data_s = data.decode('utf-8')
                data_s = data_s.strip()
            except socket.timeout: #fail after 1 second
                data_s = ''
                echo_str = "Fail Cmd: " + cmd_str 
                self.resourceManager.logMessage(3,echo_str)    
            return data_s    
