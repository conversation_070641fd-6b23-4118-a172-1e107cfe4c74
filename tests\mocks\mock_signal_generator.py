#!/usr/bin/env python3
"""
Mock N5172B Signal Generator for Testing
Simulates the behavior of the real N5172B signal generator
"""

import time
import random
from unittest.mock import Mock

class MockN5172BSigGen:
    """Mock implementation of N5172B Signal Generator"""
    
    def __init__(self, resource_manager=None):
        self.resourceManager = resource_manager or Mock()
        self.siggenIP = "TCPIP0::************::INSTR"
        self.connected = False
        
        # Signal generator parameters
        self.frequency = 1030.0e6  # Hz
        self.power_level = -21.0   # dBm
        self.rf_output = False
        self.modulation = False
        
        # Timing simulation
        self.command_delay = 0.01  # Simulated command processing
        self.frequency_settling = 0.1  # Frequency settling time
        self.power_settling = 0.05     # Power settling time
        
        # Connect automatically
        self.connect()
        
    def connect(self):
        """Simulate connection to signal generator"""
        time.sleep(0.1)
        self.connected = True
        self.basicTvlMsg("Mock Signal Generator Resource Opened.")
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        self.rf_output = False
        self.basicTvlMsg("Mock Signal Generator disconnected")
        
    def basicWrite(self, cmd):
        """Simulate writing command to signal generator"""
        if not self.connected:
            raise Exception("Signal generator not connected")
            
        time.sleep(self.command_delay)
        self.basicTvlMsg(f"> {cmd}")
        
        # Simulate command effects with optimized timing
        if ":FREQ" in cmd:
            try:
                freq_str = cmd.split()[-1]
                self.frequency = float(freq_str)
                # Optimized frequency settling (reduced from 0.5s to 0.1s)
                time.sleep(self.frequency_settling / 10)  # Scale for simulation
            except:
                pass
        elif ":POW" in cmd or ":LEVEL" in cmd:
            try:
                power_str = cmd.split()[-1]
                self.power_level = float(power_str)
                # Optimized power settling (reduced from 0.3s to 0.05s)
                time.sleep(self.power_settling / 10)  # Scale for simulation
            except:
                pass
        elif ":OUTP" in cmd:
            if "ON" in cmd or "1" in cmd:
                self.rf_output = True
            elif "OFF" in cmd or "0" in cmd:
                self.rf_output = False
        elif ":MOD" in cmd:
            if "ON" in cmd or "1" in cmd:
                self.modulation = True
            elif "OFF" in cmd or "0" in cmd:
                self.modulation = False
                
    def basicQuery(self, cmd, logEnable=False):
        """Simulate querying signal generator for data"""
        if not self.connected:
            raise Exception("Signal generator not connected")
            
        time.sleep(self.command_delay)
        
        if logEnable:
            self.basicTvlMsg(f"> {cmd}")
            
        # Simulate responses based on command
        if "*IDN?" in cmd:
            response = "Agilent Technologies,N5172B,MY12345678,A.01.02"
        elif ":FREQ?" in cmd:
            # Add small frequency accuracy variation
            actual_freq = self.frequency + random.uniform(-1.0, 1.0)
            response = f"{actual_freq:.6f}"
        elif ":POW?" in cmd or ":LEVEL?" in cmd:
            # Add small power accuracy variation
            actual_power = self.power_level + random.uniform(-0.1, 0.1)
            response = f"{actual_power:.3f}"
        elif ":OUTP?" in cmd:
            response = "1" if self.rf_output else "0"
        elif ":MOD?" in cmd:
            response = "1" if self.modulation else "0"
        else:
            response = "OK"
            
        if logEnable:
            self.basicTvlMsg(f"< {response}")
            
        return response
        
    def basicTvlMsg(self, tvlTxt):
        """Log message through resource manager"""
        if self.resourceManager:
            self.resourceManager.logMessage(1, tvlTxt)
        else:
            print(f"Mock Signal Generator: {tvlTxt}")
            
    def Ident(self):
        """Returns Instrument Ident"""
        return self.basicQuery("*IDN?")
        
    def close(self):
        """Close signal generator connection"""
        self.rf_output = False
        self.disconnect()
        
    # Signal generator specific methods with optimization simulation
    def setFrequency(self, frequency):
        """Set frequency with optimized settling"""
        self.basicWrite(f":FREQ {frequency}")
        
    def setPower(self, power):
        """Set power level with optimized settling"""
        self.basicWrite(f":POW {power}")
        
    def setRFOutput(self, state):
        """Set RF output state"""
        state_str = "ON" if state else "OFF"
        self.basicWrite(f":OUTP {state_str}")
        
    def setModulation(self, state):
        """Set modulation state"""
        state_str = "ON" if state else "OFF"
        self.basicWrite(f":MOD {state_str}")
        
    def simulate_configuration_optimization(self):
        """
        Simulate the MEDIUM PRIORITY configuration micro-delay optimization
        """
        print("=== Simulating Signal Generator Configuration Optimization ===")
        
        # Original configuration with individual delays
        original_commands = [
            (":FREQ 1030.0E6", 0.5),    # Frequency setting + settling
            (":POW -21", 0.3),          # Power setting + settling
            (":OUTP ON", 0.1),          # RF output on
            (":MOD OFF", 0.1),          # Modulation off
        ]
        
        # Optimized configuration with batched commands and reduced delays
        optimized_commands = [
            (":FREQ 1030.0E6", 0.1),    # Optimized frequency settling
            (":POW -21", 0.05),         # Optimized power settling
            (":OUTP ON", 0.01),         # Minimal RF switching delay
            (":MOD OFF", 0.01),         # Minimal modulation delay
        ]
        
        # Calculate original time
        original_time = sum(delay for _, delay in original_commands)
        optimized_time = sum(delay for _, delay in optimized_commands)
        
        print(f"Original configuration time: {original_time}s")
        print(f"Optimized configuration time: {optimized_time}s")
        
        # Simulate optimized configuration
        start_time = time.time()
        
        for cmd, delay in optimized_commands:
            self.basicWrite(cmd)
            time.sleep(delay / 10)  # Scale for simulation
            
        actual_time = time.time() - start_time
        time_savings = original_time - optimized_time
        
        print(f"Configuration completed in {actual_time:.3f}s")
        print(f"Time savings per configuration: {time_savings:.2f}s")
        print(f"Performance improvement: {(time_savings / original_time) * 100:.1f}%")
        
        return time_savings, actual_time
        
    def test_frequency_accuracy(self, test_frequencies=None):
        """
        Test frequency accuracy with optimized settling
        """
        if test_frequencies is None:
            test_frequencies = [1030.0e6, 1090.0e6, 978.0e6, 1150.0e6]
            
        print(f"=== Testing Frequency Accuracy ({len(test_frequencies)} frequencies) ===")
        
        results = []
        start_time = time.time()
        
        for target_freq in test_frequencies:
            # Set frequency with optimized settling
            self.setFrequency(target_freq)
            
            # Query actual frequency
            actual_freq_str = self.basicQuery(":FREQ?")
            actual_freq = float(actual_freq_str)
            
            error = abs(actual_freq - target_freq)
            error_ppm = (error / target_freq) * 1e6
            
            results.append({
                'target': target_freq,
                'actual': actual_freq,
                'error_hz': error,
                'error_ppm': error_ppm
            })
            
            # Optimized test interval
            time.sleep(0.01)
            
        test_time = time.time() - start_time
        
        # Analyze accuracy
        avg_error_ppm = sum(r['error_ppm'] for r in results) / len(results)
        max_error_ppm = max(r['error_ppm'] for r in results)
        
        within_spec = sum(1 for r in results if r['error_ppm'] <= 1.0)  # 1 ppm spec
        accuracy_percentage = (within_spec / len(results)) * 100
        
        print(f"Test completed in {test_time:.2f}s")
        print(f"Average error: {avg_error_ppm:.3f} ppm")
        print(f"Maximum error: {max_error_ppm:.3f} ppm")
        print(f"Frequencies within spec: {accuracy_percentage:.1f}%")
        print(f"Test rate: {len(test_frequencies) / test_time:.1f} frequencies/second")
        
        return {
            'avg_error_ppm': avg_error_ppm,
            'max_error_ppm': max_error_ppm,
            'accuracy_percentage': accuracy_percentage,
            'test_rate': len(test_frequencies) / test_time
        }
        
    def simulate_rf_stabilization_optimization(self):
        """
        Simulate RF stabilization optimization (related to FAR43 optimization)
        """
        print("=== Simulating RF Stabilization Optimization ===")
        
        original_stabilization_time = 25.0  # Original RF stabilization
        optimized_stabilization_time = 15.0  # Optimized stabilization
        
        print(f"Original RF stabilization: {original_stabilization_time}s")
        print(f"Optimized RF stabilization: {optimized_stabilization_time}s")
        
        # Simulate optimized RF stabilization
        start_time = time.time()
        
        # Turn on RF output
        self.setRFOutput(True)
        
        # Optimized stabilization time
        time.sleep(optimized_stabilization_time / 10)  # Scale for simulation
        
        # Verify RF is stable
        rf_state = self.basicQuery(":OUTP?")
        power_level = self.basicQuery(":POW?")
        
        actual_time = time.time() - start_time
        time_savings = original_stabilization_time - optimized_stabilization_time
        
        print(f"RF stabilization completed in {actual_time:.3f}s")
        print(f"RF Output: {'ON' if rf_state == '1' else 'OFF'}")
        print(f"Power Level: {power_level} dBm")
        print(f"Time savings per RF activation: {time_savings:.1f}s")
        print(f"Performance improvement: {(time_savings / original_stabilization_time) * 100:.1f}%")
        
        return time_savings, actual_time


# Test function to demonstrate optimization benefits
def test_signal_generator_optimization():
    """
    Test function to compare original vs optimized signal generator operations
    """
    print("=== Signal Generator Optimization Test ===")
    
    # Create mock resource manager
    mock_rm = Mock()
    
    # Create signal generator instance
    sg = MockN5172BSigGen(mock_rm)
    
    # Test configuration optimization
    config_savings, config_time = sg.simulate_configuration_optimization()
    
    # Test frequency accuracy
    freq_results = sg.test_frequency_accuracy()
    
    # Test RF stabilization optimization
    rf_savings, rf_time = sg.simulate_rf_stabilization_optimization()
    
    print(f"\n=== Optimization Summary ===")
    print(f"Configuration time savings: {config_savings:.2f}s per setup")
    print(f"RF stabilization time savings: {rf_savings:.1f}s per activation")
    print(f"Frequency accuracy maintained: {freq_results['accuracy_percentage']:.1f}% within spec")
    print(f"Test rate improvement: {freq_results['test_rate']:.1f} frequencies/second")
    
    sg.close()


if __name__ == "__main__":
    test_signal_generator_optimization()
