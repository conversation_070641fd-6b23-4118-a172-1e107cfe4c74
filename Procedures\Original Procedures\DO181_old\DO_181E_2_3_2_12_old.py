# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Restoration of Power, Section ********
             
			 Apply the momentary power interruption sequence appropriate for the 
			 transponder environmental category as specified in DO-160G, Section 16.
			 Two seconds after the restoration of power following each power
			 interruption, interrogate the transponder with a Mode S Only All-Call with
			 PR, IC and CL fields set to Zero.  Verify that a correct All-Call reply
			 (DF-11) is transmitted in response to this interrogation.
             
             
INPUTS:      ATC,PowerSupply,Top_Cable_Loss,Bottom_Cable_Loss
OUTPUTS:     Reply_Rates - array of reply rates, ATCRBS and ModeS (top/bottom)
             "do181_2_3_2_12.log"

HISTORY:

04/27/2020   MRS    Initial Release.
05/11/2020   MRS    CleanUp.
                                 
"""

#Required Libraries
import pyvisa
import time

#Map to Common Resource Folder
import sys
sys.path.append("../../common")
import atc
import powersupply

#Message to ClientObserver
import clr

##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_12(atc,pwr,top_loss,bot_loss):
    """ DO-181E, Restoration of Power: Sect ******** """
    
    print ("*** DO-181E, Restoration of Power: Sect ******** ***\r\n")
    
    clr.AddReference("C:\Program Files\Honeywell\Lobster4\ExternalLibraries\Observer\Interface") 
    from Honeywell.Interface import ObserverClientWrapper    
    tvl = ObserverClientWrapper()      
    tvl.SendMessageToObserver("0",0,"*Test_2_3_2_12","Start")   
    

    #Results read by TestStand
    Reply_Rates = [0.0,0.0,0.0,0.0]                                #Values read by TestStand
    
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    atc.set_cable_loss(str(top_loss), str(bot_loss))
	
    #Set Up Transponder -- ModeS
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF   
    tvl.SendMessageToObserver("0",0,"Test_2_3_2_12 ModeS","Begin Power Dropout test")   
        
    #Turn On RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    atc.waitforstatus()

    #Start Data logging
    atc.data_log_start()
    
	#Toggle LRU Power
    pwr.setOuput("OFF")
    time.sleep(.10)
    pwr.setOuput("ON")

	#Wait 2 seconds
    time.sleep(2)
	
	#Verify we are receiving ModeS Replies
    Reply_Rates = atc.getPercentReply(2)
    # fix for erroneous reply rate
    count = 0
    while Reply_Rates[1] == -1.0 and count < 10:
        Reply_Rates = atc.getPercentReply(2)
        count = count + 1
    print("Reply Rate: ",Reply_Rates)
            
    #Stop DataLogging
    atc.data_log_stop("do181_2_3_2_12.log")
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    tvl.SendMessageToObserver("0",0,"Test_2_3_2_12","Done")    

    
    print ("Done, closing session")
    
    return Reply_Rates


#run as main from command line
if __name__ == "__main__":
    rm = pyvisa.ResourceManager()
    #Initiazlie the ATC
    atc_obj = atc.ATC(rm)
    atc_obj.Reset()
	
	#Set Up Power Supply
    pwr_obj = powersupply.Powersup(rm)
    pwr_obj.Reset()
 

    print("*** IDNs ***")
    print(atc_obj.Ident())
    print(pwr_obj.Ident())
    
    res = Test_2_3_2_12(atc_obj,pwr_obj,1.0,2.0)
    
    atc_obj.closeATC()
    #pwr_obj.close

