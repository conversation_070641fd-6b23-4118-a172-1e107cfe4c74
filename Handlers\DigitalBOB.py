'''Instrument class to control muxes and additional settings on the 
TXD RF Digital Breakout Board, mainly by interfacing with the
onboard FPGA.

Before using this class, check the following has been
done on the target board to ensure desired operation:

1. The latest version of the FPGA software is loaded on the target board.
2. The FTDI chip's serial number on the board is programmed to DIGITALBOB

'''
import serial, serial.tools.list_ports
import time, ftd2xx

class DigitalBOB:
    def __init__(self, ate_rm):
        self.log = ate_rm
        try:
            self.serialPort = serial.Serial(None, baudrate=115200, timeout=2)
        except serial.SerialException as e:
            self.log.logMessage(3, str(e) + ': Could not create Board Resource')
        else:
            self.log.logMessage(0, "Resource created")
        self.bitPortC = None
        self.bitPortB = None
        self.serialMask = 0x03
        self.bitMask = 0xff

    ''' Reads from FTDI buffer and converts into a integer list of bits
        NOTE: Intended as private function only, DO NOT USE OUTSIDE THIS FILE! '''
    def __readBuffer(self, numBytes, bitLength):
        res = self.serialPort.read(numBytes)
        bitList = [int(i) for i in ('{0:0' + str(bitLength) + 'b}').format(int.from_bytes(res, 'big'))]
        return bitList

    def messageStandard(self, message):
        self.log.logMessage(1, message)

    def messageSuccess(self, message):
        self.log.logMessage(0, message)

    def messageWarning(self, message):
        self.log.logMessage(2, message)

    def messageError(self, message):
        self.log.logMessage(3, message)

    ''' Returns current connected COM port '''
    def getCOMPort(self):
        return self.serialPort.name

    ''' Connect to Board '''
    def connect(self):
        # Find list of all FTDI ports, select COM number that corresponds to channel A
        ports = list(serial.tools.list_ports.comports())
        comPort = ""
        for p in ports:
            if p.manufacturer == "FTDI":
                if p.serial_number == "DIGITALBOBA":
                    comPort = p.device
                    break
        if comPort is "":
            self.log.logMessage(3, "Cannot find Digital Breakout Board")
            raise LookupError("Cannot find Digital Breakout Board (Is it connected?)")

        # Two separate ports for serial and bit-banging capability
        try:
            self.serialPort.port = comPort
            self.serialPort.open()
            # FTDI port B will default to output high on first two pins
            self.bitPortB = ftd2xx.openEx(b'DIGITALBOBB')
            self.bitPortB.setBitMode(0xff, 1)
            self.bitPortB.write(bytes.fromhex('03'))
            # FTDI port C will default to all output high pins
            self.bitPortC = ftd2xx.openEx(b'DIGITALBOBC')
            self.bitPortC.setBitMode(0xff, 1)
            self.bitPortC.write(bytes.fromhex('ff'))
            self.resetBoardFPGA()
            self.log.logMessage(0, "Digital Breakout Board connected to " + self.getCOMPort())
        except serial.SerialException as e:
            self.log.logMessage(3, str(e) + ': Could not connect to Digital Breakout Board COM Port')

    ''' Close ports '''
    def disconnect(self):
        if self.serialPort.is_open:
            self.serialPort.close()
        if (self.bitPortB != None):
            self.bitPortB.close()
        if (self.bitPortC != None):
            self.bitPortC.close()
        self.log.logMessage(2, "Digital Breakout Board disconnected")

    ''' Returns version number [major, minor] of Digital BOB FPGA code '''
    def getFPGAVersion(self):
        self.serialPort.write(bytes.fromhex('00 00'))
        time.sleep(0.05)
        self.serialPort.write(bytes.fromhex('01 00'))
        res = self.serialPort.read(2)
        self.log.logMessage(1, "FPGA Version: " + str(list(res)))
        return(list(res))
        
    ''' Returns status of input switches in a byte '''
    def getInputSwitchStatus(self):
        self.serialPort.write(bytes.fromhex('02 00'))
        res = self.__readBuffer(1, 8)
        self.log.logMessage(1, "Input Switches: " + str(res))
        return res

    ''' Sets observation signal to output on selected trigger port
        TriggerID must be between 1 and 8
        obsSignal must be between 0 and 15
        Returns new status from FPGA '''
    def setTriggerSignal(self, triggerID, obsSignal):
        triggerID = triggerID+2 #converts the triggerID to FPGA address
        if triggerID < 3 or triggerID > 10:
            self.log.logMessage(3, "Invalid trigger location ID: Must be between 1 and 8")
            return "Invalid Trigger Location"
        if obsSignal < 0 or obsSignal > 15:
            self.log.logMessage(3, "Invalid Observation signal value: Must be between 0 and 15")
            return "Invalid Observation Signal Value"
        frame = [0x20 + triggerID] + [obsSignal]
        self.serialPort.write(bytes(frame))
        res = self.serialPort.read(1)
        self.log.logMessage(1, "Trigger " + str(triggerID - 2) + " set to " + str(list(res)))
        return(list(res))

    ''' Returns current observation signal being output on specified trigger port '''
    def getTriggerStatus(self, triggerID):
        triggerID = triggerID+2 #converts the triggerID to FPGA address
        if triggerID < 3 or triggerID > 10: # Literal address of trigger registers
            self.log.logMessage(3, "Invalid trigger location ID: Must be between 1 and 8")
            return "Invalid Trigger Location"
        frame = [triggerID] + [0]
        self.serialPort.write(bytes(frame))
        res = self.serialPort.read(1)
        self.log.logMessage(1, "Trigger " + str(triggerID - 3) + "is at " + str(list(res)))
        return(list(res))

    ''' Sets source of observation data.
        Signals come from DSUB connector when '0', FPGA when '1' '''
    def setObsDataSource(self, source):
        if source < 0 or source > 1:
            self.log.logMessage(3, "Invalid observation source: Must be 0 or 1")
            return "Invalid observation source"
        frame = [0x2B] + [source]
        self.serialPort.write(bytes(frame))
        res = self.__readBuffer(1, 1)
        self.log.logMessage(1, "Observation Data Source set to value " + str(res))
        return res
    
    ''' Returns current observation data source '''
    def getObsDataSource(self):
        self.serialPort.write(bytes.fromhex('0B 00'))
        res = self.__readBuffer(1, 1)
        self.log.logMessage(1, "Observation Data Source currently at " + str(res))
        return res

    ''' Toggle reset line for 0.5 seconds to UUT's RF FPGA '''
    def resetRFFPGA(self):
        self.serialMask = self.serialMask & 0xFE
        bytePacket = [0x2C] + [self.serialMask]
        self.serialPort.write(bytes(bytePacket))
        time.sleep(0.5)
        self.serialMask = self.serialMask | 0x01
        bytePacket = [0x2C] + [self.serialMask]
        self.serialPort.write(bytes(bytePacket))
        self.serialPort.read(1) # To clear buffer
        self.log.logMessage(1, "RF FPGA Reset Signal Sent")

    ''' Trigger RF FPGA to reprogram itself '''
    def reprogramRFFPGA(self, active):
        if active == 1:
            self.serialMask = self.serialMask & 0xFD
            bytePacket = [0x2C] + [self.serialMask]
            self.serialPort.write(bytes(bytePacket))
            self.log.logMessage(1, "RF FPGA Reprogram Signal Activated")
        else:
            self.serialMask = self.serialMask | 0x02
            bytePacket = [0x2C] + [self.serialMask]
            self.serialPort.write(bytes(bytePacket))
            self.serialPort.read(1) # to clear buffer
            self.log.logMessage(1, "RF FPGA Reprogram Signal Deactivated")
        
    def setRFSuppression(self, val):
        if val < 0 or val > 1:
            self.log.logMessage(3, "Illegal RF Suppression value sent, must be 0 or 1")
        elif val == 0:
            self.serialMask = self.serialMask & 0xFB
            bytePacket = [0x2C] + [self.serialMask]
            self.serialPort.write(bytes(bytePacket))
            self.serialPort.read(1) # to clear buffer
            self.log.logMessage(1, "RF Suppression set to " + str(val))
        else:
            self.serialMask = self.serialMask | 0xFB
            bytePacket = [0x2C] + [self.serialMask]
            self.serialPort.write(bytes(bytePacket))
            self.serialPort.read(1) # to clear buffer
            self.log.logMessage(1, "RF Suppression set to " + str(val))
        
    ''' Return status of RF UUT signals controllable by this board
        bit 2 - Enable RF_SUP_EN
        bit 1 - Enable RF_PROGRAM_INIT
        bit 0 - Reset UUT's RF FPGA '''
    def getRFControlStatus(self):
        self.serialPort.write(bytes.fromhex('0C 00'))
        res = self.__readBuffer(1, 3)
        self.log.logMessage(1, "RF Control Status: " + str(res))
        return res

    ''' Reset Digital BOB FPGA to default settings '''
    def resetBoardFPGA(self):
        self.bitMask = self.bitMask & 0xfe
        bytePacket = [self.bitMask]
        self.bitPortC.write(bytes(bytePacket))
        time.sleep(0.5)
        self.bitMask = self.bitMask | 0x01
        bytePacket = [self.bitMask]
        self.bitPortC.write(bytes(bytePacket))
        self.log.logMessage(2, "DigitalBOB FPGA reset")

    ''' Write hexadecimal byte to board's 2-digit 7 segment display '''
    def setLEDHexValue(self, hexVal):
        if hexVal < 0 or hexVal > 255:
            self.log.logMessage(3, "Display value must be between 0x00 and 0xff")
            raise ValueError("Display value must be between 0x00 and 0xff")
        frame = [0x2D] + [hexVal]
        self.serialPort.write(bytes(frame))
        res = self.serialPort.read(1)
        self.log.logMessage(1, "LED Hex value set to " + str(list(res)))
        return(list(res))

    ''' Return hexadecimal value currently displayed on 7 segment display '''
    def getLEDHexValue(self):
        self.serialPort.write(bytes.fromhex('0D 00'))
        res = self.serialPort.read(1)
        self.log.logMessage(1, "LED Hex value is " + str(list(res)))
        return(list(res))
    
    ''' Enable/Disable RS-485 port '''
    def setRS485State(self, state):
        if state is 0: # Disable port
            frame = [0x2F] + [0x0A]
            self.log.logMessage(1, "RS-485 port disabled")
        else: # Enable Port
            frame = [0x2F] + [0x05]
            self.log.logMessage(1, "RS-485 port enabled")
        self.serialPort.write(bytes(frame))
        res = self.__readBuffer(1, 4)
        return res

    ''' Return enable state of RS-485 port'''
    def getRS485State(self):
        self.serialPort.write(bytes.fromhex('0F 00'))
        res = self.__readBuffer(1, 4)
        self.log.logMessage(1, "RS-485 port state: " + str(res))
        return res

    ''' Set Aux Trig FPGA State
        bit 1 - Set Aux_Trig_FPGA_0
        bit 0 - Set Aux_Trig_FPGA_1 '''
    def setAuxTrigFPGA(self, state):
        frame = [0x30] + [state]
        self.serialPort.write(bytes(frame))
        res = self.__readBuffer(1, 2)
        self.log.logMessage(1, "setAuxTrigFPGA called: " + str(res))
        return res

    ''' Return Aux Trig FPGA States '''
    def getAuxTrigFPGA(self):
        self.serialPort.write(bytes.fromhex('10 00'))
        res = self.__readBuffer(1, 2)
        self.log.logMessage(1, "getAuxTrigFPGA called: " + str(res))
        return res

    ''' Return status of RF signals 
        bit 4 - RF Suppression Out
        bit 3 - RF ATE Probe B Direction
        bit 2 - RF ATE Probe B
        bit 1 - RF ATE Probe A
        bit 0 - RF FPGA Good '''
    def getRFStatus(self):
        self.serialPort.write(bytes.fromhex('11 00'))
        res = self.__readBuffer(1, 5)
        self.log.logMessage(1, "RF Status: " + str(res))
        return res

    ''' Return status of DME Signals
        bit 1 - DME Audio
        bit 0 - DME Enable line '''
    def getDMEStatus(self):
        self.serialPort.write(bytes.fromhex('12 00'))
        res = self.__readBuffer(1, 2)
        self.log.logMessage(1, "DME Status: " + str(res))
        return res

    ''' Return status of IRQ Signals 
        bit 2 - IRQ2_N
        bit 1 - IRQ1_N
        bit 0 - IRQ0_N '''
    def getIRQStatus(self):
        self.serialPort.write(bytes.fromhex('13 00'))
        res = self.__readBuffer(1, 3)
        self.log.logMessage(1, "IRQ Status: " + str(res))
        return res

    ''' Return status of all 13 RF observation signal lines.'''
    def getRFObsData(self):
        # Data stored in two registers in FPGA, grab MSByte then LSByte
        self.serialPort.write(bytes.fromhex('15 00'))
        time.sleep(0.05)
        self.serialPort.write(bytes.fromhex('14 00'))
        res = self.__readBuffer(2, 13)
        self.log.logMessage(1, "RF Observation Data: " + str(res))
        return res

    ''' Toggle reset line of Xilinx ZCU102 board '''
    def resetZCU102Board(self):
        # No masking since we only use bit 1 and 0 simultaneously on FTDI Port B
        self.bitPortB.write(bytes.fromhex('00'))
        time.sleep(0.5)
        self.bitPortB.write(bytes.fromhex('03'))
        self.log.logMessage(2, "Xilinx ZCU102 board reset sent")