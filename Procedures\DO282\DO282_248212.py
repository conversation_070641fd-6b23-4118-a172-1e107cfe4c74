# -*- coding: utf-8 -*-
"""
SCRIPT IDENTIFIER:  DO282_248212.py

MODULE HISTORY:
        
AUTHOR: H157797

MODULE DESCRIPTION:
  This script creates the test scenario and sends the commands to RF generator like 
  ATC5000NG for DO-282 Section *******.1.2 Verification of Basic ADS-B Message As Desired Signal.

    Provide the UUT with ADS-B Messages having:
    . RF Power Level: -94 dBm
    . Center Frequency: 978 MHz +/- 2.0 kHz +/-19.560 kHz
    . FM Deviation: 560 kHz (measured at the minimum eye pattern opening per $*******)
    . Message Contents: Basic ADS-B Message with pseudo-random payload data, and valid FEC Parity field.
    . Message Rate: 100 per second
  
DEPENDENCY:
   Python v3.6 or above
   UAT_CONNECTION.py
   FEC.py

NOTE:
   The cable line loss need to be considered. The loss of the message power levels could be set by 
   this script's input argument. By default, it is set to 3db (possitive) assuming the cable line 
   loss is 3db.

HONEYWELL DIFFERENCE/DEVIATION: N/A

"""

import os
import sys
import time
import datetime
#import argparse
import csv

from TXDLib.Handlers import ate_rm

from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc
from TXDLib.Procedures.DO282 import FEC as fec
from TXDLib.Procedures.DO282 import UAT_LOGGING as ul



#parser = argparse.ArgumentParser()
#parser = argparse.ArgumentParser(description='DO-282 *******.1.1 Test scenario script')
#parser.add_argument("-l", "--lineloss", default=3, type=int, help="Cable Line loss, unit: db")
#args = parser.parse_args()

#Cable line loss by default 3db
CABLE_LINE_LOSS = 3.0
INPUT_FILE_PATH = "DO282_248212_INPUT"

BasicADSB =''
fecOfBasicADSB = ''

#Apply ADS-B Input Messages at maximum negative frequency offset and maximum positive frequency offset
#Parameters:
#   channelSel: 1: UAT RX1; 2: RX2 
#   powerValue: the power level of the squitter messages 
#   shiftValue: the shift for the Doppler Test.
def test_case(channelSel=None, powerValue=None, shiftValue=None,inputLogFile=None):
    f = open(inputLogFile,'w',encoding='utf-8',newline='')
    
    if (channelSel != 1 and channelSel != 2):
        sys.exit("Select the UAT RX Channel: 1 for RX1; 2 for RX2")
   
    #initial the UAT scenario
    uc.set_scenario(reset=True, channel=channelSel, run_time=10, test_mode_type="Doppler_modulation", mso_step=30)
    
    #Set the scenario page for the UAT Doppler and Modulation Frequency
    uc.set_doppler_modulation_freq(frequency=21.56, shift=shiftValue, modulation=280)
    
    #100 Basic ADS-B Message
    try:
        csvWriter = csv.writer(f)
        csvWriter.writerow(['SYSTEM TIME','MSG ID','MESSAGE'])
        for i in range(100):
            BasicADSB = uc.generate_random_uat_msg(fec.BASIC_ADSB_MSG_TYPE)
            fecOfBasicADSB = fec.fec_parity(fec.BASIC_ADSB_MSG_TYPE,BasicADSB)
            csvWriter.writerow([datetime.datetime.now().strftime('%H:%M:%S.%f'),i+1,BasicADSB+fecOfBasicADSB])
            uc.set_sta_basic_adsb(intruderNumber=i+1, data = BasicADSB, fec=fecOfBasicADSB, power=powerValue)
    finally:
        f.close()
    
    uc.scenario_start(start=1)

    # Wait for scenario loading into RF generator with status polling
    def wait_for_scenario_ready(timeout=60, poll_interval=1):
        """Wait for scenario to be ready with polling instead of fixed delay"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # Check if scenario is ready by querying status
                # Using a simple status check - if no exception, scenario is likely ready
                uc.send(":ATC:STATUS?\r\n")
                time.sleep(poll_interval)
                # If we get here without exception, scenario is ready
                return True
            except:
                time.sleep(poll_interval)
        # If timeout reached, fall back to remaining time of original delay
        remaining_time = max(0, 50 - (time.time() - start_time))
        if remaining_time > 0:
            time.sleep(remaining_time)
        return False

    # Optimized scenario loading wait (was 50s, now adaptive with 60s max)
    wait_for_scenario_ready(timeout=60, poll_interval=1)
      


def basic_adsb_rxx(rm,RFPathLoss_Top,RFPathLoss_Bot):

    inputFile = os.getcwd()+"\\"+INPUT_FILE_PATH
    ul.mkdir(inputFile)
    caseId = 0
   
    #connect to the ATC target
    uc.create_connection()
    # set mode to UAT mode
    uc.set_test_mode("UAT")

    CABLE_LINE_LOSS = RFPathLoss_Top

    #@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    #TBD:  pwrList may be changed to determine the real minimum RF signal required to produce 
    #      an average reception rate of 90 percent by the UUT receiver for MOPS Testing when TXD
    #      LRU is available.
    #@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@    
    #pwrList = [-96, -95, -94, -93, -92]
    pwrList = [-96, -94, -93, -92]

    for rxNo in range(1,3):
        #Set Cable Loss to RF PathLoss
        if (rxNo == 1): 
            CABLE_LINE_LOSS = RFPathLoss_Top
        else:
            CABLE_LINE_LOSS = RFPathLoss_Bot
        caseId += 1

        rm.logMessage(2,"-----------------------------------------------------------------------------------------")
        rm.logMessage(2,"Running Test Case #" + str(caseId) + " On UAT RX" + str(rxNo))
        rm.logMessage(2,"-----------------------------------------------------------------------------------------")
        rm.logMessage(0,"   Apply the Desired Message Signal with the Center Frequency set to the minimum value:")
        rm.logMessage(0,"         978 MHz - 21.56 kHz")
        rm.logMessage(0,"   Modulation Distortion:  560 Khz")
        rm.logMessage(0,"   100 Message Content with pseudo-random payload data and valid FEC")

        stepId = 1
        for pwrLevel in pwrList:
            printMsg = "Running step " + str(stepId) +" - RF Power Level: " + str(pwrLevel) + " plus " + str(CABLE_LINE_LOSS) + "db line lose"
            rm.logMessage(0,printMsg)
            inputFilePath = inputFile+'\\CASE_'+str(caseId)+'_RX_'+str(rxNo)+'_step'+str(stepId)+'.csv'
            test_case(channelSel=rxNo, powerValue=pwrLevel + CABLE_LINE_LOSS, shiftValue='-',inputLogFile=inputFilePath)
            stepId = stepId + 1

        rm.logMessage(0,"Sleep 10 secs...")
        time.sleep(10)

        caseId += 1
        rm.logMessage(2,"-----------------------------------------------------------------------------------------")
        rm.logMessage(2,"Running Test Case #" + str(caseId) + " On UAT RX" + str(rxNo))
        rm.logMessage(2,"-----------------------------------------------------------------------------------------")
        rm.logMessage(0,"   Apply the Desired Message Signal with the Center Frequency set to the maximum value:")
        rm.logMessage(0,"         978 MHz + 21.56 kHz")
        rm.logMessage(0,"   Modulation Distortion:  560 Khz")
        rm.logMessage(0,"   100 Messages per second with pseudo-random payload data and valid FEC")

        stepId = 1
        for pwrLevel in pwrList:
            printMsg = "Running step " + str(stepId) +": RF Power Level: " + str(pwrLevel) + " plus " + str(CABLE_LINE_LOSS) + "db line lose"
            rm.logMessage(0,printMsg)
            inputFilePath = inputFile+'\\CASE_'+str(caseId)+'_RX_'+str(rxNo)+'_step'+str(stepId)+'.csv'
            test_case(channelSel=rxNo, powerValue=pwrLevel + CABLE_LINE_LOSS, shiftValue='+',inputLogFile=inputFilePath)
            stepId = stepId + 1


        rm.logMessage(0,"Sleep 10 secs...")
        time.sleep(10)
    
    #Close connection to ATC
    uc.close_connection()
    rm.logMessage(2,"Done, Closing Session.") 


if __name__ == '__main__':
   
    #SetUP Resource Manager
    rm = ate_rm()


    basic_adsb_rxx(rm,12.5, 12.69)
