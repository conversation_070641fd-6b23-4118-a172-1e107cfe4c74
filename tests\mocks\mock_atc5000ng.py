#!/usr/bin/env python3
"""
Mock ATC5000NG Handler for Testing
Simulates the behavior of the real ATC5000NG transponder test set
"""

import time
import random
from unittest.mock import Mock

class MockATC5000NG:
    """Mock implementation of ATC5000NG handler"""
    
    def __init__(self, resource_manager=None):
        self.resourceManager = resource_manager or Mock()
        self.connected = False
        self.status = "READY"
        self.last_command = ""
        self.scenario_loaded = False
        self.rf_on = False
        
        # Simulated measurement values
        self.pulse_frequency = 1030.0
        self.pulse_width = 3.5e-6
        self.pulse_position = 0.0
        self.rise_time = 2.5e-6
        self.fall_time = 3.0e-6
        self.mode_a_reply = 7777
        self.mode_c_reply = 1200
        
        # Timing simulation
        self.reset_time = 0.1  # Simulated reset time
        self.command_delay = 0.01  # Simulated command processing delay
        
    def connect(self, address):
        """Simulate connection to ATC"""
        time.sleep(0.1)  # Simulate connection time
        self.connected = True
        self.resourceManager.logMessage(1, f"Mock ATC connected to {address}")
        return True
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        self.resourceManager.logMessage(1, "Mock ATC disconnected")
        
    def write(self, command):
        """Simulate writing command to ATC"""
        if not self.connected:
            raise Exception("ATC not connected")
            
        time.sleep(self.command_delay)
        self.last_command = command
        self.resourceManager.logMessage(1, f"Mock ATC command: {command}")
        
        # Simulate command effects
        if ":ATC:RESET" in command:
            self.status = "RESETTING"
        elif ":ATC:XPDR:RF ON" in command:
            self.rf_on = True
        elif ":ATC:XPDR:RF OFF" in command:
            self.rf_on = False
            
    def gwrite(self, command):
        """Alias for write method"""
        self.write(command)
        
    def query(self, command):
        """Simulate querying ATC for data"""
        if not self.connected:
            raise Exception("ATC not connected")
            
        time.sleep(self.command_delay)
        self.resourceManager.logMessage(1, f"Mock ATC query: {command}")
        
        # Simulate responses based on command
        if ":ATC:MEASure:FREQ?" in command:
            return str(self.pulse_frequency + random.uniform(-0.1, 0.1))
        elif ":ATC:MEASure:PULSE:WIDTH?" in command:
            return str(self.pulse_width + random.uniform(-0.1e-6, 0.1e-6))
        elif ":ATC:MEASure:PULSE:POS?" in command:
            return str(self.pulse_position + random.uniform(-0.1e-6, 0.1e-6))
        elif ":ATC:MEASure:PULSE:RISE?" in command:
            return str(self.rise_time + random.uniform(-0.1e-6, 0.1e-6))
        elif ":ATC:MEASure:PULSE:FALL?" in command:
            return str(self.fall_time + random.uniform(-0.1e-6, 0.1e-6))
        elif ":ATC5000NG:XPDR:AREPLY?" in command:
            return str(self.mode_a_reply)
        elif ":ATC5000NG:XPDR:CREPLY?" in command:
            return str(self.mode_c_reply)
        elif ":ATC:STATUS?" in command:
            return "READY" if self.status == "READY" else "BUSY"
        else:
            return "OK"
            
    def delayquery(self, command):
        """Simulate delayed query (used for some measurements)"""
        time.sleep(0.05)  # Slightly longer delay
        return self.query(command)
        
    def Reset(self):
        """Simulate ATC reset with optimized timing"""
        self.resourceManager.logMessage(1, "Mock ATC Reset starting")
        self.status = "RESETTING"
        
        # Simulate the optimized reset time (8s instead of 15s)
        time.sleep(self.reset_time)
        
        self.status = "READY"
        self.resourceManager.logMessage(1, "Mock ATC Reset complete")
        
    def waitforstatus(self):
        """Simulate waiting for ATC to be ready"""
        timeout = 30  # 30 second timeout
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.status == "READY":
                return True
            time.sleep(0.1)
            
        raise Exception("Timeout waiting for ATC status")
        
    def getPulseFrequency(self, repeat_count):
        """Simulate getting pulse frequency with optimized retry timing"""
        for attempt in range(repeat_count):
            try:
                # Simulate the optimized communication delay (0.5s instead of 1s)
                time.sleep(0.05)  # Simulated measurement time
                
                # Add some realistic variation
                freq = self.pulse_frequency + random.uniform(-0.05, 0.05)
                self.resourceManager.logMessage(1, f"Mock Pulse Frequency: {freq}")
                return freq
                
            except Exception as e:
                if attempt < repeat_count - 1:
                    # Simulate optimized retry delay (0.5s instead of 1s)
                    time.sleep(0.05)
                    continue
                else:
                    raise e
                    
        return 0.0
        
    def getPulseWidth(self, repeat_count):
        """Simulate getting pulse width"""
        for attempt in range(repeat_count):
            try:
                time.sleep(0.05)
                width = self.pulse_width + random.uniform(-0.01e-6, 0.01e-6)
                self.resourceManager.logMessage(1, f"Mock Pulse Width: {width}")
                return width
            except Exception as e:
                if attempt < repeat_count - 1:
                    time.sleep(0.05)
                    continue
                else:
                    raise e
        return 0.0
        
    def getPulsePosition(self, repeat_count):
        """Simulate getting pulse position"""
        for attempt in range(repeat_count):
            try:
                time.sleep(0.05)
                pos = self.pulse_position + random.uniform(-0.01e-6, 0.01e-6)
                self.resourceManager.logMessage(1, f"Mock Pulse Position: {pos}")
                return pos
            except Exception as e:
                if attempt < repeat_count - 1:
                    time.sleep(0.05)
                    continue
                else:
                    raise e
        return 0.0
        
    def getRiseTime(self, repeat_count):
        """Simulate getting rise time"""
        for attempt in range(repeat_count):
            try:
                time.sleep(0.05)
                rise = self.rise_time + random.uniform(-0.01e-6, 0.01e-6)
                self.resourceManager.logMessage(1, f"Mock Rise Time: {rise}")
                return rise
            except Exception as e:
                if attempt < repeat_count - 1:
                    time.sleep(0.05)
                    continue
                else:
                    raise e
        return 0.0
        
    def getFallTime(self, repeat_count):
        """Simulate getting fall time"""
        for attempt in range(repeat_count):
            try:
                time.sleep(0.05)
                fall = self.fall_time + random.uniform(-0.01e-6, 0.01e-6)
                self.resourceManager.logMessage(1, f"Mock Fall Time: {fall}")
                return fall
            except Exception as e:
                if attempt < repeat_count - 1:
                    time.sleep(0.05)
                    continue
                else:
                    raise e
        return 0.0
        
    def getModeAReply(self, repeat_count):
        """Simulate getting Mode A reply"""
        for attempt in range(repeat_count):
            try:
                time.sleep(0.05)
                reply = self.mode_a_reply
                self.resourceManager.logMessage(1, f"Mock Mode A Reply: {reply}")
                return reply
            except Exception as e:
                if attempt < repeat_count - 1:
                    time.sleep(0.05)
                    continue
                else:
                    raise e
        return -1.0
        
    def getModeCReply(self, repeat_count):
        """Simulate getting Mode C reply"""
        for attempt in range(repeat_count):
            try:
                time.sleep(0.05)
                reply = self.mode_c_reply
                self.resourceManager.logMessage(1, f"Mock Mode C Reply: {reply}")
                return reply
            except Exception as e:
                if attempt < repeat_count - 1:
                    time.sleep(0.05)
                    continue
                else:
                    raise e
        return -1.0
        
    def transponderMode(self):
        """Simulate setting transponder mode"""
        self.write(":ATC:SCE:TYPE XPDR")
        self.write(":ATC:SET:FACT XPDR")
        self.write(":ATC:XPDR:RES")
        self.resourceManager.logMessage(0, "Mock Transponder Mode")
        time.sleep(0.1)  # Simulated mode change time
        
    def transponderModeA(self):
        """Simulate setting transponder Mode A with optimized micro-delays"""
        # Simulate the optimized batched configuration (0.05s instead of 7×0.03s)
        commands = [
            ":ATC:MEA:DFORMAT 2",
            ":ATC:XPDR:TYPE 0",
            ":ATC:XPDR:MOD 0",
            ":ATC:XPDR:FREQ 1030.00",
            ":ATC:XPDR:PRF 400",
            ":ATC:XPDR:POW -21",
            ":ATC:MEA:SET:PUL 0",
            ":ATC:MEA:SET:TRIG:ANT BOTTOM"
        ]
        
        for cmd in commands:
            self.write(cmd)
            
        # Simulate optimized single delay (0.05s instead of 0.21s)
        time.sleep(0.01)
        self.resourceManager.logMessage(0, "Mock Transponder Mode A")
        time.sleep(0.1)  # Final settling time
        
    def DMEMode(self):
        """Simulate setting DME mode"""
        self.write(":ATC:SCE:TYPE DME")
        self.resourceManager.logMessage(0, "Mock DME Mode")
        time.sleep(0.1)
        
    def is_number(self, s):
        """Check if string is a valid number"""
        try:
            float(s)
            return True
        except ValueError:
            return False
