package main

import (
	"bufio"
	"fmt"
	"net"
	"os"
	"strings"
	"time"
)

// 
const (
	ip          = "***********" // Replace with the target IP
	port        = "23"          // Replace with the target Port
	command     = "dir"         // Replace with the command to execute
	startMarker = "CURRENT FAULTS:"
	endMarker   = "End Of Faults"
)

/**
 * main function initiates the process of connecting, sending command, reading response,
 * and appending output in a loop every minute.
 */
func main() {
	for {
		output, err := executeCommand(ip, port, command)
		if err != nil {
			fmt.Println("Error:", err)
			time.Sleep(1 * time.Minute)
			continue
		}

		appendOutput(output)

		// Wait for 1 minute before the next execution
		time.Sleep(1 * time.Minute)
	}
}

/**
 * executeCommand establishes a Telnet connection, sends a command, and reads the response.
 * It returns the relevant output and any error encountered.
 *
 * @param ip - the target IP address
 * @param port - the target port
 * @param command - the command to execute
 * @return output - a slice of strings containing the relevant output
 * @return err - any error encountered during the process
 */
func executeCommand(ip, port, command string) ([]string, error) {
	// Establish the Telnet connection
	conn, err := net.Dial("tcp", ip+":"+port)
	if err != nil {
		return nil, fmt.Errorf("Error connecting: %v", err)
	}
	defer conn.Close()

	// Send the command
	_, err = conn.Write([]byte(command + "\n"))
	if err != nil {
		return nil, fmt.Errorf("Error sending command: %v", err)
	}

	// Read the response
	scanner := bufio.NewScanner(conn)
	var output []string
	inFaultSection := false

	for scanner.Scan() {
		line := scanner.Text()
		if strings.Contains(line, startMarker) {
			inFaultSection = true
		}
		if inFaultSection {
			output = append(output, line)
		}
		if strings.Contains(line, endMarker) {
			inFaultSection = false
			break
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("Error reading response: %v", err)
	}

	return output, nil
}

/**
 * appendOutput appends the given output to a file named "output.txt".
 * It also adds a separator line "--------" after the output.
 *
 * @param output - a slice of strings containing the output to be appended to the file
 */
func appendOutput(output []string) {
	file, err := os.OpenFile("output.txt", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Println("Error opening file:", err)
		return
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	for _, line := range output {
		_, err := writer.WriteString(line + "\n")
		if err != nil {
			fmt.Println("Error writing to file:", err)
			return
		}
	}

	// Add the "--------" line
	_, err = writer.WriteString("--------\n")
	if err != nil {
		fmt.Println("Error writing to file:", err)
		return
	}

	err = writer.Flush()
	if err != nil {
		fmt.Println("Error flushing to file:", err)
		return
	}
}
