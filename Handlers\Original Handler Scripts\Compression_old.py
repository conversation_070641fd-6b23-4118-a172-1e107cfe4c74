# GB and CS 8/4/2022 for PPQ added setupPwrMeterXPDRModeC
# CS added for PPQ - setupPwrMeterXPDRModeS and setupPwrMeterDME

import visa, time, math
import datetime
import csv
from time import localtime, strftime
from TXDLib.Procedures import Calibration
from TXDLib.Procedures import CSVFile
import os
import numpy

    
# CSV Columns
PartNumber = []
Date = []
Time = []
Type = []
SerialNumber = []
E1P1Measurement = []
E1P2Measurement = []
E2P1Measurement = []
E2P2Measurement = []
Measurement = []
WSSetting = []
P1WSSetting = []
P2WSSetting = []
FrequencySetting = []
Directory = []
Phase  = []
PassFail  = []
Result = []
Highspec  = []
Lowspec   = []
Unit = []

E1_Power = []
E2_Power = []

DIRECTORY = 'n/a'
UNIT = 'dBm'

TYPE = 'J1 Output 20200702A_Setup'
PART_NUMBER = 'P01'
SERIAL_NUMBER = 'SN5'

E1_START_FREQ = 1025
E1_END_FREQ = 1150
E1_FREQ_INCREMENT = 1
E2_START_FREQ = 1030
E2_END_FREQ = 1030
E2_FREQ_INCREMENT = 10
WS_FREQUENCY_RANGE_E1 = numpy.arange(E1_START_FREQ, E1_END_FREQ + E1_FREQ_INCREMENT, E1_FREQ_INCREMENT)
WS_FREQUENCY_RANGE_E2 = numpy.arange(E2_START_FREQ, E2_END_FREQ + E2_FREQ_INCREMENT, E2_FREQ_INCREMENT)
WS_MIN = 0
WS_MAX = -30
WS_STEP = -0.5
wssteps = numpy.arange(WS_MIN, WS_MAX + WS_STEP, WS_STEP)

def main(aterm):
    print('Index: ' + str(wssteps[wssteps == -1]))
    SLEEP_BEFORE_MEAS = 0.5 #seconds
    SLEEP_AFTER_MEAS = 0.5 #seconds
    '''Calibration pulse switch settings:
        top_antenna:
            1, connect TX to top antenna
            0, connect TX to bot antenna
        cal_brkt_en_e1_p1:
            1: Enable P1 pulse on E1
            0: Disable P1 pulse on E1
        cal_brkt_en_e1_p2:
            1: Enable P2 pulse on E1
            0: Disable P2 pulse on E1
        cal_brkt_en_e2_p1:
            1: Enable P1 pulse on E2
            0: Disable P1 pulse on E2
        cal_brkt_en_e2_p2:
            1: Enable P2 pulse on E2
            0: Disable P2 pulse on E2
        high_power:
            1, activate high-power transmit monitor path on both P1 and P2.
            0, activate low-power transmit monitor path on both P1 and P2.
        P1_monitor:
            0: No connect transmit monitor for P1 pulse.
            1: Connect transmit monitor to E1 for P1 pulse.
            2: Connect transmit monitor to E2 for P1 pulse.
        P2_monitor:
            0: No connect transmit monitor for P2 pulse.
            1: Connect transmit monitor to E1 for P2 pulse.
            2: Connect transmit monitor to E2 for P2 pulse.
        TX_E1_Mode_SW:
            Select Bank 0-4 of the E1 transmit mode switch.
        TX_E2_Mode_SW:
            Select Bank 0-1 of the E2 transmit mode switch. 0 = TCAS mode, 1 = RX 1090MHz Calibration
        Cal_Freq_Sel:
            0: TX 1090MHz CAL_FREQ_SEL, Selects proper DDC montior for Calibration
            1: TX 1030MHz CAL_FREQ_SEL, Selects proper DDC montior for Calibration
        Self_Test:
            0: P1 and P2 pulse cycle
            1: SELF-TEST Loopback sequence'''
    top_antenna = 1
    high_power = 1
    cal_brkt_en_e1_p1 = 1
    cal_brkt_en_e1_p2 = 1
    cal_brkt_en_e2_p1 = 1
    cal_brkt_en_e2_p2 = 1
    P1_monitor = 1
    P2_monitor = 2
    TX_E1_Mode_SW = 1 #over-written below depending on frequency of transmission
    TX_E2_Mode_SW = 0
    Cal_Freq_Sel = 1
    Self_Test = 0


    # Calibration pulse RF settings:
    rfCalBurstSpacing = 1 #in millisconds, cannot be less than 1ms.
    rfCalBurstNumber = 10 #number of pulse pairs to send
    E1_P1_Phase = 0
    E1_P2_Phase = 0
    E2_P1_Phase = 0
    E2_P2_Phase = 0
    DUC_MHz = 380
    LO_MHz = 800

    MODE0_RANGE = [1069, 1114] # 1090MHz RX Calibration
    MODE1_RANGE = [1025, 1042]
    MODE2_RANGE = [1042, 1069]
    MODE3_RANGE = [1069, 1114]
    MODE4_RANGE = [1114, 1150]


    full_data_file_name = 'C:\\Test Data\\TXD RF\\TX Power Curves\\' + PART_NUMBER + ' - ' +  SERIAL_NUMBER + ' - ' + TYPE + ' - ' + ' Full Data ' + strftime("%Y-%m-%d %H-%M-%S", localtime()) + '.csv'

    # ----ROUTINE START----
    print('Start')
    for Frequency_MHz in numpy.arange(min(E1_START_FREQ, E2_START_FREQ), max(E1_END_FREQ, E2_END_FREQ)+1, min(E1_FREQ_INCREMENT, E2_FREQ_INCREMENT)):
        # Clear CSV columns
        PartNumber = []
        Date = []
        Time = []
        Type = []
        SerialNumber = []
        E1P1Measurement = []
        E1P2Measurement = []
        E2P1Measurement = []
        E2P2Measurement = []
        Measurement = []
        FrequencySetting = []
        Directory = []
        Phase  = []
        PassFail  = []
        Result = []
        Highspec  = []
        Lowspec   = []
        Unit = []

        E1_Power = []
        E2_Power = []

        # Setup E1 Mode Switch:
        if Frequency_MHz >= MODE1_RANGE[0] and Frequency_MHz <= MODE1_RANGE[1]:
            TX_E1_Mode_SW = 1
            print('   E1 TX Mode Switch = ' + str(TX_E1_Mode_SW) + ', Frequency Range: (' + str(MODE1_RANGE[0]) + 'MHz - ' + str(MODE1_RANGE[1]) + 'MHz)')
        elif Frequency_MHz >= MODE2_RANGE[0] and Frequency_MHz <= MODE2_RANGE[1]:
            TX_E1_Mode_SW = 2
            print('   E1 TX Mode Switch = ' + str(TX_E1_Mode_SW) + ', Frequency Range: (' + str(MODE2_RANGE[0]) + 'MHz - ' + str(MODE2_RANGE[1]) + 'MHz)')
        elif Frequency_MHz >=MODE3_RANGE[0] and Frequency_MHz <= MODE3_RANGE[1]:
            TX_E1_Mode_SW = 3
            print('   E1 TX Mode Switch = ' + str(TX_E1_Mode_SW) + ', Frequency Range: (' + str(MODE3_RANGE[0]) + 'MHz - ' + str(MODE3_RANGE[1]) + 'MHz)')
        elif Frequency_MHz >=MODE4_RANGE[0] and Frequency_MHz <= MODE4_RANGE[1]:
            TX_E1_Mode_SW = 4
            print('   E1 TX Mode Switch = ' + str(TX_E1_Mode_SW) + ', Frequency Range: (' + str(MODE4_RANGE[0]) + 'MHz - ' + str(MODE4_RANGE[1]) + 'MHz)')

        # Only turn on transmitter brackets of the channel within this frequency range.
        if Frequency_MHz in WS_FREQUENCY_RANGE_E1:
            cal_brkt_en_e1_p1 = 1
            cal_brkt_en_e1_p2 = 1
            MeasureE1 = True
        else:
            cal_brkt_en_e1_p1 = 0
            cal_brkt_en_e1_p2 = 0
            MeasureE1 = False
        if Frequency_MHz in WS_FREQUENCY_RANGE_E2:
            cal_brkt_en_e2_p1 = 1
            cal_brkt_en_e2_p2 = 1
            MeasureE2 = True
        else:
            cal_brkt_en_e2_p1 = 0
            cal_brkt_en_e2_p2 = 0
            MeasureE2 = False

        # overwrite custom calibration signal configuration file with updated switch settings:
        Calibration.setCustomCalConfig(aterm, top_antenna, cal_brkt_en_e1_p1, cal_brkt_en_e1_p2, cal_brkt_en_e2_p1,
                                    cal_brkt_en_e2_p2, high_power, P1_monitor, P2_monitor, TX_E1_Mode_SW, 
                                    TX_E2_Mode_SW, Cal_Freq_Sel, Self_Test)

        print('Stepping through Transmit Attenuators')
        for WSAtten in numpy.arange(WS_MIN, WS_MAX, 2*WS_STEP):
            # Start transmission sequence
            E1_P1_ws_dB = WSAtten
            E1_P2_ws_dB = WSAtten + WS_STEP
            E2_P1_ws_dB = WSAtten
            E2_P2_ws_dB = WSAtten + WS_STEP
            Calibration.generateCalPulse(aterm, top_antenna, cal_brkt_en_e1_p1, cal_brkt_en_e1_p2, cal_brkt_en_e2_p1,
                                        cal_brkt_en_e2_p2, E1_P1_ws_dB, E1_P2_ws_dB, E2_P1_ws_dB, E2_P2_ws_dB, 
                                        E1_P1_Phase, E1_P2_Phase, E2_P1_Phase, E2_P2_Phase, Frequency_MHz, 
                                        DUC_MHz, LO_MHz, rfCalBurstSpacing, rfCalBurstNumber)
            # Start Measure Sequence
            time.sleep(SLEEP_BEFORE_MEAS)
            if MeasureE1 == True:
                E1P1Measurement.append(aterm.instruments["PwrMeter"].CH1_Marker_Power()[0])
                E1P2Measurement.append(aterm.instruments["PwrMeter"].CH1_Marker_Power()[1])
                E1_Power.append(E1P1Measurement[-1])
                E1_Power.append(E1P2Measurement[-1])

            if MeasureE2 == True:
                E2P1Measurement.append(aterm.instruments["PwrMeter"].CH2_Marker_Power()[0])
                E2P2Measurement.append(aterm.instruments["PwrMeter"].CH2_Marker_Power()[1])
                E2_Power.append(E2P1Measurement[-1])
                E2_Power.append(E2P2Measurement[-1])

            time.sleep(SLEEP_AFTER_MEAS)
        # Add test results to data:
        if MeasureE1 == True:
            i = 0
            for WSAtten in numpy.arange(WS_MIN, WS_MAX, 2*WS_STEP):
                E1_P1_ws_dB = WSAtten
                E1_P2_ws_dB = WSAtten + WS_STEP
                i+=1
            WS_Analysis(Frequency_MHz, E1_Power, wssteps)
        if MeasureE2 == True:
            i = 0
            for WSAtten in numpy.arange(WS_MIN, WS_MAX, 2*WS_STEP):
                E2_P1_ws_dB = WSAtten
                E2_P2_ws_dB = WSAtten + WS_STEP
                i+=1
            WS_Analysis(Frequency_MHz, E2_Power, wssteps)
        # Write CSV file
        with open('C:\\Test Data\\TXD RF\\TX Power Curves\\' + PART_NUMBER + ' - ' +  SERIAL_NUMBER + ' - ' + TYPE + ' - ' + str(Frequency_MHz) + 'MHz Power ' + strftime("%Y-%m-%d %H-%M-%S", localtime()) + '.csv','w', newline='') as csvfile:
            writer = csv.writer(csvfile, delimiter = ',')
            rows = zip(PartNumber, Date, Time, SerialNumber, Type, Measurement, WSSetting, FrequencySetting, Directory, PassFail, Result, Highspec, Lowspec, Unit)
            writer.writerows(rows)
        csvfile.close()
        # Write to full CSV file
        with open(full_data_file_name,'a+', newline='') as csvfile:
            writer = csv.writer(csvfile, delimiter = ',')
            rows = zip(PartNumber, Date, Time, SerialNumber, Type, Measurement, WSSetting, FrequencySetting, Directory, PassFail, Result, Highspec, Lowspec, Unit)
            writer.writerows(rows)
        csvfile.close()

    print('done')
    # ----ROUTINE OVER----

def setupPwrMeter(aterm):
    # Turn on both channels
    aterm.instruments["PwrMeter"].setCalculate1_on("On")
    aterm.instruments["PwrMeter"].setCalculate2_on("On")
    aterm.instruments["PwrMeter"].setCalculateMode("PULSE")
    aterm.instruments["PwrMeter"].setCalculateUnits('dBm')

    aterm.instruments["PwrMeter"].setFrequency1("1e+09") # 1 GHz
    aterm.instruments["PwrMeter"].setTimeBase("2e-6")
    aterm.instruments["PwrMeter"].setTriggerMode("NORMAL")
    aterm.instruments["PwrMeter"].setTriggerSource("TRIG1")
    aterm.instruments["PwrMeter"].setTriggerLevel(0.450)
    aterm.instruments["PwrMeter"].setTriggerSlope("NEG")
    aterm.instruments["PwrMeter"].setTriggerDelay(6.52e-6)
    aterm.instruments["PwrMeter"].basicWrite("SENSe2:CORRection:OFFSet 51")
    aterm.instruments["PwrMeter"].basicWrite("SENSe2:AVERAGE 1")
    aterm.instruments["PwrMeter"].basicWrite("DISPlay:TRACe1:VSCALE 10.00")
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:CORRection:OFFSet 51")
    aterm.instruments["PwrMeter"].basicWrite("DISPlay:TRACe1:VCENTer 35.00")
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:AVERAGE 1")
    aterm.instruments["PwrMeter"].basicWrite("MARKer1:ASSIgn ACTIVE")
    aterm.instruments["PwrMeter"].basicWrite("MARKer2:ASSIgn ACTIVE")
    aterm.instruments["PwrMeter"].basicWrite("MARKer1:POSition:PIXel 155")
    aterm.instruments["PwrMeter"].basicWrite("MARKer2:POSition:PIXel 355")
    aterm.instruments["PwrMeter"].basicWrite("MARKer:MATH:BOTH RATIO,MK1MK2,dBm")
    aterm.instruments["PwrMeter"].initiate_cont("ON")

def setupPwrMeterXPDRModeS(aterm):
    # Turn on both channels
    aterm.instruments["PwrMeter"].setCalculate1_on("On")# set for PPQ
    #aterm.instruments["PwrMeter"].setCalculate2_on("On")# set for PPQ
    aterm.instruments["PwrMeter"].setCalculateMode("PULSE")# set for PPQ
    aterm.instruments["PwrMeter"].setCalculateUnits('dBm')# set for PPQ

    aterm.instruments["PwrMeter"].setFrequency1("1.03e+09") # 1.03 GHz
    aterm.instruments["PwrMeter"].setTimeBase("5e-6")# set for PPQ
    aterm.instruments["PwrMeter"].setTriggerMode("NORMAL")
    aterm.instruments["PwrMeter"].setTriggerSource("CH1")
    aterm.instruments["PwrMeter"].setTriggerLevel(15)
    aterm.instruments["PwrMeter"].setTriggerSlope("POS")
    aterm.instruments["PwrMeter"].setTriggerDelay(9.00e-6)
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:CORRection:OFFSet 51")
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:AVERAGE 1")
    aterm.instruments["PwrMeter"].basicWrite("DISPlay:TRACe1:VSCALE 10.00")
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:CORRection:OFFSet 51")
    aterm.instruments["PwrMeter"].basicWrite("DISPlay:TRACe1:VCENTer 21.71")
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:AVERAGE 1")
    aterm.instruments["PwrMeter"].basicWrite("MARKer1:ASSIgn ACTIVE")
    aterm.instruments["PwrMeter"].basicWrite("MARKer2:ASSIgn ACTIVE")
    aterm.instruments["PwrMeter"].basicWrite("MARKer1:POSition:PIXel 164")
    aterm.instruments["PwrMeter"].basicWrite("MARKer2:POSition:PIXel 290")
    aterm.instruments["PwrMeter"].basicWrite("MARKer:MATH:BOTH RATIO,MK1MK2,dBm")
    aterm.instruments["PwrMeter"].initiate_cont("ON")

def setupPwrMeterXPDRModeC(aterm):
    # Turn on both channels
    aterm.instruments["PwrMeter"].setCalculate1_on("On")# set for PPQ
    #aterm.instruments["PwrMeter"].setCalculate2_on("On")# set for PPQ
    aterm.instruments["PwrMeter"].setCalculateMode("PULSE")# set for PPQ
    aterm.instruments["PwrMeter"].setCalculateUnits('dBm')# set for PPQ

    aterm.instruments["PwrMeter"].setFrequency1("1.03e+09") # 1030 MHz
    aterm.instruments["PwrMeter"].setTimeBase("5e-6")# set for PPQ
    aterm.instruments["PwrMeter"].setTriggerMode("NORMAL")# set for PPQ
    aterm.instruments["PwrMeter"].setTriggerSource("CH1")# set for PPQ
    aterm.instruments["PwrMeter"].setTriggerLevel(15)# set for PPQ
    aterm.instruments["PwrMeter"].setTriggerSlope("POS") # set for PPQ
    aterm.instruments["PwrMeter"].setTriggerDelay(9.00e-6)# set for PPQ
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:CORRection:OFFSet 51")
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:AVERAGE 1")
    aterm.instruments["PwrMeter"].basicWrite("DISPlay:TRACe1:VSCALE 10.00") # set for PPQ dBm/div
    aterm.instruments["PwrMeter"].basicWrite("DISPlay:TRACe1:VCENTer 21.71")# set for PPQ
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:CORRection:OFFSet 51") 
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:AVERAGE 1")
    aterm.instruments["PwrMeter"].basicWrite("MARKer1:ASSIgn ACTIVE")# set for PPQ
    aterm.instruments["PwrMeter"].basicWrite("MARKer2:ASSIgn ACTIVE")# set for PPQ
    aterm.instruments["PwrMeter"].basicWrite("MARKer1:POSition:PIXel 183")
    aterm.instruments["PwrMeter"].basicWrite("MARKer2:POSition:PIXel 394")
    aterm.instruments["PwrMeter"].basicWrite("MARKer:MATH:BOTH RATIO,MK1MK2,dBm")
    aterm.instruments["PwrMeter"].initiate_cont("ON")

def setupPwrMeterDME(aterm):
    # Turn on both channels
    aterm.instruments["PwrMeter"].setCalculate1_on("On")# set for PPQ
    #aterm.instruments["PwrMeter"].setCalculate2_on("On")# set for PPQ
    aterm.instruments["PwrMeter"].setCalculateMode("PULSE")# set for PPQ
    aterm.instruments["PwrMeter"].setCalculateUnits('dBm')# set for PPQ

    aterm.instruments["PwrMeter"].setFrequency1("1.03e+09") # 1030 MHz
    aterm.instruments["PwrMeter"].setTimeBase("5e-6")# set for PPQ
    aterm.instruments["PwrMeter"].setTriggerMode("NORMAL")# set for PPQ
    aterm.instruments["PwrMeter"].setTriggerSource("CH1")# set for PPQ
    aterm.instruments["PwrMeter"].setTriggerLevel(15)# set for PPQ
    aterm.instruments["PwrMeter"].setTriggerSlope("POS") # set for PPQ
    aterm.instruments["PwrMeter"].setTriggerDelay(9.00e-6)# set for PPQ
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:CORRection:OFFSet 51")
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:AVERAGE 1")
    aterm.instruments["PwrMeter"].basicWrite("DISPlay:TRACe1:VSCALE 10.00") # set for PPQ dBm/div
    aterm.instruments["PwrMeter"].basicWrite("DISPlay:TRACe1:VCENTer 21.71")# set for PPQ
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:CORRection:OFFSet 51") 
    aterm.instruments["PwrMeter"].basicWrite("SENSe1:AVERAGE 1")
    aterm.instruments["PwrMeter"].basicWrite("MARKer1:ASSIgn ACTIVE")# set for PPQ
    aterm.instruments["PwrMeter"].basicWrite("MARKer2:ASSIgn ACTIVE")# set for PPQ
    aterm.instruments["PwrMeter"].basicWrite("MARKer1:POSition:PIXel 189")
    aterm.instruments["PwrMeter"].basicWrite("MARKer2:POSition:PIXel 310")
    aterm.instruments["PwrMeter"].basicWrite("MARKer:MATH:BOTH RATIO,MK1MK2,dBm")
    aterm.instruments["PwrMeter"].initiate_cont("ON")


def WS_Analysis(Frequency_MHz, PowerArray, wssteps):
    # P1dB is defined in 727-0014-702 at the attenuation step that output power drops more than 0.5dB from the previous 1dB step.
    gainArray = []
    for val in range(len(wssteps)):
        gainArray.append(PowerArray[val] - wssteps[val])
    # gainArray = PowerArray - wssteps[0:len(PowerArray)]
    LinearGain = gainArray[-int(len(gainArray) / 2):]
    averageGain = sum(LinearGain) / (len(LinearGain)) # Take the average gain of the lower half of the power sweep and subtract it from the gain.
    compressionArray = []
    for val in range(len(gainArray)):
        compressionArray.append(averageGain - gainArray[val])
    # compressionArray = averageGain - gainArray

    P1dB_WS_Setting = wssteps[-1]
    P1dB = PowerArray[-1]
    P1dBCompression = compressionArray[-1]
    for i in range(len(compressionArray)):
        if compressionArray[i] <= 1:
            P1dB_WS_Setting = wssteps[i]
            P1dB = PowerArray[i]
            P1dBCompression = compressionArray[i]
            break
    
    return [P1dB_WS_Setting, P1dB, P1dBCompression]

# Get greatest common denominator
def gcd(a, b):
     while b != 0:
         (a, b) = (b, a%b)
     return a

def saveData(aterm, PLOT_DATA_E1, attenSteps, datadir):
    plot_data = CSVFile.create_csv()

    attenSteps.insert(0,'Atten Step')
    plot_data.tuple_add(attenSteps)
    
    e1_header = datadir.split("\\")[-1]
    PLOT_DATA_E1.insert(0, e1_header)
    plot_data.tuple_add(PLOT_DATA_E1)

    savedir = plot_data.print_out(0,datadir)
    aterm.logMessage(1, "CSV File Saved: %s" % savedir)

def xpdrModeSAnaysis(aterm, traceStr, threshold):
    aterm.logMessage(1, "Procedure Started")
    numPulses = 0
    trace = [float(x) for x in traceStr.split(',')]
    minPeak = max(trace)
    maxPeak = min(trace)

    currMax = trace[0]
    pulseDetected = False

    for sample in trace:
        if (sample>threshold)&(pulseDetected==False):
            pulseDetected=True
            currMax = sample
            numPulses+=1
            #print(str(sample) + ": greater than " + str(threshold))
        if sample<threshold:
            pulseDetected=False
        if pulseDetected:
            if sample>currMax:
                currMax=sample
                if currMax>maxPeak:
                    maxPeak=currMax
                if currMax<minPeak:
                    minPeak=currMax
    peakDiff=maxPeak-minPeak
    aterm.logMessage(1, "NumPulses: " + str(numPulses)+", maxPeak-minPeak: "+str(peakDiff))
    aterm.logMessage(1, "Procedure Ended")
    return list((numPulses, maxPeak, minPeak))