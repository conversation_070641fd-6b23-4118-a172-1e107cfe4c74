# -*- coding: utf-8 -*-
"""
Created on Mon March 30 3:02:30 2020

@author: E589493
         <PERSON><PERSON> <PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the instantion of power supply class.


HISTORY:

03/30/2020   KF    Initial Release.
04/23/2020   MR   Added various error handling statements and debug to ObserverClient
02/05/2021   MD   Improved trace logging to cover all SCPI commands

"""

import time
#Message to ClientObserver
import clr
import pyvisa

class Powersup():
    def __init__(self, rm):
        # Add conneciton to Observer client, ignore linting errors
        self.resourceManager = rm
        
        # Attempt connection to resource
        powersupIP = 'TCPIP0::***********::INSTR'
        try:
            self.powersup = self.resourceManager.rm.open_resource(powersupIP)
            self.resourceManager.logMessage(0, "Connection Success")
            self.basicWrite('')
        except pyvisa.VisaIOError:
            self.resourceManager.logMessage(3, "Failed to connect to resource")
            raise

##################################################
# Basic Commands
##################################################

    def basicWrite(self, cmd):
        tvlTxt = "> %s" % cmd
        self.resourceManager.logMessage(1, tvlTxt)
        resp = self.powersup.write("%s" % cmd)
        return resp

    def basicQuery(self, cmd, logEnable=True):
        tvlTxt = "> %s" % cmd
        if logEnable==True:
            self.resourceManager.logMessage(1, tvlTxt)
        resp = self.powersup.query("%s" % cmd).strip()
        tvlTxt = "< %s" % resp
        if logEnable==True:
            self.resourceManager.logMessage(1, tvlTxt)
        return resp

    def Ident(self):
       return self.basicQuery('*IDN?') + '\n'

    def Reset(self):
        """ Resets the Power Supply. """
        self.basicWrite('*RST')
        time.sleep(2)

    def Clear(self):
        """ Clear the unit to its power on default settings"""
        self.basicWrite('*RST')
    
    def closePS(self):
        """ Closes the Session """
        self.powersup.close()

##################################################
# Voltage Commands
##################################################

    def getVoltSource(self):
        """ Returns the set Voltage in Volts """
        return self.basicQuery(':SOUR:VOLT?')

    def getVoltMeasured(self):
        """ Returns the measured Voltage in Volts """
        return self.basicQuery(':MEAS:VOLT?')

    def setVoltageSource(self, volts):
        """ Sets voltage on Power Supply, User should pass in value of the voltage they wish to set"""
        try:
            float(volts)
            self.basicWrite(':SOUR:VOLT  ' + str(volts))
        except ValueError as e:
            # self.tvl.SendMessageToObserver("0",3,"PowerSupply, setVoltageSource","Invalid Voltage {}".format(e.args))
            # print("ERROR: PowerSupply, setVoltageSource, Invalid Voltage {}".format(e.args))
            self.resourceManager.logMessage(3, "Invalid Voltage {}".format(e.args))


    def setOVP(self, volts):
        """ Sets Over Voltage Protection on Power Supply, User should pass in value of the voltage they wish to set"""
        try:
            float(volts)
            self.basicWrite(':STAT:PROT:ENABLE ' + str(volts))
        except ValueError as e:
            # self.tvl.SendMessageToObserver("0",3,"PowerSupply, setOVP","Invalid Voltage {}".format(e.args))
            # print("ERROR: PowerSupply, setOVP, Invalid Voltage {}".format(e.args))
            self.resourceManager.logMessage(3, "Invalid Voltage {}".format(e.args))

    def getOVP(self):
        """ Sets Over Voltage Protection on Power Supply, User should pass in value of the voltage they wish to set"""
        return self.basicQuery('STAT:PROT:ENABLE?')

##################################################
# Current Commands
##################################################

    def getAmpSource(self):
        """ Returns the set Current in Amps """
        return self.basicQuery(':SOUR:CURR?')

    def getAmpMeasured(self):
        """ Returns the measured Current in Amps """
        return self.basicQuery(':MEAS:CURR?')

    def setCurrentSource(self, amps):
        """ Sets amps on Power Supply, User should pass in value of the current they wish to set"""
        try:
            float(amps)
            self.basicWrite(':SOUR:CURR  ' + str(amps))
        except ValueError as e:
            # self.tvl.SendMessageToObserver("0",3,"PowerSupply, setCurrentSource","Invalid Current {}".format(e.args))
            # print("ERROR: PowerSupply, setCurrentSource, Invalid Current {}".format(e.args))
            self.resourceManager.logMessage(3, "Invalid Current {}".format(e.args))


##################################################
# Output Commands
##################################################

    def getOutput(self):
        """ Returns the state of the ouptut in Boolean """
        return self.basicQuery(':OUTPut:STATe?')

    def setOuput(self, state):
        """ Sets VOltage Output of the Power SUpply, Pass in String 'ON' or 'OFF' """
        if state.lower() in ['on', 'off']:
            return self.basicQuery(':OUTPut:STATe ' + state)
        else:
            # self.tvl.SendMessageToObserver("0",3,"PowerSupply, setOutput","Invalid Output State ({})".format(state))
            # print("ERROR: PowerSupply, setOutput, Invalid Output State ({})".format(state))
            self.resourceManager.logMessage(3, "Invalid Output State ({})".format(state))