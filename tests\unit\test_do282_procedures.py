#!/usr/bin/env python3
"""
Unit Tests for DO282 Procedures
Tests both original functionality and HIGH PRIORITY sleep optimizations
"""

import unittest
import time
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from tests.mocks.mock_uat_connection import MockUATConnection, MockUATClient
from tests.mocks.mock_atc5000ng import MockATC5000NG
from unittest.mock import Mock, patch

class TestDO282Procedures(unittest.TestCase):
    """Test cases for DO282 procedures"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_rm = Mock()
        self.uat = MockUATConnection()
        self.uat.connect("*************")
        
    def tearDown(self):
        """Clean up after tests"""
        self.uat.disconnect()
        
    def test_uat_connection(self):
        """Test UAT connection functionality"""
        # Test connection status
        self.assertTrue(self.uat.connected)
        
        # Test basic command
        response = self.uat.send(":ATC:STATUS?")
        self.assertIsNotNone(response)
        
    def test_scenario_loading_optimization(self):
        """Test HIGH PRIORITY optimization: Scenario loading with adaptive polling"""
        print("\n=== Testing Scenario Loading Optimization ===")
        
        # Test optimized scenario loading
        ready, load_time = self.uat.simulate_scenario_loading_optimization()
        
        self.assertTrue(ready, "Scenario should load successfully")
        self.assertLess(load_time, 10.0, "Optimized loading should be much faster than 50s")
        
        # Verify scenario is loaded
        self.assertTrue(self.uat.scenario_loaded)
        self.assertEqual(self.uat.status, "READY")
        
        print(f"✓ Scenario loading optimization: {50 - load_time:.2f}s time savings")
        
    def test_scenario_start_optimization(self):
        """Test HIGH PRIORITY optimization: Scenario start with adaptive polling"""
        print("\n=== Testing Scenario Start Optimization ===")
        
        # First load a scenario
        self.uat.scenario_load("test_scenario.xml")
        
        # Test optimized scenario start
        ready, start_time = self.uat.simulate_scenario_start_optimization()
        
        self.assertTrue(ready, "Scenario should start successfully")
        self.assertLess(start_time, 5.0, "Optimized start should be much faster than 30s")
        
        # Verify scenario is running
        self.assertTrue(self.uat.scenario_running)
        
        print(f"✓ Scenario start optimization: {30 - start_time:.2f}s time savings")
        
    def test_adaptive_polling_mechanism(self):
        """Test the adaptive polling mechanism used in optimizations"""
        print("\n=== Testing Adaptive Polling Mechanism ===")
        
        # Test wait_for_ready function
        self.uat.status = "READY"
        
        start_time = time.time()
        ready = self.uat.wait_for_ready(timeout=10)
        poll_time = time.time() - start_time
        
        self.assertTrue(ready, "Should detect ready status")
        self.assertLess(poll_time, 1.0, "Should detect ready status quickly")
        
        print(f"✓ Adaptive polling detected ready status in {poll_time:.3f}s")
        
    def test_status_checking_fallback(self):
        """Test status checking fallback mechanism"""
        print("\n=== Testing Status Checking Fallback ===")
        
        # Test scenario where polling succeeds
        self.uat.status = "READY"
        ready = self.uat.wait_for_ready(timeout=5)
        self.assertTrue(ready)
        
        # Test scenario where polling times out (would use fallback delay)
        self.uat.status = "BUSY"
        start_time = time.time()
        ready = self.uat.wait_for_ready(timeout=0.2)  # Short timeout
        timeout_time = time.time() - start_time
        
        self.assertFalse(ready, "Should timeout when not ready")
        self.assertGreater(timeout_time, 0.15, "Should respect timeout")
        
        print("✓ Status checking fallback mechanism working correctly")
        
    def test_do282_24823_optimization_simulation(self):
        """Simulate the DO282_24823.py optimization behavior"""
        print("\n=== Simulating DO282_24823 Optimization ===")
        
        # Simulate the original procedure with optimizations
        total_start_time = time.time()
        
        # Step 1: Optimized scenario loading (was 50s fixed delay)
        print("Step 1: Loading scenario with adaptive polling...")
        ready, load_time = self.uat.simulate_scenario_loading_optimization()
        self.assertTrue(ready)
        
        # Step 2: Start scenario
        print("Step 2: Starting scenario...")
        self.uat.scenario_start()
        
        # Step 3: Optimized scenario start wait (was 30s fixed delay)
        print("Step 3: Waiting for scenario start with adaptive polling...")
        ready, start_time = self.uat.simulate_scenario_start_optimization()
        self.assertTrue(ready)
        
        total_time = time.time() - total_start_time
        original_total_time = 50 + 30  # Original fixed delays
        
        print(f"\n=== DO282_24823 Optimization Results ===")
        print(f"Original total time: {original_total_time}s")
        print(f"Optimized total time: {total_time:.2f}s")
        print(f"Total time savings: {original_total_time - total_time:.2f}s")
        print(f"Performance improvement: {((original_total_time - total_time) / original_total_time) * 100:.1f}%")
        
        # Should achieve significant time savings
        self.assertLess(total_time, 20.0, "Should be much faster than original 80s")
        
    def test_do282_248212_optimization_simulation(self):
        """Simulate the DO282_248212.py optimization behavior"""
        print("\n=== Simulating DO282_248212 Optimization ===")
        
        # Simulate the original procedure with optimizations
        start_time = time.time()
        
        # Optimized scenario loading (was 50s fixed delay)
        print("Loading scenario with adaptive polling...")
        ready, load_time = self.uat.simulate_scenario_loading_optimization()
        self.assertTrue(ready)
        
        total_time = time.time() - start_time
        original_time = 50  # Original fixed delay
        
        print(f"\n=== DO282_248212 Optimization Results ===")
        print(f"Original time: {original_time}s")
        print(f"Optimized time: {total_time:.2f}s")
        print(f"Time savings: {original_time - total_time:.2f}s")
        print(f"Performance improvement: {((original_time - total_time) / original_time) * 100:.1f}%")
        
        # Should achieve significant time savings
        self.assertLess(total_time, 10.0, "Should be much faster than original 50s")
        
    def test_error_handling_in_optimizations(self):
        """Test error handling in optimization scenarios"""
        print("\n=== Testing Error Handling in Optimizations ===")
        
        # Test scenario loading with connection issues
        self.uat.disconnect()
        
        with self.assertRaises(Exception):
            self.uat.scenario_load("test_scenario.xml")
            
        # Reconnect and test recovery
        self.uat.connect("*************")
        ready, load_time = self.uat.simulate_scenario_loading_optimization()
        self.assertTrue(ready, "Should recover after reconnection")
        
        print("✓ Error handling working correctly")
        
    def test_optimization_consistency(self):
        """Test that optimizations produce consistent results"""
        print("\n=== Testing Optimization Consistency ===")
        
        # Run multiple optimization cycles
        load_times = []
        start_times = []
        
        for i in range(5):
            # Reset UAT state
            self.uat.scenario_loaded = False
            self.uat.scenario_running = False
            self.uat.status = "IDLE"
            
            # Test scenario loading
            ready, load_time = self.uat.simulate_scenario_loading_optimization()
            self.assertTrue(ready)
            load_times.append(load_time)
            
            # Test scenario start
            ready, start_time = self.uat.simulate_scenario_start_optimization()
            self.assertTrue(ready)
            start_times.append(start_time)
            
        # Analyze consistency
        avg_load_time = sum(load_times) / len(load_times)
        avg_start_time = sum(start_times) / len(start_times)
        
        print(f"Average load time: {avg_load_time:.3f}s (range: {min(load_times):.3f}-{max(load_times):.3f}s)")
        print(f"Average start time: {avg_start_time:.3f}s (range: {min(start_times):.3f}-{max(start_times):.3f}s)")
        
        # All times should be much less than original fixed delays
        self.assertTrue(all(t < 10.0 for t in load_times), "All load times should be optimized")
        self.assertTrue(all(t < 5.0 for t in start_times), "All start times should be optimized")
        
        print("✓ Optimizations produce consistent results")


class TestDO282PerformanceValidation(unittest.TestCase):
    """Performance validation tests for DO282 optimizations"""
    
    def setUp(self):
        """Set up performance test fixtures"""
        self.uat = MockUATConnection()
        self.uat.connect("*************")
        
    def test_scenario_loading_performance_metrics(self):
        """Validate scenario loading performance metrics"""
        print("\n=== Scenario Loading Performance Metrics ===")
        
        # Test multiple scenario loading cycles
        original_time = 50.0  # Original fixed delay
        optimized_times = []
        
        for i in range(10):
            self.uat.scenario_loaded = False
            self.uat.status = "IDLE"
            
            ready, load_time = self.uat.simulate_scenario_loading_optimization()
            self.assertTrue(ready)
            optimized_times.append(load_time)
            
        avg_optimized_time = sum(optimized_times) / len(optimized_times)
        min_time = min(optimized_times)
        max_time = max(optimized_times)
        
        # Calculate performance metrics
        avg_improvement = ((original_time - avg_optimized_time) / original_time) * 100
        best_improvement = ((original_time - min_time) / original_time) * 100
        worst_improvement = ((original_time - max_time) / original_time) * 100
        
        print(f"Original time: {original_time}s")
        print(f"Average optimized time: {avg_optimized_time:.3f}s")
        print(f"Time range: {min_time:.3f}s - {max_time:.3f}s")
        print(f"Average improvement: {avg_improvement:.1f}%")
        print(f"Best case improvement: {best_improvement:.1f}%")
        print(f"Worst case improvement: {worst_improvement:.1f}%")
        
        # Validate performance targets
        self.assertGreater(avg_improvement, 80, "Should achieve >80% average improvement")
        self.assertGreater(worst_improvement, 70, "Should achieve >70% even in worst case")
        
    def test_combined_do282_performance(self):
        """Test combined performance of both DO282 procedures"""
        print("\n=== Combined DO282 Performance Test ===")
        
        # Simulate running both procedures
        total_original_time = 50 + 30 + 50  # DO282_24823 + DO282_248212
        
        # Test DO282_24823 equivalent
        ready1, load_time1 = self.uat.simulate_scenario_loading_optimization()
        ready2, start_time1 = self.uat.simulate_scenario_start_optimization()
        
        # Reset for DO282_248212 equivalent
        self.uat.scenario_loaded = False
        self.uat.status = "IDLE"
        
        # Test DO282_248212 equivalent
        ready3, load_time2 = self.uat.simulate_scenario_loading_optimization()
        
        total_optimized_time = load_time1 + start_time1 + load_time2
        total_improvement = ((total_original_time - total_optimized_time) / total_original_time) * 100
        
        print(f"Combined original time: {total_original_time}s")
        print(f"Combined optimized time: {total_optimized_time:.2f}s")
        print(f"Total time savings: {total_original_time - total_optimized_time:.2f}s")
        print(f"Combined improvement: {total_improvement:.1f}%")
        
        # Should achieve the target 105-120s savings
        time_savings = total_original_time - total_optimized_time
        self.assertGreater(time_savings, 100, "Should achieve >100s time savings")
        
    def test_optimization_reliability(self):
        """Test reliability of optimizations under various conditions"""
        print("\n=== Optimization Reliability Test ===")
        
        success_count = 0
        total_tests = 20
        
        for i in range(total_tests):
            try:
                # Reset state
                self.uat.scenario_loaded = False
                self.uat.scenario_running = False
                self.uat.status = "IDLE"
                
                # Test optimization
                ready, load_time = self.uat.simulate_scenario_loading_optimization()
                
                if ready and load_time < 10.0:  # Success criteria
                    success_count += 1
                    
            except Exception as e:
                print(f"Test {i+1} failed: {e}")
                
        reliability = (success_count / total_tests) * 100
        
        print(f"Successful optimizations: {success_count}/{total_tests}")
        print(f"Reliability: {reliability:.1f}%")
        
        # Should achieve high reliability
        self.assertGreaterEqual(reliability, 95.0, "Should achieve ≥95% reliability")


if __name__ == '__main__':
    # Run tests with verbose output
    unittest.main(verbosity=2)
