# -*- coding: utf-8 -*-
"""
Created on <PERSON><PERSON> Jan  6 08:58:20 2021

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the FAR43 J Squiter requiremensts.
             
             Verify that the Mode S transponder generates a correct squitter 
             approximately once per second.             
             
INPUTS:      ate_rm,ATC5000NG,RFBOB
OUTPUTS:     ReplyRates - accumulates reply rates, if any, for ATCRBS and ModeS
             SquitterRate - DF11 squitter rate
             CA           = DF11 CA Field
             ADRS         = DF11 Address
             FAR43_JOut.log - log file
    
    
             NOTEs: 
                 1) Interrogations are set to -100dBm and data recording
                    enabled
                 2) Reply Rates should be zeros (no interrogations)
                 3) Log File should contain Squitters only, approx 1 per second
                    for 60 seconds.
                              

HISTORY:
01/06/2021   MRS    Initial Release.

                                 
"""

#Required Libraries
import time
import os

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import RFBOB
from TXDLib.Handlers import ATC_RGS_Log_Decode


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def init_RFBOB(rfbob):
    """ initializes the RF BOB to Prim & Sec to BOTTOM port --- should be default. """
    #rfbob.connect()
    rfbob.setSwitch(0,0)   #Primary Bottom Port
    rfbob.setSwitch(1,0)   #Secondary Bottom Port
    rfbob.setSwitch(10,1)
    #rfbob.disconnect()


def post_process_modes(rm,mode):
    """ This routine post-processes ModeS log files for DF11 messages.
    Returns Squitter Rate, CA and Adrs fields for DF11 messages. """

    rm.logMessage(1,"PostProcessing ModeS log file.")
    squitter = -1     #squitter rate

    #filename
    fname = 'FAR43_JOut.log'

    #open file
    try:
        fn = open(fname,'r')
    except IOError:
        rm.logMessage(3,"Post Processing Second ModeS File: ERROR File Not Found.")
        return -1
   
    #Read In lines
    idx1  = -1
    idx2  = -1
    t1 = 0.0
    t2 = 0.0
    delta = 0.0
    count = 0
    for line in fn:
        #print(line)

        #Check line for DFx type
        idx1 = line.find(mode)  
  
        if idx1 > 0:             #mode (df type) string found in line
 
            #parse the line
            idx2 = line.find('Time:')
            t2 = float(line[idx2+6:idx2+14])
            delta = delta + (t2 - t1)
            #print("Count,Delta:",count,delta)
            t1 = t2   
            count = count + 1
        
        #Check for CA field
        if idx1 > 0:
            #Check for CA field
            idx2 = line.find('CA')
            if idx2 > 0:
                CAs = line[idx2+3:idx2+5]
        
        #Check for Addrs field
        if idx1 > 0:
            #Check for CA field
            idx2 = line.find('Adrs')
            if idx2 > 0:
                Adrs = line[idx2+5:idx2+12]

    #Close the file
    fn.close()

    #Convert strings to ints
    squitter = delta/count
    CA = int(CAs)
    ADRS = int(Adrs)

    return squitter,CA,ADRS
       
def delete_logfiles(fname):
    """ This routine deletes log files. """

    #delete the log file, if not needed
    #if its deleleted already, not a problem.
    try:
       os.remove(fname)
    except OSError:
        rm.logMessage(3,"Delete First ATCRBS File:File Not Found.")

    return
  

##############################################################################
################# MAIN     ##################################################
##############################################################################
        
def FAR43_J(rm,atc,rfbob):
    """ FAR43, J, Squitter """
    rm.logMessage(2,"*** FAR43 J, Squitter ***")
    
    #initialize rfbob
    init_RFBOB(rfbob)

    #Initialize Results
    ReplyRates = [0.0,0.0,0.0,0.0]
    SquitterRate = 0.0

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set Up Transponder -- MODE AS, -100 dBm
    atc.transponderModeAS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:POW -100.0")
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            
                           
    #start data logging
    atc.data_log_start()
    
    #Loop for one minute making Reply readings every 5 seconds
    r1=0.0
    r2=0.0
    r3=0.0
    r4=0.0
    for t1 in range(12):
 
        atc.waitforstatus()
        
        replyrate = atc.getPercentReply(2)
        # fix for erroneous reply rate
        count = 0
        while replyrate[1] == -1.0 and count < 10:
            replyrate = atc.getPercentReply(2)
            count = count + 1
    
        print("Reply Rate: ",replyrate)
		
		#accumulate any reply data
        r1 = r1 + replyrate[0]
        r2 = r2 + replyrate[1]
        r3 = r3 + replyrate[2]
        r4 = r4 + replyrate[3]
            
        time.sleep(5)
        msg = "Indx: " + str(t1*5)
        rm.logMessage(0,"FAR43_J: " + msg)    

    
    #Accumulated reply rates
    ReplyRates = [r1,r2,r3,r4]

    #stop recording and download data, log file generated for verification.
    atc.data_log_stop("FAR43_J.log")
    time.sleep(1)
   
    #decode the data
    ATC_RGS_Log_Decode.main("FAR43_J.log")
    time.sleep(2)
                    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Get Sqitter Rate,CA and Address of DF11 Message
    SquitterRate,CA,ADRS = post_process_modes(rm,'DF11')

    rm.logMessage(2,"Reply Rates: %d,%d,%d,%d" % (r1,r2,r3,r4))   
    rm.logMessage(2,"Squitter: %f" % (SquitterRate))   
    rm.logMessage(2,"CA: %d, ADDRS: %d" % (CA,ADRS))   
    rm.logMessage(2,"Done, closing session")

    #CleanUp, delete un-needed log files
    delete_logfiles("FAR43_J.log")
    
    #add Squitter Rate,CA and ADRS to results
    ReplyRates.append(SquitterRate)
    ReplyRates.append(CA)
    ReplyRates.append(ADRS)

    return ReplyRates 

##############################################################################
#run as main from command line
if __name__ == "__main__":
    
    rm = ate_rm()

    #Initialize the RFBOB
    rf_obj = RFBOB(rm)
    rf_obj.connect()
 
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

    
    res = FAR43_J(rm,atc_obj,rf_obj)

    atc_obj.close()
    rf_obj.disconnect()







