# -*- coding: utf-8 -*-
"""
Created on Tue Jan  5 08:58:20 2021

@author: E282068
         M<PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the FAR43 F Mode S Address requiremensts.
             
             Mode S Address: Interrogate the Mode S transponder and verify that
             it replies only to its assigned address. Use the correct address 
             and at least two incorrect addresses. The interrogations should be
             made at a nominal rate of 50 interrogations per second.             
    
             
INPUTS:      ate_rm,ATC5000NG,RFBOB
OUTPUTS:     Valid_Addrs    = % Reply
             InValid_Adrs1  = % Reply
             InValid_Adrs2  = % Reply
             
             NOTEs: 
                 1) Interrogations are UF5 messages with Address = 000004
                              

HISTORY:
01/05/2021   MRS    Initial Release.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import RFBOB


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def init_RFBOB(rfbob):
    """ initializes the RF BOB to Prim & Sec to BOTTOM port --- should be default. """
    #rfbob.connect()
    rfbob.setSwitch(0,0)   #Primary Bottom Port
    rfbob.setSwitch(1,0)   #Secondary Bottom Port
    rfbob.setSwitch(10,1)
    #rfbob.disconnect()


##############################################################################
################# MAIN     ##################################################
##############################################################################
        
def FAR43_F(rm,atc,rfbob):
    """ FAR43, F - MODE S Address """
    rm.logMessage(2,"*** FAR43 F, MODE S Address ***")
    
    #initialize rfbob
    init_RFBOB(rfbob)

    #Initialize Results
    Valid_Adrs = 0                         #Values read by TestStand
    InValid_Adrs1 = 0                      #Values read by TestStand
    InValid_Adrs2 = 0                      #Values read by TestStand

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set Up Transponder -- MODE S  ***VALID ADDRESS****
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 28000000000004") #Mode S Message UF5,PR0 Adrs000004
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            
        
            
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    Valid_Adrs = replyrate[1]                                     #ModeS Bottom           
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
   
    #Set Up Transponder -- MODE S  ***INVALID ADDRESS****
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 28000000000005") #Mode S Message UF5,PR0 Adrs000005
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            
        
            
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    InValid_Adrs1 = replyrate[1]                                     #ModeS Bottom
           
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Set Up Transponder -- MODE S  ***INVALID ADDRESS****
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 28000000000003") #Mode S Message UF5,PR0 Adrs000003
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            
        
            
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    InValid_Adrs2 = replyrate[1]                                     #ModeS Bottom            
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")



    rm.logMessage(2,"Validities: %f,%f,%f" % (Valid_Adrs,InValid_Adrs1,InValid_Adrs2))   
    rm.logMessage(2,"Done, closing session")

    
    return [Valid_Adrs,InValid_Adrs1,InValid_Adrs2]

##############################################################################
#run as main from command line
if __name__ == "__main__":
    
    rm = ate_rm()

    #Initialize the RFBOB
    rf_obj = RFBOB(rm)
    rf_obj.connect()


    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

    
    res = FAR43_F(rm,atc_obj,rf_obj)
    

    atc_obj.close()
    rf_obj.disconnect()

