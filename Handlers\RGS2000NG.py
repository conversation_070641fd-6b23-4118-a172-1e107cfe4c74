# -*- coding: utf-8 -*-
"""
Created on 3/12/2020

@author: H118396
         <PERSON>
         CNS QUALIFICATION TEST GROUP
         
DESCRIPTION:
RGS limited function and attribute class.

INPUTS: VISA Resource Manager obj (rm)
OUTPUTS: N/A

HISTORY:

04/23/2020  MR  Added various error handling statements and debug to ObserverClient
04/28/2020  MR  Fixed Scenario stop bug
08/26/2020  MRS Added own aircraft method, and generic gWirte/gQuery commands
10/06/2020  NEZ Updates for new ATE_RM.
"""

import threading
import time


class RGS2000NG():
    def __init__(self, ate_rm):

        
        # Attempt connection to resource
        self.rgsIP = "TCPIP0::*************::2001::SOCKET"
        self.resourceManager = ate_rm
        try:
            self.rgs = self.resourceManager.rm.open_resource(self.rgsIP)
            self.rgs.read_termination = "\r\n"
            self.rgs.write("")
            self.resourceManager.logMessage(0, "RGS Connection Success")

        except:
            self.resourceManager.logMessage(3,"RGS, <PERSON> Failed to connect to resource")
            raise

##################################################
# Basic Commands
##################################################

    def basicTvlMsg(self, tvlTxt):
        self.resourceManager.logMessage(1, tvlTxt)
        
    def basicWrite(self, cmd):
        tvlTxt = "> %s" % cmd
        self.basicTvlMsg(tvlTxt)
        resp = self.rgs.write("%s" % cmd)
        return resp

    def basicQuery(self, cmd, logEnable=False):
        tvlTxt = "> %s" % cmd
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        resp = self.rgs.query("%s" % cmd).strip()
        tvlTxt = "< %s" % resp
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        return resp
    
    def Ident(self):
        """ Returns the Scope identification string. """
        return self.basicQuery("*IDN?")

    def Reset(self):
        """ Resets the equipment """
        self.basicWrite(":RGS:RESET")
        time.sleep(15)
        self.basicTvlMsg("complete\n")
    
    def close(self):
        """ Closes the equipment connection """
        self.rgs.close()
         
    def rgsStatus(self):
        """ Returns the equipment status. *|20 indicates equipment is ready for next command. """
        return self.basicQuery(":RGS:STATUS?")

    def setGeneratorFreq(self, gen, freq):
        """ Sets Generator Frequency. String inputs are generator (GENA, GENB ...) and frequency (MHz)"""
        self.basicWrite(":RGS:SET:" + str(gen) + ":FREQ " + str(freq))

##################################################
# Scenario Commands
##################################################

    def init_own_aircraft_pos(self,altitude):
        """ Set Lat/Lon/Hdg/Alt and Xpdr Address for own aircraft. Input altitude
        in feet as a string. """
        self.basicWrite(":RGS:OWN:LAT 48.834340")
        self.basicWrite(":RGS:OWN:LONG -122.214200")
        self.basicWrite(":RGS:OWN:HEAD 0")
        self.basicWrite(":RGS:OWN:ALT {}".format(altitude))
        self.basicWrite(":RGS:OWN:MSADDR 4")
        
        
    def loadScen(self, scenario):
        """ Loads an intruder scenario from the RGS2000NG given <scen.csv>. """
        self.basicWrite(":RGS:SCE:LOAD {}".format(scenario))
        time.sleep(1)

    def startScen(self):
        """ Starts the loaded or created current scenario. """
        self.basicWrite(":RGS:SCE:STA")
        """
        if self.rgs.query(":RGS:SCE:STA") == "*":
            return True
        else:
            self.tvl.SendMessageToObserver("0",3,"RGS, startScen","Failed to start scenario")
            return False
        """
        monitor = threading.Timer(60, self.monitor_rgs)
        monitor.start()

    def monitor_rgs(self):
        if self.rgsStatus() != "*|20":
            self.startScen()

    def stopScen(self):
        """ Stops the currently running scenario. """
        self.basicWrite(":RGS:SCE:STOP")

    def saveScen(self, file):
        """ Saves the currently running intruder scenario given <filename.csv>. """
        self.basicWrite(":RGS:SCE:SAVE {}".format(file))

    def resetScen(self):
        """ Resets loaded scenario back to RGS2000NG default. """
        self.basicWrite(":RGS:SCE:RESET")

##################################################
# Measurement Commands
##################################################

    def pulse_measure(self,measurement):
        """ Set the measurement routine to sample the selected pulse """
        '''
           Pulse measurement commands:

            0 - F1 ATCRBS Reply
            1 - C1 ATCRBS Reply
            2 - A1 ATCRBS Reply
            3 - C2 ATCRBS Reply
            4 - A2 ATCRBS Reply
            5 - C4 ATCRBS Reply
            6 - A4 ATCRBS Reply
            7 - B1 ATCRBS Reply
            8 - D1 ATCRBS Reply
            9 - B2 ATCRBS Reply
            10 - D2 ATCRBS Reply
            11 - B4 ATCRBS Reply
            12 - D4 ATCRBS Reply
            13 - F2 ATCRBS Reply
            14 - P1 Mode S Reply
            15 - P2 Mode S Reply
            16 - P3 Mode S Reply
            17 - P4 Mode S Reply
            18 - S1 ATCRBS Interrogation
            19 - P1 ATCRBS Interrogation
            20 - P2 ATCRBS Interrogation
            21 - P3 ATCRBS Interrogation
            22 - P4 ATCRBS Interrogation
            23 - P1 Mode S Interrogation
            24 - P2 Mode S Interrogation
            25 - P6 Start Interrogation
            26 - P6 End Interrogation
            27 - P6 Mode S Interrogation
            28 - P6 SPR
            29 - SPI ATCRBS Reply
            30 - DF Frame Data
        '''
        self.basicWrite(":RGS:MEA:SET:PUL {}".format(str(measurement)))

    def pulse_falltime(self):
        """ Returns the fall time of the pulse set during "pulse_measure" """
        pulsefall = self.basicWrite(":RGS:MEA:PUL:FALL?")
        return str(pulsefall)
    
    def pulse_risetime(self):   
        """ Returns the rise time of the pulse set during "pulse_measure" """
        pulserise = self.basicWrite(":RGS:MEA:PUL:RISE?")
        return str(pulserise)
    
    def pulse_width(self):   
        """ Returns the pulse width of the pulse set during "pulse_measure" """
        pulsewidth = self.basicWrite(":RGS:MEA:PUL:WID?")
        return str(pulsewidth)