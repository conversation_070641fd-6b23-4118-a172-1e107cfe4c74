# -*- coding: utf-8 -*-
"""
SCRIPT IDENTIFIER:  DO282_248213.py

MODULE HISTORY:
        
AUTHOR: H157797

MODULE DESCRIPTION:
  This script creates the test scenario and sends the commands to RF generator like 
  ATC5000NG for DO-282 Section *******.1.3 Verification of Ground Uplink Message As Desired Signal.
  
  Provide the UUT with Ground Uplink Messages having:
    . RF Power Level:   -91 dBm
    . Center Frequency: 978 MHz +/- 1.0 kHz +/-19.560 kHz
    . FM Deviation:     560 kHz (measured at the minimum eye pattern opening per $*******)
    . Message Contents: Ground Uplink Message Message with pseudo-random payload data,
                        and valid FEC Parity field.
    . Message Rate:     10 per second in the Uplink segment only.

DEPENDENCY:
   Python v3.6 or above
   UAT_CONNECTION.py
   FEC.py

NOTE:
   The cable line loss need to be considered. The loss of the message power levels could be set by 
   this script's input argument. By default, it is set to 3db (possitive) assuming the cable line 
   loss is 3db.

HONEYWELL DIFFERENCE/DEVIATION: N/A

"""

import os
import sys
import time
import datetime
#import argparse
import csv

from TXDLib.Handlers import ate_rm

from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc
from TXDLib.Procedures.DO282 import FEC as fec
from TXDLib.Procedures.DO282 import UAT_LOGGING as ul



#parser = argparse.ArgumentParser()
#parser = argparse.ArgumentParser(description='DO-282 *******.1.1 Test scenario script')
#parser.add_argument("-l", "--lineloss", default=3, type=int, help="Cable Line loss, unit: db")
#args = parser.parse_args()

#Cable line loss by default 3db
CABLE_LINE_LOSS = 3.0
INPUT_FILE_PATH = "DO282_248213_INPUT"

#set MSO Step for each Ground Uplink Msg with uniformal spaced in MSO 0 ~ 751
MSO_STEP = 75

gndUplink =''
fecOfGndUplink = '' 
    
#Apply Ground Uplink Input Messages at maximum negative frequency offset and maximum positive frequency offset
#Parameters:
#   channelSel: 1: UAT RX1; 2: RX2 
#	powerValue: the power level of the squitter messages 
#	shiftValue: the shift for the Doppler Test.
def test_case(channelSel=None, powerValue=None,shiftValue=None,inputLogFile=None):
    f = open(inputLogFile,'w',encoding='utf-8',newline='')

    if(channelSel != 1 and channelSel != 2):
        sys.exit("Select the UAT RX Channel: 1 for RX1; 2 for RX2")

    #initial the UAT scenario
    uc.set_scenario(reset=True, channel=channelSel, run_time=10, test_mode_type="Doppler_modulation", mso_step=MSO_STEP)

    #Set the scenario page for the UAT Doppler and Modulation Frequency
    uc.set_doppler_modulation_freq(frequency=21.06, shift=shiftValue,modulation=280 )

    #10 Ground Uplink Messages
    try:
        csvWriter = csv.writer(f)
        csvWriter.writerow(['SYSTEM TIME','MSG ID','MESSAGE'])
        msoStep = 0
        for i in range(10):
            for j in range(6):
                gndUplink = uc.generate_random_uat_msg(fec.GNDUPLINK_MSG_TYPE)
                fecOfGndUplink = fec.fec_parity(fec.GNDUPLINK_MSG_TYPE,gndUplink)
                csvWriter.writerow([datetime.datetime.now().strftime('%H:%M:%S.%f'),i+1,gndUplink+fecOfGndUplink])
                if(j == 0): #set MSO number for 1st block
                    uc.set_sta_fisb_matrix_msg(intruderNumber=i+1, mso=msoStep, data = gndUplink, fec=fecOfGndUplink, power=powerValue, blockNumber=j+1)
                else:
                    uc.set_sta_fisb_matrix_msg(intruderNumber=i+1, data = gndUplink, fec=fecOfGndUplink, power=powerValue, blockNumber=j+1)
            msoStep = msoStep + MSO_STEP
    finally:
        f.close()

    uc.scenario_start(start=1)

    #Sleep 50 sec for the scenario loading into RF generator
    time.sleep(50)


    
    
def ground_uplink_rxx(rm,RFPathLoss_Top, RFPathLoss_Bot):    

    inputFile = os.getcwd()+"\\"+INPUT_FILE_PATH
    ul.mkdir(inputFile)
    caseId = 0
   
    #connect to the ATC target
    uc.create_connection()
    # set mode to UAT mode
    uc.set_test_mode("UAT")

    CABLE_LINE_LOSS = RFPathLoss_Top

    #@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    #TBD:  pwrList may be changed to determine the real minimum RF signal required to produce 
    #      an average reception rate of 90 percent by the UUT receiver for MOPS Testing when TXD
    #      LRU is available.
    #@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@    
    #pwrList = [-96, -95, -94, -93, -92]
    pwrList = [-96, -94, -93, -92]

    for rxNo in range(1,3):     	

        #Set Cable Loss to RF PathLoss
        if (rxNo == 1): 
            CABLE_LINE_LOSS = RFPathLoss_Top
        else:
            CABLE_LINE_LOSS = RFPathLoss_Bot


        caseId += 1

        rm.logMessage(2,"-----------------------------------------------------------------------------------------")
        rm.logMessage(2,"Running Test Case #" + str(caseId) + " On UAT RX" + str(rxNo))
        rm.logMessage(2,"-----------------------------------------------------------------------------------------")
        rm.logMessage(0,"   Apply the Desired Message Signal with the Center Frequency set to the minimum value:")
        rm.logMessage(0,"         978 MHz - 21.06 kHz")
        rm.logMessage(0,"   Modulation Distortion:  560 Khz")
        rm.logMessage(0,"   10 Messages Content per second with pseudo-random payload data and valid FEC")

        stepId = 1
        for pwrLevel in pwrList:
            printMsg ="Running step " + str(stepId) +" - RF Power Level: " + str(pwrLevel) + " plus " + str(CABLE_LINE_LOSS) + "db line lose"
            rm.logMessage(0,printMsg)
            inputFilePath = inputFile+'\\CASE_'+str(caseId)+'_RX_'+str(rxNo)+'_step'+str(stepId)+'.csv'
            test_case(channelSel=rxNo, powerValue=pwrLevel + CABLE_LINE_LOSS,shiftValue='-',inputLogFile=inputFilePath)
            stepId = stepId + 1

        print("Sleep 10 secs...")
        time.sleep(10)

        caseId += 1
        rm.logMessage(2,"-----------------------------------------------------------------------------------------")
        rm.logMessage(2,"Running Test Case #" + str(caseId) + " On UAT RX" + str(rxNo))
        rm.logMessage(2,"-----------------------------------------------------------------------------------------")
        rm.logMessage(0,"   Apply the Desired Message Signal with the Center Frequency set to the maximum value:")
        rm.logMessage(0,"         978 MHz + 21.06 kHz")
        rm.logMessage(0,"   Modulation Distortion:  560 Khz")
        rm.logMessage(0,"   10 Messages Content per second with pseudo-random payload data and valid FEC")

        stepId = 1
        for pwrLevel in pwrList:
            printMsg = "Running step " + str(stepId) +": RF Power Level: " + str(pwrLevel) + " plus " + str(CABLE_LINE_LOSS) + "db line lose"
            rm.logMessage(0,printMsg)
            inputFilePath = inputFile+'\\CASE_'+str(caseId)+'_RX_'+str(rxNo)+'_step'+str(stepId)+'.csv'
            test_case(channelSel=rxNo, powerValue=pwrLevel + CABLE_LINE_LOSS,shiftValue='+',inputLogFile=inputFilePath)
            stepId = stepId + 1


        rm.logMessage(0,"Sleep 10 secs...")
        time.sleep(10)
    
    #Close connection to ATC
    uc.close_connection()
    rm.logMessage(2,"Done, Closing Session.") 


if __name__ == '__main__':
   
    #SetUP Resource Manager
    rm = ate_rm()

    ground_uplink_rxx(rm,12.5, 12.69)

