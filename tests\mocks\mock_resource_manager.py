#!/usr/bin/env python3
"""
Mock Resource Manager (ate_rm) for Testing
Simulates the behavior of the real resource manager
"""

import time
import threading
from datetime import datetime
from unittest.mock import Mock

class MockResourceManager:
    """Mock implementation of ate_rm resource manager"""
    
    def __init__(self):
        self.rm = Mock()  # Mock VISA resource manager
        self.log_messages = []
        self.log_lock = threading.Lock()
        self.connected_resources = {}
        
        # Logging levels
        self.LOG_LEVELS = {
            0: "INFO",
            1: "DEBUG", 
            2: "WARNING",
            3: "ERROR"
        }
        
        self.logMessage(0, "Mock Resource Manager initialized")
        
    def logMessage(self, level, message, details=""):
        """Log message with timestamp and level"""
        with self.log_lock:
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            level_name = self.LOG_LEVELS.get(level, "UNKNOWN")
            
            if details:
                full_message = f"[{timestamp}] {level_name}: {message} - {details}"
            else:
                full_message = f"[{timestamp}] {level_name}: {message}"
                
            self.log_messages.append({
                'timestamp': timestamp,
                'level': level,
                'level_name': level_name,
                'message': message,
                'details': details,
                'full_message': full_message
            })
            
            # Print to console for immediate feedback
            print(full_message)
            
    def getLogMessages(self, level=None):
        """Get log messages, optionally filtered by level"""
        with self.log_lock:
            if level is None:
                return self.log_messages.copy()
            else:
                return [msg for msg in self.log_messages if msg['level'] == level]
                
    def clearLog(self):
        """Clear all log messages"""
        with self.log_lock:
            self.log_messages.clear()
            self.logMessage(0, "Log cleared")
            
    def open_resource(self, resource_string):
        """Mock opening a VISA resource"""
        self.logMessage(1, f"Opening resource: {resource_string}")
        
        # Create a mock resource
        mock_resource = Mock()
        mock_resource.resource_name = resource_string
        mock_resource.timeout = 5000  # 5 second timeout
        
        # Add common VISA methods
        mock_resource.write = Mock()
        mock_resource.query = Mock(return_value="OK")
        mock_resource.read = Mock(return_value="")
        mock_resource.close = Mock()
        
        # Store the resource
        self.connected_resources[resource_string] = mock_resource
        
        self.logMessage(0, f"Resource opened successfully: {resource_string}")
        return mock_resource
        
    def close_resource(self, resource_string):
        """Mock closing a VISA resource"""
        if resource_string in self.connected_resources:
            resource = self.connected_resources[resource_string]
            resource.close()
            del self.connected_resources[resource_string]
            self.logMessage(0, f"Resource closed: {resource_string}")
        else:
            self.logMessage(2, f"Resource not found for closing: {resource_string}")
            
    def cleanup(self):
        """Clean up all resources"""
        self.logMessage(0, "Starting resource cleanup...")
        
        for resource_string in list(self.connected_resources.keys()):
            self.close_resource(resource_string)
            
        self.logMessage(0, f"Cleanup complete. Closed {len(self.connected_resources)} resources")
        
    def list_resources(self):
        """List all available resources (mock)"""
        mock_resources = [
            "TCPIP0::************::INSTR",  # Power meter
            "TCPIP0::************::INSTR",  # Signal generator
            "TCPIP0::************::INSTR",  # Spectrum analyzer
            "TCPIP0::************::INSTR",  # Oscilloscope
            "TCPIP0::*************::2001::SOCKET",  # ATC5000NG
        ]
        
        self.logMessage(1, f"Available resources: {len(mock_resources)}")
        return mock_resources
        
    def get_connected_resources(self):
        """Get list of currently connected resources"""
        return list(self.connected_resources.keys())
        
    def simulate_optimization_logging(self):
        """
        Simulate logging behavior during optimization testing
        """
        print("=== Simulating Optimization Logging ===")
        
        # Simulate HIGH PRIORITY optimization logging
        self.logMessage(0, "=== HIGH PRIORITY OPTIMIZATION TEST ===")
        
        # Scenario loading optimization
        self.logMessage(0, "Testing scenario loading optimization...")
        start_time = time.time()
        time.sleep(0.1)  # Simulate optimized scenario loading
        load_time = time.time() - start_time
        self.logMessage(0, f"Scenario loaded in {load_time:.3f}s (was 50s)")
        
        # Instrument reset optimization
        self.logMessage(0, "Testing instrument reset optimization...")
        start_time = time.time()
        time.sleep(0.05)  # Simulate optimized reset
        reset_time = time.time() - start_time
        self.logMessage(0, f"Instrument reset in {reset_time:.3f}s (was 15s)")
        
        # RF stabilization optimization
        self.logMessage(0, "Testing RF stabilization optimization...")
        start_time = time.time()
        time.sleep(0.08)  # Simulate optimized RF stabilization
        rf_time = time.time() - start_time
        self.logMessage(0, f"RF stabilized in {rf_time:.3f}s (was 25s)")
        
        # Simulate MEDIUM PRIORITY optimization logging
        self.logMessage(0, "=== MEDIUM PRIORITY OPTIMIZATION TEST ===")
        
        # Communication retry optimization
        self.logMessage(0, "Testing communication retry optimization...")
        start_time = time.time()
        time.sleep(0.03)  # Simulate optimized communication
        comm_time = time.time() - start_time
        self.logMessage(0, f"Communication completed in {comm_time:.3f}s (was 1s)")
        
        # Measurement settling optimization
        self.logMessage(0, "Testing measurement settling optimization...")
        start_time = time.time()
        time.sleep(0.02)  # Simulate optimized measurement settling
        measure_time = time.time() - start_time
        self.logMessage(0, f"Measurement settled in {measure_time:.3f}s (was 0.5s)")
        
        # Calculate total savings
        original_total = 50 + 15 + 25 + 1 + 0.5  # 91.5s
        optimized_total = load_time + reset_time + rf_time + comm_time + measure_time
        total_savings = original_total - optimized_total
        
        self.logMessage(0, f"=== OPTIMIZATION SUMMARY ===")
        self.logMessage(0, f"Original total time: {original_total}s")
        self.logMessage(0, f"Optimized total time: {optimized_total:.3f}s")
        self.logMessage(0, f"Total time savings: {total_savings:.3f}s")
        self.logMessage(0, f"Performance improvement: {(total_savings / original_total) * 100:.1f}%")
        
        return {
            'original_time': original_total,
            'optimized_time': optimized_total,
            'time_savings': total_savings,
            'improvement_percentage': (total_savings / original_total) * 100
        }
        
    def generate_test_report(self):
        """Generate a test report from logged messages"""
        print("\n=== Test Report Generation ===")
        
        # Count messages by level
        level_counts = {}
        for level_name in self.LOG_LEVELS.values():
            level_counts[level_name] = 0
            
        for msg in self.log_messages:
            level_counts[msg['level_name']] += 1
            
        # Generate report
        report = {
            'total_messages': len(self.log_messages),
            'level_counts': level_counts,
            'start_time': self.log_messages[0]['timestamp'] if self.log_messages else None,
            'end_time': self.log_messages[-1]['timestamp'] if self.log_messages else None,
            'errors': [msg for msg in self.log_messages if msg['level'] == 3],
            'warnings': [msg for msg in self.log_messages if msg['level'] == 2]
        }
        
        print(f"Total log messages: {report['total_messages']}")
        print(f"Message breakdown:")
        for level, count in level_counts.items():
            print(f"  {level}: {count}")
            
        if report['errors']:
            print(f"Errors found: {len(report['errors'])}")
            for error in report['errors']:
                print(f"  - {error['message']}")
                
        if report['warnings']:
            print(f"Warnings found: {len(report['warnings'])}")
            for warning in report['warnings']:
                print(f"  - {warning['message']}")
                
        return report
        
    def test_resource_management(self):
        """Test resource management functionality"""
        print("=== Testing Resource Management ===")
        
        # Test opening resources
        resources_to_test = [
            "TCPIP0::************::INSTR",  # Power meter
            "TCPIP0::************::INSTR",  # Signal generator
            "TCPIP0::*************::2001::SOCKET",  # ATC5000NG
        ]
        
        opened_resources = []
        
        for resource in resources_to_test:
            try:
                mock_resource = self.open_resource(resource)
                opened_resources.append(resource)
                self.logMessage(0, f"Successfully opened: {resource}")
            except Exception as e:
                self.logMessage(3, f"Failed to open {resource}: {e}")
                
        # Test resource operations
        for resource in opened_resources:
            mock_res = self.connected_resources[resource]
            mock_res.write("*IDN?")
            response = mock_res.query("*IDN?")
            self.logMessage(1, f"Resource {resource} responded: {response}")
            
        # Test cleanup
        initial_count = len(self.connected_resources)
        self.cleanup()
        final_count = len(self.connected_resources)
        
        self.logMessage(0, f"Resource management test complete")
        self.logMessage(0, f"Opened: {initial_count}, Closed: {initial_count - final_count}")
        
        return {
            'resources_tested': len(resources_to_test),
            'resources_opened': len(opened_resources),
            'cleanup_successful': final_count == 0
        }


# Alias for compatibility with existing code
def ate_rm():
    """Factory function to create MockResourceManager instance"""
    return MockResourceManager()


# Test function to demonstrate resource manager functionality
def test_resource_manager():
    """
    Test function to demonstrate mock resource manager capabilities
    """
    print("=== Resource Manager Test ===")
    
    # Create resource manager
    rm = MockResourceManager()
    
    # Test resource management
    resource_results = rm.test_resource_management()
    
    # Test optimization logging
    optimization_results = rm.simulate_optimization_logging()
    
    # Generate test report
    report = rm.generate_test_report()
    
    print(f"\n=== Test Summary ===")
    print(f"Resource management: {'PASS' if resource_results['cleanup_successful'] else 'FAIL'}")
    print(f"Optimization logging: {'PASS' if optimization_results['time_savings'] > 80 else 'FAIL'}")
    print(f"Total log messages: {report['total_messages']}")
    print(f"Errors: {len(report['errors'])}")
    print(f"Warnings: {len(report['warnings'])}")


if __name__ == "__main__":
    test_resource_manager()
