#!/usr/bin/env python3
"""
Mock Spectrum Analyzer for Testing
Simulates the behavior of spectrum analyzer instruments
"""

import time
import random
import math
from unittest.mock import Mock

class MockSpectrumAnalyzer:
    """Mock implementation of spectrum analyzer"""
    
    def __init__(self):
        self.connected = False
        self.center_freq = 1000.0  # MHz
        self.span = 100.0  # MHz
        self.res_bandwidth = 10.0  # kHz
        self.vid_bandwidth = 100.0  # kHz
        self.sweep_time = 1.0  # seconds
        self.trace_type = "NORMAL"
        
        # Measurement simulation
        self.noise_floor = -80.0  # dBm
        self.peak_freq = 1030.0  # MHz
        self.peak_power = -30.0  # dBm
        
        # Timing simulation
        self.config_delay = 0.01  # Simulated configuration delay
        self.measurement_delay = 0.1  # Simulated measurement delay
        
    def connect(self, address):
        """Simulate connection to spectrum analyzer"""
        time.sleep(0.1)
        self.connected = True
        print(f"Mock Spectrum Analyzer connected to {address}")
        return True
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        print("Mock Spectrum Analyzer disconnected")
        
    def CenterFreqSet(self, freq, unit='MHz'):
        """Simulate setting center frequency with optimized timing"""
        if not self.connected:
            raise Exception("Spectrum Analyzer not connected")
            
        # Simulate optimized configuration (no individual delays)
        time.sleep(self.config_delay)
        self.center_freq = freq
        print(f"Mock SA: Center frequency set to {freq} {unit}")
        
    def ResBandwidthSet(self, bw, unit='kHz'):
        """Simulate setting resolution bandwidth"""
        if not self.connected:
            raise Exception("Spectrum Analyzer not connected")
            
        time.sleep(self.config_delay)
        self.res_bandwidth = bw
        print(f"Mock SA: Resolution bandwidth set to {bw} {unit}")
        
    def VidBandwidthSet(self, bw, unit='kHz'):
        """Simulate setting video bandwidth"""
        if not self.connected:
            raise Exception("Spectrum Analyzer not connected")
            
        time.sleep(self.config_delay)
        self.vid_bandwidth = bw
        print(f"Mock SA: Video bandwidth set to {bw} {unit}")
        
    def SweepTimeSet(self, time_val, unit='s'):
        """Simulate setting sweep time"""
        if not self.connected:
            raise Exception("Spectrum Analyzer not connected")
            
        time.sleep(self.config_delay)
        self.sweep_time = time_val
        print(f"Mock SA: Sweep time set to {time_val} {unit}")
        
    def SpanSet(self, span, unit='kHz'):
        """Simulate setting frequency span"""
        if not self.connected:
            raise Exception("Spectrum Analyzer not connected")
            
        time.sleep(self.config_delay)
        self.span = span / 1000.0 if unit == 'kHz' else span  # Convert to MHz
        print(f"Mock SA: Span set to {span} {unit}")
        
    def TraceTypeSet(self, trace_type):
        """Simulate setting trace type"""
        if not self.connected:
            raise Exception("Spectrum Analyzer not connected")
            
        time.sleep(self.config_delay)
        self.trace_type = trace_type
        print(f"Mock SA: Trace type set to {trace_type}")
        
        # Simulate trace acquisition time for MAXHold
        if trace_type == "MAXHold":
            print("Mock SA: Starting MAXHold trace acquisition...")
            time.sleep(self.measurement_delay)  # Simulated acquisition time
            
    def GetMaxPeakFreq(self):
        """Simulate getting maximum peak frequency"""
        if not self.connected:
            raise Exception("Spectrum Analyzer not connected")
            
        time.sleep(self.measurement_delay)
        
        # Simulate realistic frequency measurement with some variation
        freq_variation = random.uniform(-0.01, 0.01)  # ±10 kHz variation
        measured_freq = self.center_freq + freq_variation
        
        print(f"Mock SA: Max peak frequency: {measured_freq:.6f} MHz")
        return measured_freq
        
    def GetMaxPeakPower(self):
        """Simulate getting maximum peak power"""
        if not self.connected:
            raise Exception("Spectrum Analyzer not connected")
            
        time.sleep(self.measurement_delay)
        
        # Simulate realistic power measurement with some variation
        power_variation = random.uniform(-1.0, 1.0)  # ±1 dB variation
        measured_power = self.peak_power + power_variation
        
        print(f"Mock SA: Max peak power: {measured_power:.2f} dBm")
        return measured_power
        
    def GetTrace(self):
        """Simulate getting trace data"""
        if not self.connected:
            raise Exception("Spectrum Analyzer not connected")
            
        time.sleep(self.measurement_delay)
        
        # Generate simulated trace data
        num_points = 401  # Typical number of trace points
        start_freq = self.center_freq - self.span / 2
        stop_freq = self.center_freq + self.span / 2
        
        frequencies = []
        powers = []
        
        for i in range(num_points):
            freq = start_freq + (stop_freq - start_freq) * i / (num_points - 1)
            
            # Simulate signal peak at center frequency
            if abs(freq - self.peak_freq) < 0.1:  # Within 100 kHz of peak
                power = self.peak_power + random.uniform(-0.5, 0.5)
            else:
                power = self.noise_floor + random.uniform(-5.0, 5.0)
                
            frequencies.append(freq)
            powers.append(power)
            
        return frequencies, powers
        
    def simulate_optimized_configuration(self):
        """
        Simulate the optimized spectrum analyzer configuration
        This demonstrates the MEDIUM PRIORITY optimization in action
        """
        print("=== Simulating Optimized Spectrum Analyzer Configuration ===")
        
        # Original behavior: individual delays after each command
        print("Original configuration method:")
        start_time = time.time()
        
        # Simulate original method with individual 3s delays
        original_commands = [
            ("CenterFreqSet", 1080, "MHz"),
            ("ResBandwidthSet", 10, "kHz"),
            ("VidBandwidthSet", 100, "kHz"),
            ("SweepTimeSet", 10, "s"),
            ("SpanSet", 500, "kHz")
        ]
        
        original_time = len(original_commands) * 3.0  # 3s delay per command
        print(f"Original method would take: {original_time}s")
        
        # Optimized behavior: batched configuration with single delay
        print("\nOptimized configuration method:")
        opt_start_time = time.time()
        
        # Execute all commands without individual delays
        self.CenterFreqSet(1080, 'MHz')
        self.ResBandwidthSet(10, 'kHz')
        self.VidBandwidthSet(100, 'kHz')
        self.SweepTimeSet(10, 's')
        self.SpanSet(500, 'kHz')
        
        # Single delay after batch configuration (2s instead of 15s)
        time.sleep(0.1)  # Simulated batch settling time
        
        optimized_time = time.time() - opt_start_time
        
        print(f"Optimized method took: {optimized_time:.2f}s")
        print(f"Time savings: {original_time - optimized_time:.2f}s")
        print(f"Performance improvement: {((original_time - optimized_time) / original_time) * 100:.1f}%")
        
        return original_time, optimized_time
        
    def measure_frequency_pair(self, pair_number):
        """
        Simulate measuring frequency pair with optimized configuration
        """
        print(f"=== Measuring Frequency Pair {pair_number} ===")
        
        if pair_number == 1:
            center_freq = 1080
        elif pair_number == 2:
            center_freq = 1150
        else:
            raise ValueError("Invalid pair number")
            
        # Simulate optimized configuration
        start_time = time.time()
        
        # Batch configuration (optimized)
        self.CenterFreqSet(center_freq, 'MHz')
        self.ResBandwidthSet(10, 'kHz')
        self.VidBandwidthSet(100, 'kHz')
        self.SweepTimeSet(10, 's')
        self.SpanSet(500, 'kHz')
        
        # Single delay after batch configuration
        time.sleep(0.1)  # Simulated settling time
        
        self.TraceTypeSet('MAXHold')
        time.sleep(0.2)  # Simulated MAXHold acquisition
        
        # Get measurements
        freq = self.GetMaxPeakFreq()
        power = self.GetMaxPeakPower()
        
        config_time = time.time() - start_time
        
        print(f"Pair {pair_number} measurement completed in {config_time:.2f}s")
        print(f"Frequency: {freq:.6f} MHz, Power: {power:.2f} dBm")
        
        return freq, power, config_time


class MockSpectrumAnalyzerE4440A(MockSpectrumAnalyzer):
    """Mock Agilent E4440A Spectrum Analyzer"""
    
    def __init__(self):
        super().__init__()
        self.model = "E4440A"
        self.manufacturer = "Agilent"
        
    def preset(self):
        """Simulate instrument preset"""
        time.sleep(0.2)
        self.center_freq = 1000.0
        self.span = 100.0
        self.res_bandwidth = 10.0
        self.vid_bandwidth = 100.0
        self.sweep_time = 1.0
        print("Mock E4440A: Preset complete")


class MockSpectrumAnalyzerR3132(MockSpectrumAnalyzer):
    """Mock Advantest R3132 Spectrum Analyzer"""
    
    def __init__(self):
        super().__init__()
        self.model = "R3132"
        self.manufacturer = "Advantest"
        
    def initialize(self):
        """Simulate instrument initialization"""
        time.sleep(0.3)
        print("Mock R3132: Initialization complete")


# Test function to demonstrate optimization benefits
def test_spectrum_analyzer_optimization():
    """
    Test function to compare original vs optimized spectrum analyzer configuration
    """
    print("=== Spectrum Analyzer Optimization Test ===")
    
    sa = MockSpectrumAnalyzer()
    sa.connect("GPIB::18::INSTR")
    
    # Test optimized configuration
    original_time, optimized_time = sa.simulate_optimized_configuration()
    
    print(f"\n=== Testing Frequency Measurements ===")
    
    # Test frequency pair measurements
    freq1, power1, time1 = sa.measure_frequency_pair(1)
    freq2, power2, time2 = sa.measure_frequency_pair(2)
    
    total_measurement_time = time1 + time2
    original_measurement_time = 2 * (5 * 3.0 + 0.5)  # 5 commands × 3s + measurement time
    
    print(f"\n=== Measurement Summary ===")
    print(f"Original measurement time: {original_measurement_time}s")
    print(f"Optimized measurement time: {total_measurement_time:.2f}s")
    print(f"Total time savings: {original_measurement_time - total_measurement_time:.2f}s")
    
    sa.disconnect()


if __name__ == "__main__":
    test_spectrum_analyzer_optimization()
