# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Receiver Characteristics, Section *******, Step 2 (Sensitivity 
             Variation with Frequency).
             
             "Using a standard Mode C/Mode S All-Call interrogation, 
             interrogate the transponder at RF signal frequencies of 1029.8, 
             1030.0 and 1030.2 MHz.  Determine the required maximum RF signal 
             level at each frequency required to produce 90% reply efficiency."
             
INPUTS:      RM,ATC,PathLoss
OUTPUTS:     'Pwr_Levels' Array of Pwr_Levels at 90% efficiency (MTLs).

             Notes: PathLoss = RF Path Loss: Cable Loss (bottom) + RFBOB 

HISTORY:

02/06/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review
02/25/2021   MRS    Updates for new Handlers and Lobster.
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# MAIN     ##################################################
##############################################################################
def Test_2_3_2_1_Step2(rm,atc,PathLoss):
    """ DO-181E, Receiver Characteristics, Sect *******, Step 2 """
    
    rm.logMessage(2,"*** DO-181E, Receiver Characteristics, Sect *******, Step 2 ***\r\n")
        
    #Initialize  Frequencies and Power Levels
    Init_PowelLevel = -65.0                         #initial Interrogation Power Level
    Frequency = ['1029.80','1030.00','1030.20']     #interrogation frequencies
    Pwr_Levels = [0.0,0.0,0.0]                      #Values read by TestStand
    
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE C/S All Call
    atc.transponderModeCS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    
    atc.waitforstatus()            
    rm.logMessage(0,"Test_2_3_2_1_Step2 Begin Power Loop")    
    
    #loop thru the three frequencies,Check the Reply Rate
    k = 0
    for f in Frequency:
        cmd = ':ATC:XPDR:FREQ ' + f 
        atc.gwrite(cmd)
        rm.logMessage(0,cmd)
        cmd = ':ATC:XPDR:POW ' + str(Init_PowelLevel)
        atc.gwrite(cmd)
        rm.logMessage(0,cmd)
       
        atc.waitforstatus()   
               
        #decrease power levels unit %replies < 90%
        Power_Level = Init_PowelLevel
        for i in range(0,15):
            
            replyrate = atc.getPercentReply(2)
            # fix for erroneous reply rate,try 10 times
            count = 0
            while replyrate[3] == -1.0 and count < 10:
                replyrate = atc.getPercentReply(2)
                count = count + 1
            
            val = replyrate[3]                                     #ModeS Bottom
            if val > 89.0:                                         #%replies
               Power_Level = Power_Level - 1.0
               cmd = ':ATC:XPDR:POW ' + str(Power_Level)
               #print(cmd)
               atc.gwrite(cmd)
               time.sleep(4)
                    
        #print result at this frequency
        rm.logMessage(2,("RESULT: Idx: %d, Frequency: %s, Power_Level: %f") % (k,f,Power_Level))
        Pwr_Levels[k] = Power_Level
        msg_str = "Freq: "+ f + " PowerLevel: " + str(Power_Level)
        rm.logMessage(0,"Test_2_3_2_1_Step1" + msg_str)    
        k=k+1
                 
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    #Adjust Power Levels for Path Loss
    Pwr_Levels[0] = Pwr_Levels[0] - PathLoss
    Pwr_Levels[1] = Pwr_Levels[1] - PathLoss
    Pwr_Levels[2] = Pwr_Levels[2] - PathLoss
    
    rm.logMessage(0,"Test_2_3_2_1_Step2 - " + str(Pwr_Levels) )    
    rm.logMessage(2,"Done, closing session")
   
    return Pwr_Levels

##############################################################################
#run as main from command line
if __name__ == "__main__":

    #SetUP Resource Manager
    rm = ate_rm()
 
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    
 
    res = Test_2_3_2_1_Step2(rm,atc_obj,-12.0)
    
    
    atc_obj.close()

    


