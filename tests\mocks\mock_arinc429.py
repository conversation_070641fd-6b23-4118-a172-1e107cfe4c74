#!/usr/bin/env python3
"""
Mock ARINC 429 Bus Interface for Testing
Simulates the behavior of ARINC 429 data bus communications
"""

import time
import random
from unittest.mock import Mock

class MockARINC429:
    """Mock implementation of ARINC 429 interface"""
    
    def __init__(self):
        self.connected = False
        self.channel_frequency = 111.90  # MHz
        self.distance = 34.0  # nautical miles
        self.ssm = "11"  # Sign/Status Matrix
        self.data_rate = 12.5  # kHz (low speed ARINC 429)
        
        # Simulation parameters
        self.communication_delay = 0.01  # Simulated bus communication delay
        self.measurement_variation = 0.1  # ±0.1 nm distance variation
        
    def connect(self, port="COM1", baudrate=115200):
        """Simulate connection to ARINC 429 interface"""
        time.sleep(0.1)
        self.connected = True
        print(f"Mock ARINC 429 connected to {port} at {baudrate} baud")
        return True
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        print("Mock ARINC 429 disconnected")
        
    def writeChannel(self, frequency):
        """Simulate writing channel frequency with optimized timing"""
        if not self.connected:
            raise Exception("ARINC 429 not connected")
            
        # Simulate optimized channel write (reduced delay)
        time.sleep(self.communication_delay)
        self.channel_frequency = frequency
        print(f"Mock ARINC 429: Channel frequency set to {frequency} MHz")
        
        # Simulate DME tuning time (reduced from 5s to 1s in optimizations)
        time.sleep(0.05)  # Simulated tuning time
        
    def getDistance_201(self):
        """Simulate getting distance from label 201 with optimized polling"""
        if not self.connected:
            raise Exception("ARINC 429 not connected")
            
        # Simulate optimized communication timing (reduced from 0.5s to 0.25s polling)
        time.sleep(self.communication_delay)
        
        # Add realistic distance variation
        variation = random.uniform(-self.measurement_variation, self.measurement_variation)
        measured_distance = self.distance + variation
        
        # Ensure distance stays within reasonable bounds
        measured_distance = max(33.5, min(34.5, measured_distance))
        
        print(f"Mock ARINC 429: Distance (Label 201): {measured_distance:.2f} nm")
        return measured_distance
        
    def getSSM(self):
        """Simulate getting Sign/Status Matrix"""
        if not self.connected:
            raise Exception("ARINC 429 not connected")
            
        time.sleep(self.communication_delay)
        
        # Simulate occasional SSM variations (mostly "11" = Normal Operation)
        ssm_options = ["11", "11", "11", "11", "10", "01"]  # Weighted toward normal
        current_ssm = random.choice(ssm_options)
        
        print(f"Mock ARINC 429: SSM: {current_ssm}")
        return current_ssm
        
    def getRangeRate(self):
        """Simulate getting range rate"""
        if not self.connected:
            raise Exception("ARINC 429 not connected")
            
        time.sleep(self.communication_delay)
        
        # Simulate range rate (typically small for stationary test)
        range_rate = random.uniform(-0.1, 0.1)  # ±0.1 nm/min
        
        print(f"Mock ARINC 429: Range rate: {range_rate:.3f} nm/min")
        return range_rate
        
    def getIdentifier(self):
        """Simulate getting DME identifier"""
        if not self.connected:
            raise Exception("ARINC 429 not connected")
            
        time.sleep(self.communication_delay)
        
        # Simulate DME identifier based on channel
        identifier = f"DME{int(self.channel_frequency)}"
        
        print(f"Mock ARINC 429: Identifier: {identifier}")
        return identifier
        
    def simulate_optimized_range_polling(self, duration=15.0):
        """
        Simulate the optimized range polling behavior
        This demonstrates the MEDIUM PRIORITY optimization in action
        """
        print("=== Simulating Optimized Range Polling ===")
        
        # Original polling: every 0.5 seconds for 15 seconds = 30 polls
        # Optimized polling: every 0.25 seconds for 15 seconds = 60 polls
        
        original_interval = 0.5
        optimized_interval = 0.25
        
        original_polls = int(duration / original_interval)
        optimized_polls = int(duration / optimized_interval)
        
        print(f"Original polling: {original_polls} polls over {duration}s")
        print(f"Optimized polling: {optimized_polls} polls over {duration}s")
        print(f"Polling frequency improvement: {optimized_polls / original_polls:.1f}x")
        
        # Simulate optimized polling
        start_time = time.time()
        correct_readings = 0
        total_readings = 0
        
        print("\nStarting optimized range polling...")
        
        end_time = start_time + duration
        while time.time() < end_time:
            # Get distance and SSM
            distance = self.getDistance_201()
            ssm = self.getSSM()
            
            # Check if reading is correct (within threshold and proper SSM)
            if ssm == "11" and 33.83 <= distance <= 34.17:
                correct_readings += 1
            
            total_readings += 1
            
            # Optimized polling interval
            time.sleep(optimized_interval - self.communication_delay)
            
        actual_duration = time.time() - start_time
        pass_rate = (correct_readings / total_readings) * 100 if total_readings > 0 else 0
        
        print(f"\nOptimized polling completed in {actual_duration:.2f}s")
        print(f"Total readings: {total_readings}")
        print(f"Correct readings: {correct_readings}")
        print(f"Pass rate: {pass_rate:.1f}%")
        print(f"Data collection improvement: {(optimized_polls - original_polls) / original_polls * 100:.1f}% more data points")
        
        return pass_rate, total_readings, actual_duration
        
    def simulate_channel_tuning_optimization(self):
        """
        Simulate the optimized channel tuning behavior
        """
        print("=== Simulating Optimized Channel Tuning ===")
        
        # Original tuning time: 5 seconds
        # Optimized tuning time: varies based on actual hardware response
        
        original_tuning_time = 5.0
        
        print(f"Original tuning time: {original_tuning_time}s")
        
        # Simulate optimized tuning
        start_time = time.time()
        
        # Set channel frequency
        self.writeChannel(134.40)  # DME Channel 56X
        
        # Simulate variable tuning time (realistic hardware behavior)
        actual_tuning_time = random.uniform(0.5, 2.0)  # 0.5-2.0 seconds
        time.sleep(actual_tuning_time - 0.05)  # Subtract the simulated delay from writeChannel
        
        optimized_time = time.time() - start_time
        
        print(f"Optimized tuning time: {optimized_time:.2f}s")
        print(f"Time savings: {original_tuning_time - optimized_time:.2f}s")
        print(f"Performance improvement: {((original_tuning_time - optimized_time) / original_tuning_time) * 100:.1f}%")
        
        return original_tuning_time, optimized_time
        
    def test_data_integrity(self, num_samples=100):
        """
        Test data integrity with optimized polling
        """
        print(f"=== Testing Data Integrity ({num_samples} samples) ===")
        
        start_time = time.time()
        
        distances = []
        ssms = []
        
        for i in range(num_samples):
            distance = self.getDistance_201()
            ssm = self.getSSM()
            
            distances.append(distance)
            ssms.append(ssm)
            
            # Use optimized polling interval
            time.sleep(0.01)  # Fast sampling for test
            
        test_time = time.time() - start_time
        
        # Analyze data quality
        valid_distances = [d for d in distances if 33.0 <= d <= 35.0]
        valid_ssms = [s for s in ssms if s in ["11", "10", "01", "00"]]
        
        distance_validity = len(valid_distances) / len(distances) * 100
        ssm_validity = len(valid_ssms) / len(ssms) * 100
        
        avg_distance = sum(distances) / len(distances)
        distance_std = (sum((d - avg_distance)**2 for d in distances) / len(distances))**0.5
        
        print(f"Test completed in {test_time:.2f}s")
        print(f"Distance validity: {distance_validity:.1f}%")
        print(f"SSM validity: {ssm_validity:.1f}%")
        print(f"Average distance: {avg_distance:.3f} nm")
        print(f"Distance std deviation: {distance_std:.3f} nm")
        print(f"Sampling rate: {num_samples / test_time:.1f} samples/second")
        
        return {
            'distance_validity': distance_validity,
            'ssm_validity': ssm_validity,
            'avg_distance': avg_distance,
            'std_deviation': distance_std,
            'sampling_rate': num_samples / test_time
        }


class MockARINC429Interface:
    """Alternative ARINC 429 interface implementation"""
    
    def __init__(self):
        self.arinc = MockARINC429()
        
    def open(self, device_id=0):
        """Open ARINC 429 device"""
        return self.arinc.connect(f"Device_{device_id}")
        
    def close(self):
        """Close ARINC 429 device"""
        self.arinc.disconnect()
        
    def transmit_label(self, label, data):
        """Transmit data on specific label"""
        print(f"Mock ARINC 429: Transmit Label {label}: {data}")
        time.sleep(0.01)
        
    def receive_label(self, label):
        """Receive data from specific label"""
        if label == 201:  # Distance
            return self.arinc.getDistance_201()
        elif label == 202:  # Range rate
            return self.arinc.getRangeRate()
        else:
            return 0.0


# Test function to demonstrate optimization benefits
def test_arinc429_optimization():
    """
    Test function to compare original vs optimized ARINC 429 operations
    """
    print("=== ARINC 429 Optimization Test ===")
    
    arinc = MockARINC429()
    arinc.connect()
    
    # Test channel tuning optimization
    original_time, optimized_time = arinc.simulate_channel_tuning_optimization()
    
    print(f"\n=== Testing Range Polling Optimization ===")
    
    # Test optimized range polling
    pass_rate, readings, duration = arinc.simulate_optimized_range_polling(15.0)
    
    print(f"\n=== Testing Data Integrity ===")
    
    # Test data integrity
    integrity_results = arinc.test_data_integrity(50)
    
    print(f"\n=== Optimization Summary ===")
    print(f"Channel tuning time savings: {original_time - optimized_time:.2f}s")
    print(f"Range polling improvement: 2x data collection rate")
    print(f"Data integrity maintained: {integrity_results['distance_validity']:.1f}% valid")
    
    arinc.disconnect()


if __name__ == "__main__":
    test_arinc429_optimization()
