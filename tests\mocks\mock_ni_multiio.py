#!/usr/bin/env python3
"""
Mock NI6363 MultiIO Handler for Testing
Simulates the behavior of National Instruments 6363 multifunction I/O device
"""

import time
import random
import numpy as np
from unittest.mock import Mock

class MockNI6363MultiIO:
    """Mock implementation of NI6363 multifunction I/O handler"""
    
    def __init__(self, resource_manager=None):
        self.resourceManager = resource_manager or Mock()
        self.connected = False
        self.device_name = "Dev1"
        
        # Analog input channels (32 channels)
        self.ai_channels = {}
        self.ai_voltage_range = (-10.0, 10.0)
        self.ai_sample_rate = 1000.0
        
        # Analog output channels (4 channels)
        self.ao_channels = {}
        self.ao_voltage_range = (-10.0, 10.0)
        
        # Digital input/output (32 lines)
        self.digital_lines = {}
        
        # Counter/timer channels (4 channels)
        self.counter_channels = {}
        
        # Initialize default states
        self._initialize_channels()
        
    def _initialize_channels(self):
        """Initialize all channels to default states"""
        # Analog inputs - simulate some realistic values
        for i in range(32):
            self.ai_channels[i] = random.uniform(-1.0, 1.0)
            
        # Analog outputs - initialize to 0V
        for i in range(4):
            self.ao_channels[i] = 0.0
            
        # Digital lines - initialize to False
        for i in range(32):
            self.digital_lines[i] = False
            
        # Counters - initialize to 0
        for i in range(4):
            self.counter_channels[i] = 0
            
    def connect(self, device_name="Dev1"):
        """Simulate connection to NI device"""
        time.sleep(0.1)  # Simulate connection time
        self.connected = True
        self.device_name = device_name
        self.resourceManager.logMessage(1, f"Mock NI6363 connected to {device_name}")
        return True
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        self.resourceManager.logMessage(1, "Mock NI6363 disconnected")
        
    def readAnalogInput(self, channel):
        """Read analog input channel"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        if 0 <= channel < 32:
            # Add some noise to simulate real measurements
            base_value = self.ai_channels[channel]
            noise = random.uniform(-0.01, 0.01)
            value = base_value + noise
            
            # Clamp to voltage range
            value = max(self.ai_voltage_range[0], min(self.ai_voltage_range[1], value))
            
            self.resourceManager.logMessage(1, f"Mock NI6363 AI{channel}: {value:.4f}V")
            return value
        else:
            raise ValueError(f"Invalid analog input channel: {channel}")
            
    def writeAnalogOutput(self, channel, voltage):
        """Write analog output channel"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        if 0 <= channel < 4:
            if self.ao_voltage_range[0] <= voltage <= self.ao_voltage_range[1]:
                self.ao_channels[channel] = voltage
                self.resourceManager.logMessage(1, f"Mock NI6363 AO{channel}: {voltage:.4f}V")
                time.sleep(0.001)  # Simulate settling time
            else:
                raise ValueError(f"Voltage out of range: {voltage}")
        else:
            raise ValueError(f"Invalid analog output channel: {channel}")
            
    def readAnalogInputs(self, channels, samples=1):
        """Read multiple analog input channels"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        data = {}
        for channel in channels:
            if samples == 1:
                data[channel] = self.readAnalogInput(channel)
            else:
                # Generate sample array
                base_value = self.ai_channels[channel]
                sample_data = []
                for _ in range(samples):
                    noise = random.uniform(-0.01, 0.01)
                    value = base_value + noise
                    value = max(self.ai_voltage_range[0], min(self.ai_voltage_range[1], value))
                    sample_data.append(value)
                data[channel] = sample_data
                
        time.sleep(samples * 0.001)  # Simulate acquisition time
        return data
        
    def writeAnalogOutputs(self, channel_voltage_pairs):
        """Write multiple analog output channels"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        for channel, voltage in channel_voltage_pairs.items():
            self.writeAnalogOutput(channel, voltage)
            
    def readDigitalLine(self, line):
        """Read digital input line"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        if 0 <= line < 32:
            value = self.digital_lines[line]
            self.resourceManager.logMessage(1, f"Mock NI6363 DI{line}: {value}")
            return value
        else:
            raise ValueError(f"Invalid digital line: {line}")
            
    def writeDigitalLine(self, line, state):
        """Write digital output line"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        if 0 <= line < 32:
            self.digital_lines[line] = bool(state)
            self.resourceManager.logMessage(1, f"Mock NI6363 DO{line}: {state}")
            time.sleep(0.001)  # Simulate switching time
        else:
            raise ValueError(f"Invalid digital line: {line}")
            
    def readDigitalPort(self, port_size=8):
        """Read digital port (8 or 16 bits)"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        value = 0
        for i in range(port_size):
            if self.digital_lines[i]:
                value |= (1 << i)
                
        self.resourceManager.logMessage(1, f"Mock NI6363 Digital Port: 0x{value:04X}")
        return value
        
    def writeDigitalPort(self, value, port_size=8):
        """Write digital port (8 or 16 bits)"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        for i in range(port_size):
            self.digital_lines[i] = bool(value & (1 << i))
            
        self.resourceManager.logMessage(1, f"Mock NI6363 Digital Port: 0x{value:04X}")
        time.sleep(0.001)
        
    def startCounter(self, counter, frequency=1000.0):
        """Start counter/timer"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        if 0 <= counter < 4:
            self.counter_channels[counter] = 0
            self.resourceManager.logMessage(1, f"Mock NI6363 Counter{counter} started at {frequency}Hz")
            time.sleep(0.01)  # Simulate start time
        else:
            raise ValueError(f"Invalid counter: {counter}")
            
    def stopCounter(self, counter):
        """Stop counter/timer"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        if 0 <= counter < 4:
            self.resourceManager.logMessage(1, f"Mock NI6363 Counter{counter} stopped")
            time.sleep(0.01)
        else:
            raise ValueError(f"Invalid counter: {counter}")
            
    def readCounter(self, counter):
        """Read counter value"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        if 0 <= counter < 4:
            # Simulate incrementing counter
            self.counter_channels[counter] += random.randint(1, 10)
            value = self.counter_channels[counter]
            self.resourceManager.logMessage(1, f"Mock NI6363 Counter{counter}: {value}")
            return value
        else:
            raise ValueError(f"Invalid counter: {counter}")
            
    def generateWaveform(self, channel, waveform_type="sine", frequency=1000.0, amplitude=1.0, samples=1000):
        """Generate waveform on analog output"""
        if not self.connected:
            raise Exception("NI6363 not connected")
            
        if 0 <= channel < 4:
            # Generate waveform data
            t = np.linspace(0, 1.0, samples)
            
            if waveform_type == "sine":
                waveform = amplitude * np.sin(2 * np.pi * frequency * t)
            elif waveform_type == "square":
                waveform = amplitude * np.sign(np.sin(2 * np.pi * frequency * t))
            elif waveform_type == "triangle":
                waveform = amplitude * (2 * np.arcsin(np.sin(2 * np.pi * frequency * t)) / np.pi)
            elif waveform_type == "sawtooth":
                waveform = amplitude * (2 * (frequency * t - np.floor(frequency * t + 0.5)))
            else:
                raise ValueError(f"Unknown waveform type: {waveform_type}")
                
            self.resourceManager.logMessage(1, 
                f"Mock NI6363 AO{channel} waveform: {waveform_type}, {frequency}Hz, {amplitude}V")
            
            # Simulate waveform output time
            time.sleep(samples / 10000.0)  # Assume 10kS/s output rate
            
            return waveform
        else:
            raise ValueError(f"Invalid analog output channel: {channel}")
            
    def setSampleRate(self, sample_rate):
        """Set analog input sample rate"""
        if sample_rate > 0:
            self.ai_sample_rate = sample_rate
            self.resourceManager.logMessage(1, f"Mock NI6363 sample rate: {sample_rate}S/s")
        else:
            raise ValueError(f"Invalid sample rate: {sample_rate}")
            
    def setVoltageRange(self, min_voltage, max_voltage):
        """Set analog input voltage range"""
        if min_voltage < max_voltage:
            self.ai_voltage_range = (min_voltage, max_voltage)
            self.resourceManager.logMessage(1, 
                f"Mock NI6363 voltage range: {min_voltage}V to {max_voltage}V")
        else:
            raise ValueError("Invalid voltage range")
            
    def reset(self):
        """Reset device to default state"""
        self.resourceManager.logMessage(1, "Mock NI6363 Reset")
        self._initialize_channels()
        self.ai_voltage_range = (-10.0, 10.0)
        self.ao_voltage_range = (-10.0, 10.0)
        self.ai_sample_rate = 1000.0
        time.sleep(0.1)  # Simulate reset time
        
    def close(self):
        """Close connection to device"""
        self.disconnect()
