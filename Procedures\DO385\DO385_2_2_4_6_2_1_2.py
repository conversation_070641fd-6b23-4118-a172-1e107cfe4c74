# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-185E/385 MOPs requirement for
             Mode C Surveillance Initiation, Section *******.2.1.2.
             
             The following procedures verify the ability of the Mode C reply processor
             to properly detect and decode reply signals, to resolve overlapping replies 
             and to reject narrow pulses.
             
             Step1: This step tests *******.6.1 ModeC Surveillance Initiation.
             This test verfies that the Mode C surveillance acquisition range is adequate and
             parameters of the acquisition logic are correct.
             The followin scenario file is used for this test:
             1) ModeC_Surv.csv --normal tracked
             2) ModeC_Surv.csv --not tracked, altitude reporting off
             3) ModeC_Surv.csv --not tracked, altitude varies > 300ft
          
             
             
INPUTS:      RGS,ARINC,Top_Cable_Loss,Bottom_Cable_Loss
OUTPUTS:     Pass/Fail for each scenario, arrays of 3 elements:
             -- Step1_Results[3]

HISTORY:

08/25/2020   MRS    Initial Release.
10/09/2020   NEZ    Changes for ate_rm
                                
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers.RGS2000NG import RGS2000NG  
from TXDLib.Handlers.ARINC_Client import ARINC_Client


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def readTCASData(ARINC):
    """ Reads data via ARINC client, returns 4 lists containing data
        in the order intruders, range, altitude, bearing"""
    #Read the Number of TCAS Intruders
    intr = ARINC.TCAS_Read(b"READ,TCAS_OUT,INT")
    print("Number of Intruders",intr)

    #Get a List of All TCAS Intruders
    data = ARINC.TCAS_Read(b"READ,TCAS_OUT,ALL")
    print("Intruder Return String: ",data)
    #Decode the Return string into separate lists
    itr,rng,alt,brg = ARINC.TCAS_Decode(data)
    return itr, rng, alt, brg

def testIntruders(ARINC, timeInterval):
    """ Record intruder data end of time interval and 
    tests that data matches. Returns 4 booleans in the order 
    intruders, range, altitude, bearing """

    time.sleep(timeInterval)
    
    
    # get intruder data after one timeInterval
    itr1, rng1, alt1, brg1 = readTCASData(ARINC)

    print ("Intruders: ",itr1)
    print ("Range: ",rng1)
    print ("Altitude: ",alt1)
    print ("Bearing: ",brg1)


    return itr1,rng1,alt1,brg1


##############################################################################
################# MAIN     ##################################################
##############################################################################
def Test_2_4_2_1_6_1(rm, rgs,ARINC):
    """ DO-185E/385, Mode C Surveillance Initiation, Sect  *******.2.1.2 """
    
    rm.logMessage(0, "*** DO-185E/385, Mode C Surveillance Initiation, Sect  *******.2.1.2***")
    
    
    rm.logMessage(0,"*Test_*******.2.1.2 - Start")  
    
    #Initialize results:
    Step1_Results = [0,0,0]
    
    #Initialize Aircraft Position for scenarios
    rgs.stopScen() #stop scenario if already running.
    rgs.init_own_aircraft_pos('11000')
    time.sleep(2)
    
    # Step1:Load and Start Scenario A, normal
    rm.logMessage(0,"Step1:Test_385_Mode C Surveillance Initiation - Scenario A") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODEC_SURV.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 1 :     #should be tracked
        Step1_Results[0] = 1
      
    # Step2:Load and Start Scenario B, altitude reporting turned off
    rm.logMessage(0,"Step2:Test_385_Mode C Surveillance Initiation - Scenario B") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODEC_SURV.csv')
    time.sleep(15)
    
    #Intruder Waypoints By Time Definition. 
    rgs.basicWrite(":RGS:SCE:WAYPOINTS:MODE TIME")
    rgs.basicWrite(":RGS:SCE:DYNAMIC:1:WAYPOINTS:NTIM 3")
    #Begin times
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:1:BEGIN 0.0")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:2:BEGIN 4.0")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:3:BEGIN 8.0")
    #Intruder Altitude  Reporting
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:1:PAR ALTRPT,ON")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:2:PAR ALTRPT,OFF")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:3:PAR ALTRPT,ON")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:1:PAR REPLY,ON")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:2:PAR REPLY,OFF")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:3:PAR REPLY,OFF")
    #Waypoint Enable
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:1:ENA ON")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:2:ENA ON")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:3:ENA ON")

    rgs.startScen()
    time.sleep(15)
    intr,rng,alt,brg = testIntruders(ARINC, 15)
    if len(intr) == 0 :   #should not be tracked
        Step1_Results[1] = 1
        
        
                
    # Step3:Load and Start Scenario C, altitude varies > 300ft
    rm.logMessage(0,"Step3:Test_385_Mode C Surveillance Initiation - Scenario C") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODEC_SURV.csv')
    time.sleep(15)
    
    #Intruder Waypoints By Time Definition. 
    rgs.basicWrite(":RGS:SCE:WAYPOINTS:MODE TIME")
    rgs.basicWrite(":RGS:SCE:DYNAMIC:1:WAYPOINTS:NTIM 3")
    #Begin times
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:1:BEGIN 0.0")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:2:BEGIN 4.0")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:3:BEGIN 8.0")
    #Intruder Altitude  Reporting
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:1:PAR VERTICAL,0")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:2:PAR VERTICAL,-32700")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:3:PAR VERTICAL,-32700")
    #Waypoint Enable
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:1:ENA ON")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:2:ENA ON")
    rgs.basicWrite(":RGS:SCE:DYN:1:WAY:TIME:3:ENA ON")
    
    
    rgs.startScen()
    time.sleep(15)
    intr,rng,alt,brg = testIntruders(ARINC, 15)
    if len(intr) == 0 :
        Step1_Results[2] = 1
        

    rm.logMessage(0,"Test_*******.2.1.2 - Done")    
    rgs.stopScen() #stop scenario if already running.
    
    print ("Step1_Results: ",Step1_Results)
    
    return Step1_Results

##############################################################################
#run as main from command line
if __name__ == "__main__":
    #SetUP Resource Manager
    rm = ate_rm()
   
    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")

    #Initiazlie the RGS
    rgs = RGS2000NG(rm)
  

    Test_2_4_2_1_6_1(rm, rgs, ARINC)
    
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()
    
    #Close RGS
    rgs.close()

