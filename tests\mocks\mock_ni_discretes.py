#!/usr/bin/env python3
"""
Mock NI6528 Discrete I/O Handler for Testing
Simulates the behavior of National Instruments 6528 discrete I/O device
"""

import time
import random
from unittest.mock import Mock

class MockNI6528Discretes:
    """Mock implementation of NI6528 discrete I/O handler"""
    
    def __init__(self, resource_manager=None):
        self.resourceManager = resource_manager or Mock()
        self.connected = False
        self.device_name = "Dev2"
        
        # Digital I/O configuration
        self.num_ports = 3  # Typically 3 ports (0, 1, 2)
        self.lines_per_port = 8  # 8 lines per port
        self.total_lines = self.num_ports * self.lines_per_port
        
        # Port and line states
        self.port_states = {}
        self.line_states = {}
        self.port_directions = {}  # True = Output, False = Input
        
        # Initialize default states
        self._initialize_ports()
        
    def _initialize_ports(self):
        """Initialize all ports and lines to default states"""
        for port in range(self.num_ports):
            self.port_states[port] = 0x00  # All lines low
            self.port_directions[port] = False  # Default to input
            
            # Initialize individual line states
            self.line_states[port] = {}
            for line in range(self.lines_per_port):
                self.line_states[port][line] = False
                
    def connect(self, device_name="Dev2"):
        """Simulate connection to NI device"""
        time.sleep(0.1)  # Simulate connection time
        self.connected = True
        self.device_name = device_name
        self.resourceManager.logMessage(1, f"Mock NI6528 connected to {device_name}")
        return True
        
    def disconnect(self):
        """Simulate disconnection"""
        self.connected = False
        self.resourceManager.logMessage(1, "Mock NI6528 disconnected")
        
    def setPortDirection(self, port, direction):
        """Set port direction (True=Output, False=Input)"""
        if not self.connected:
            raise Exception("NI6528 not connected")
            
        if 0 <= port < self.num_ports:
            self.port_directions[port] = direction
            dir_str = "OUTPUT" if direction else "INPUT"
            self.resourceManager.logMessage(1, f"Mock NI6528 Port{port} direction: {dir_str}")
            time.sleep(0.01)  # Simulate configuration time
        else:
            raise ValueError(f"Invalid port: {port}")
            
    def getPortDirection(self, port):
        """Get port direction"""
        if 0 <= port < self.num_ports:
            return self.port_directions[port]
        else:
            raise ValueError(f"Invalid port: {port}")
            
    def writePort(self, port, value):
        """Write value to entire port (8 bits)"""
        if not self.connected:
            raise Exception("NI6528 not connected")
            
        if 0 <= port < self.num_ports:
            if not self.port_directions[port]:
                raise Exception(f"Port {port} is configured as input")
                
            # Ensure value is within 8-bit range
            value = value & 0xFF
            self.port_states[port] = value
            
            # Update individual line states
            for line in range(self.lines_per_port):
                self.line_states[port][line] = bool(value & (1 << line))
                
            self.resourceManager.logMessage(1, f"Mock NI6528 Port{port} write: 0x{value:02X}")
            time.sleep(0.001)  # Simulate write time
        else:
            raise ValueError(f"Invalid port: {port}")
            
    def readPort(self, port):
        """Read value from entire port (8 bits)"""
        if not self.connected:
            raise Exception("NI6528 not connected")
            
        if 0 <= port < self.num_ports:
            if self.port_directions[port]:
                # For output ports, return the written value
                value = self.port_states[port]
            else:
                # For input ports, simulate some input activity
                # Add some random changes to simulate real inputs
                if random.random() < 0.1:  # 10% chance of change
                    bit_to_toggle = random.randint(0, 7)
                    current_value = self.port_states[port]
                    new_value = current_value ^ (1 << bit_to_toggle)
                    self.port_states[port] = new_value
                    
                    # Update line state
                    self.line_states[port][bit_to_toggle] = bool(new_value & (1 << bit_to_toggle))
                    
                value = self.port_states[port]
                
            self.resourceManager.logMessage(1, f"Mock NI6528 Port{port} read: 0x{value:02X}")
            return value
        else:
            raise ValueError(f"Invalid port: {port}")
            
    def writeLine(self, port, line, state):
        """Write state to individual line"""
        if not self.connected:
            raise Exception("NI6528 not connected")
            
        if 0 <= port < self.num_ports and 0 <= line < self.lines_per_port:
            if not self.port_directions[port]:
                raise Exception(f"Port {port} is configured as input")
                
            # Update line state
            self.line_states[port][line] = bool(state)
            
            # Update port state
            if state:
                self.port_states[port] |= (1 << line)
            else:
                self.port_states[port] &= ~(1 << line)
                
            self.resourceManager.logMessage(1, f"Mock NI6528 Port{port}.{line}: {state}")
            time.sleep(0.001)
        else:
            raise ValueError(f"Invalid port/line: {port}/{line}")
            
    def readLine(self, port, line):
        """Read state from individual line"""
        if not self.connected:
            raise Exception("NI6528 not connected")
            
        if 0 <= port < self.num_ports and 0 <= line < self.lines_per_port:
            if not self.port_directions[port]:
                # For input lines, add some random activity
                if random.random() < 0.05:  # 5% chance of change
                    self.line_states[port][line] = not self.line_states[port][line]
                    # Update port state accordingly
                    if self.line_states[port][line]:
                        self.port_states[port] |= (1 << line)
                    else:
                        self.port_states[port] &= ~(1 << line)
                        
            state = self.line_states[port][line]
            self.resourceManager.logMessage(1, f"Mock NI6528 Port{port}.{line}: {state}")
            return state
        else:
            raise ValueError(f"Invalid port/line: {port}/{line}")
            
    def writeMultipleLines(self, port_line_states):
        """Write multiple lines simultaneously"""
        if not self.connected:
            raise Exception("NI6528 not connected")
            
        for (port, line), state in port_line_states.items():
            self.writeLine(port, line, state)
            
    def readMultipleLines(self, port_line_list):
        """Read multiple lines simultaneously"""
        if not self.connected:
            raise Exception("NI6528 not connected")
            
        results = {}
        for port, line in port_line_list:
            results[(port, line)] = self.readLine(port, line)
            
        return results
        
    def setBit(self, port, bit_position):
        """Set a specific bit in a port"""
        if 0 <= port < self.num_ports and 0 <= bit_position < self.lines_per_port:
            current_value = self.port_states[port]
            new_value = current_value | (1 << bit_position)
            self.writePort(port, new_value)
        else:
            raise ValueError(f"Invalid port/bit: {port}/{bit_position}")
            
    def clearBit(self, port, bit_position):
        """Clear a specific bit in a port"""
        if 0 <= port < self.num_ports and 0 <= bit_position < self.lines_per_port:
            current_value = self.port_states[port]
            new_value = current_value & ~(1 << bit_position)
            self.writePort(port, new_value)
        else:
            raise ValueError(f"Invalid port/bit: {port}/{bit_position}")
            
    def toggleBit(self, port, bit_position):
        """Toggle a specific bit in a port"""
        if 0 <= port < self.num_ports and 0 <= bit_position < self.lines_per_port:
            current_value = self.port_states[port]
            new_value = current_value ^ (1 << bit_position)
            self.writePort(port, new_value)
        else:
            raise ValueError(f"Invalid port/bit: {port}/{bit_position}")
            
    def getBit(self, port, bit_position):
        """Get state of a specific bit in a port"""
        if 0 <= port < self.num_ports and 0 <= bit_position < self.lines_per_port:
            current_value = self.readPort(port)
            return bool(current_value & (1 << bit_position))
        else:
            raise ValueError(f"Invalid port/bit: {port}/{bit_position}")
            
    def pulseOutput(self, port, line, pulse_width_ms=10):
        """Generate a pulse on an output line"""
        if not self.connected:
            raise Exception("NI6528 not connected")
            
        if 0 <= port < self.num_ports and 0 <= line < self.lines_per_port:
            if not self.port_directions[port]:
                raise Exception(f"Port {port} is configured as input")
                
            # Set line high
            self.writeLine(port, line, True)
            
            # Wait for pulse width
            time.sleep(pulse_width_ms / 1000.0)
            
            # Set line low
            self.writeLine(port, line, False)
            
            self.resourceManager.logMessage(1, 
                f"Mock NI6528 Pulse on Port{port}.{line}: {pulse_width_ms}ms")
        else:
            raise ValueError(f"Invalid port/line: {port}/{line}")
            
    def getAllPortStates(self):
        """Get all port states"""
        states = {}
        for port in range(self.num_ports):
            states[port] = self.readPort(port)
        return states
        
    def reset(self):
        """Reset device to default state"""
        self.resourceManager.logMessage(1, "Mock NI6528 Reset")
        self._initialize_ports()
        time.sleep(0.1)  # Simulate reset time
        
    def close(self):
        """Close connection to device"""
        self.disconnect()
