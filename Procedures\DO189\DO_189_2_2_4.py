# -*- coding: utf-8 -*-
"""
Created on Tues March 3 3:02:30 2020

@author: E589493
         K<PERSON> <PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-189 MOPs requirement for
             Interrogator Pulse Coding, Section 2.2.4
             
             "The interrogation signal shall comprise a pair of pulses having 
             intervals of either 12 +/- 0.25 or 36 +/- 0.25 microseconds 
             between points on the leading edges of the pulses which the
             amplitude is 50% of the maximum pulse voltage amplitude.
             
INPUTS:      Top_Cable_Loss, ATC, ARINC server, scope, count (set in teststand, typically 5)
OUTPUTS:     Pulse spacing from trigDetect

HISTORY:

0
3/03/2020   KF    Initial Release.
06/22/2020  AS    Added tvl statements, Added ARINC
03/10/2021  MRS   Updates for new handlers and Lobster.
10/2023     CS    Updated to use ATC instead of Scope for Pulse Characteristics. Don't currently have a reliable trigger
                  mechanism when using TXD flight s/w.
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers.ARINC_Client import ARIN<PERSON>_Client
from TXDLib.Handlers.D3054Scope import D3054Scope
       

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def runScope(scope_obj):
    scope_obj.trigRun()

def init_DME_Standard(cable_loss, atc, ARINC):
    """ Sets DME Standard COnditions to DME Channel 56X at -70dBm adn 34nm, Squidder rate 
    of 2700 """
    ARINC.writeChannel(111.90)
    time.sleep(5)
    atc.DMEMode()
    atc.gwrite(":ATC:DME:CABLELOSS " + str(cable_loss))

def set_VOR_PAIR5(atc, ARINC):
    ARINC.writeChannel(117.95)
    time.sleep(5)
    atc.gwrite(":ATC:DME:CHANNEL:MODE 5VOR")
    time.sleep(1)
    atc.gwrite(":ATC:DME:CHANNEL 117.9")
    time.sleep(10)

def scope_Setup(scope_obj,trigger,timescale,threshold):
    """ Basic Scope setup for DME Pulse """
    #Display Chan 2 and 3 (only use 3 for this test)
    scope_obj.chanDisplay(1,0)
    scope_obj.chanDisplay(2,1)
    scope_obj.chanDisplay(3,1)
    scope_obj.chanDisplay(4,0)
    #Set the Scale
    scope_obj.voltDiv(1, 100, "mV")
    scope_obj.voltDiv(2, 100, "mV")
    scope_obj.voltDiv(3, 20, "mV")
    scope_obj.voltDiv(4, 20, "mV")
    #Digitize all chanels
    #scope_obj.Digitize()
    #Invert Chan 3 and 4
    scope_obj.invertChan(3,1)
    scope_obj.invertChan(4,1)
    #Set Impdance 3 and 4 to 50 Ohms
    scope_obj.setChanImpdance(3,5)
    scope_obj.setChanImpdance(4,5)
    #Set TimeScale and Trigger Level
    scope_obj.timeScale(timescale, "us")
    scope_obj.trigSource(trigger)
    scope_obj.trigType("EDGE")
    scope_obj.setEdgeTrigger(trigger, threshold, "mV")   #chan 3 is trigger
    scope_obj.trigRun()


def trigDetect(scope_obj, threshold, count):
    """ Detecs edges and sends back the time difference"""
    result = -1
    k = 0

    #try count times
    while ((k < count) and (result == -1)):
        #Trigger on single
        scope_obj.trigSingle(1)
        print('trig single complete')
        time.sleep(2)
        pEDge, fEdge = scope_obj.digiEdgePos(threshold/1000.0,source=3)
        time.sleep(2)
        if (len(pEDge) + len(fEdge)) == 4:
            return  pEDge[1] - pEDge[0]
        k=k+1

    return  0
##############################################################################
################# Main Test Sequence #########################################
##############################################################################
def Test_2_2_4(rm,ARINC,atc):

    #Initialize DME
    ARINC.writeChannel(134.40)
    atc.DMEMode()

    #Initialize Scope
    #scope = D3054Scope(rm)
    #scope.Reset()

    Xspacing = 0.0
    Yspacing = 0.0
    sf = 1.0e-3                             #Scale factor (convert nsec to usec)

    #Measure Channel X Spacing
    atc.waitforstatus()  
    time.sleep(2)
    atc.gwrite(":ATC:DME:PULSE P2")      #Measure 2nd pulse
    time.sleep(5)
    atc.gwrite(":ATC:MEA:DFORMAT 2")      #Measure 2nd pulse
    time.sleep(0.3)
    Xspacing = atc.getPulsePosition(2)
    time.sleep(1)
    rm.logMessage(0,("Xspacing: " + str(Xspacing)))
        
    # fix for erroneous response
    count = 0
    while (Xspacing == 0.0 or Xspacing == 20) and count < 10:
        time.sleep(1)
        Xspacing = atc.getPulsePosition(2)
        count = count + 1
    
    time.sleep(0.3)
    Xspacing = (Xspacing * sf)
    rm.logMessage(0,("Xspacing Final: " + str(Xspacing)))
  
    #Measure Channel Y SPacing
    atc.waitforstatus()  
    time.sleep(2)
    ARINC.writeChannel(117.95)
    time.sleep(5)
    set_VOR_PAIR5(atc, ARINC)
    time.sleep(1)
    Yspacing = atc.getPulsePosition(2)
    time.sleep(1)
    rm.logMessage(0,("Yspacing: " + str(Yspacing)))
        
    # fix for erroneous response
    count = 0
    while (Yspacing == 0.0 or Yspacing == 20) and count < 10:
        time.sleep(1)
        Yspacing = atc.getPulsePosition(2)
        count = count + 1
    
    time.sleep(0.3)
    Yspacing = (Yspacing * sf)
    rm.logMessage(0,("Yspacing Final: " + str(Yspacing)))

    atc.gwrite(':ATC:DME:STOP')
   
    return [Xspacing] + [Yspacing]

##############################################################################

def main():

    #Initialize Intruuments
    rm = ate_rm()

    #Initiazlie the ATC
    atc = ATC5000NG(rm)
    atc.Reset()    
    atc.DMEMode()

    #Initialize Scope
    #scope = D3054Scope(rm)
    #scope.Reset()

    Xspacing = 0
    Yspacing = 0
    sf = 1.0e-3                             #Scale factor (convert nsec to usec)

    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")
    time.sleep(5)

    #Measure Channel X Spacing
    atc.waitforstatus()  
    time.sleep(2)
    Xspacing = atc.getPulsePosition(2)
    time.sleep(1)
    rm.logMessage(0,("Xspacing: " + str(Yspacing)))
        
    # fix for erroneous response
    count = 0
    while (Xspacing == 0.0 or Xspacing == 20) and count < 10:
        time.sleep(1)
        Xspacing = atc.getPulsePosition(2)
        count = count + 1
    
    time.sleep(0.3)
    Xspacing = (Xspacing * sf)
    rm.logMessage(0,("Xspacing Final: " + str(Yspacing)))
    '''
    trigger = 3
    timescale = 10.0
    threshold = 20.0
    scope_Setup(scope,trigger,timescale,threshold)
    time.sleep(1)
    result = trigDetect(scope, threshold, 5)
    print("Chan X Spacing: ",result)
    time.sleep(.1)
    '''

    #Measure Channel Y SPacing
    atc.waitforstatus()  
    time.sleep(2)
    set_VOR_PAIR5(atc, ARINC)
    time.sleep(1)
    Yspacing = atc.getPulsePosition(2)
    time.sleep(1)
    rm.logMessage(0,("Yspacing: " + str(Yspacing)))
        
    # fix for erroneous response
    count = 0
    while (Yspacing == 0.0 or Yspacing == 20) and count < 10:
        time.sleep(1)
        Yspacing = atc.getPulsePosition(2)
        count = count + 1
    
    time.sleep(0.3)
    Yspacing = (Yspacing * sf)
    rm.logMessage(0,("Yspacing Final: " + str(Yspacing)))

    '''
    trigger = 3
    timescale = 10.0
    threshold = 20.0
    scope_Setup(scope,trigger,timescale,threshold)
    result = trigDetect(scope, threshold, 5)
    print("Chan Y Spacing: ",result)
    time.sleep(.1)
	'''

    atc.gwrite(':ATC:DME:STOP')
    atc.close()
    scope.close()
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()

    return [Xspacing] + [Yspacing]

if __name__ == "__main__":
    main()
