import time, math, sys

def enable_4VDC_UUT(aterm):
    aterm.logMessage(1, "Procedure Started")
    dcps=aterm.instruments["dcps"]
    voltage = 4.2
    currLim = 4.0
    chnl = 2

    dcps.setVolt(voltage, chnl)
    dcps.setCurrLim(currLim, chnl)
    time.sleep(0.5)
    dcps.setOutputState("ON", chnl)
    aterm.logMessage(1, "Procedure Ended")

def disable_4VDC_UUT(aterm):
    aterm.logMessage(1, "Procedure Started")
    dcps=aterm.instruments["dcps"]
    chnl = 2
    
    dcps.setOutputState("OFF", chnl)
    aterm.logMessage(1, "Procedure Ended")

def enable_58VDC_UUT(aterm):
    aterm.logMessage(1, "Procedure Started")
    dcps=aterm.instruments["dcps"]
    voltage = 58
    currLim = 0.5
    chnl = 3
    slewrate = 10
    
    dcps.setVolt(voltage, chnl)
    dcps.setCurrLim(currLim, chnl)
    dcps.setSlew(slewrate, chnl)
    time.sleep(0.5)
    dcps.setOutputState("ON", chnl)
    aterm.logMessage(1, "Procedure Ended")

def disable_58VDC_UUT(aterm):
    aterm.logMessage(1, "Procedure Started")
    dcps=aterm.instruments["dcps"]
    chnl = 3
    slewrate = 10

    dcps.setSlew(slewrate, chnl)
    dcps.setOutputState("OFF", chnl)
    aterm.logMessage(1, "Procedure Ended")

def setup_UUT_Power(aterm):
    aterm.logMessage(1, "Procedure Started")
    dcps=aterm.instruments["dcps"]
    voltage = 58
    currLim = 0.5
    chnl = 3
    slewrate = 10
    
    dcps.setVolt(voltage, chnl)
    dcps.setCurrLim(currLim, chnl)
    dcps.setSlew(slewrate, chnl)
    time.sleep(0.5)

    voltage = 4.2
    currLim = 4.0
    chnl = 2

    dcps.setVolt(voltage, chnl)
    dcps.setCurrLim(currLim, chnl)
    time.sleep(0.5)

    aterm.logMessage(1, "Procedure Ended")
