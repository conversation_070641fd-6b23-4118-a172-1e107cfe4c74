# -*- coding: utf-8 -*-
"""
Created on Thu Apr 30 06:59:45 2020

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
DESCRIPTION:
    Decode ATC5000ng and RGS2000ng Data Logs (see below):
     
INPUTS: Command Line input: FileName (file to be procesed)
OUTPUTS: FileName_Out.txt  (output text file)

HISTORY:

01/19/2021  MRS  Updates.
  

"""

"""
NOTES:

 TTG raw message format (INPUT)
 ------------------------------

 01 <USER> <GROUP> 64DF24 00000004 000000A295D6

 03 00000000000000 80000004 B0ADE5 03FFFFFF 000000001441
  |  |             |           |       |         |
  |  |             |           |       |         + Time in 1/4 msec
  |  |             |           |       |        
  |  |             |           |       + Decoded Address         
  |  |             |           |
  |  |             |           + AP | PI (Address & Parity)
  |  |             |
  |  |             + Start of short messages
  |  |      
  |  +  If block != All 0s => Long message (data starts @ 3rd char)
  |     If block == All 0s => Short Message (data starts @ 17th char)
  | 
  + Type: 1 => DF from UUT (Mode S Reply)
          2 => DF from UUT (ATCRBS Reply) 
          3 => UF from UUT (Mode S Inter)
          4 => UF from UUT (ATCRBS Inter) 
          5 => DF from Intruder (Mode S Reply)
          6 => DF from Intruder (ATCRBS Reply) 
          7 => UF from Intruder (Mode S Inter)
          8 => UF from Intruder (ATCRBS Inter)

  The message is a HEX string representation of the message with the first byte:
  starting at the 3rd character (short message - 56 bits) or at the 17th character 
  (long message - 112 bits).
  The first byte is encoded 1000 0xxx:  5 MSB => UF|DF number (example 16)
  
  Unpack the pieces based on the sample format
   type   long         short  parity  ? address   time 
   03 00000000000000 80000004 B0ADE5 03 FFFFFF 000000001441
   2        14           8      6     2   6         12

"""


#Required Libraries
from TXDLib.Handlers import Decode_Common as dc

##############################################################################
################ Global Constants ############################################
##############################################################################

# Mode S SubFields  (from DO-181E)
AA  = 24
AC  = 13
AQ  = 1
AP  = 24     # AP not used in here
BDS = 8
CA  = 3
CC  = 1
DF  = 5
DI  = 3
DR  = 5
FS  = 3
ID  = 13
II  = 4
MA  = 56
MB  = 56
ME  = 56
MU  = 56
MV  = 56
PC  = 3
PI  = 24
PR  = 4
RI  = 4
RL  = 1
RR  = 5
SD  = 16
SL  = 3
UF  = 5
UM  = 6
VS  = 1

# UF messages structure (Mode S Interrogation - DO181E Figure 2-5)
UF0  = [UF,   3 , RL,   4 , AQ, BDS, 10]
UF4  = [UF, PC, RR, DI, SD]
UF5  = [UF, PC, RR, DI, SD]
UF11 = [UF, PR, II,  19]
UF16 = [UF,   3 , RL,   4 , AQ,  18, MU]
UF20 = [UF, PC, RR, DI, SD, MA]
UF21 = [UF, PC, RR, DI, SD, MA]

# DF meassages structure (Mode S Replies - DO 181E Figure 2-6)
DF0  = [DF, VS, CC,   1 , SL,    2 , RI,  2 , AC]
DF4  = [DF, FS, DR, UM, AC]
DF5  = [DF, FS, DR, UM, ID]
DF11 = [DF, CA, AA]
DF16 = [DF, VS,   2 , SL,   2 , RI,   2 , AC, MV]
DF17 = [DF, CA, AA, ME]
DF20 = [DF, FS, DR, UM, AC, MB]
DF21 = [DF, FS, DR, UM, ID, MB]



##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
 
def data_log_decode(line):
    """ 
    Partially Decodes The Data Line from Log File. 
    Parses string into parts: type, longpart, shortpart, parity, GenRecv, address and
    time.  Then decodes each part of message. Converts Hex String to Binary Strings for decode.
    """

    #fields for ModeS
    line = line.strip()
    ty = line[0:2]                #type
    longPart = line[2:16]         #long ModeS
    shortPart = line[16:24]       #short ModeS
    parity = line[24:30]          #Mode S Parity
    loc = line[30:32]             #Gen/Rcv Locations
    address = line[32:38]         #ModeS Adrs
    time = line[38:50]            #time


    ###################################################
    # Step through each reply and generate data message

    ##-------------------ModeS Reply-------------------## 
    if (ty == '01'):
        line = line + " : " + 'ModeS Reply'
        
        if longPart == '00000000000000':        #short reply
            shortBin = dc.hex2bin(shortPart)
            parityBin = dc.hex2bin(parity)
            DF = shortBin[0:5]
            line = line + decodeDFShort(DF, shortBin, parityBin) + " ICAO: " + dc.icao(line[2:30])
        else:                    #long reply
            shortBin = dc.hex2bin(shortPart)
            longBin = dc.hex2bin(longPart)
            DF = longBin[0:5]
            dataBin = longBin + shortBin
            parityBin =dc.hex2bin(parity)
            line = line + " : Msg " + decodeDFLong(DF, dataBin, parityBin)  + " ICAO: " + dc.icao(line[2:30])
        
        line = line + getLocStatusUUT(dc.hex2bin(loc))
        line = line + " : Adrs " + address
    ##-------------------------------------------------##

    ##------------------ATCRBS Reply-------------------##        
    if (ty == '02'):
        line = line + " : " + 'ATCRBS Reply'

        #decode squawk or altitude
        msg = line[5:8]
        d1 = dc.hex2bin(msg)
        #create z
        d1 = d1[0:6] + '0' + d1[6:12]
        #Cant tell if ModeA or ModeC
        d2 = dc.squawk(d1)
        d3 = str(dc.altitude(d1))

        dataBin = dc.hex2bin((longPart + shortPart))
        line = line + " : Swk: " + d2 + " Alt: " + d3 + decodeATCRBS(dataBin)
        line = line + getLocStatusUUT(dc.hex2bin(loc))
    ##-------------------------------------------------##
        
    if (ty == '03'):
        line = line + " : " + 'ModeS Inter'
    
    if (ty == '04'):
        line = line + " : " + 'ATCRBS Inter'
    
    ##--------------ATC/RGS ModeS Reply---------------##
    if (ty == '05'):
        line = line + " : " + 'ATC/RGS ModeS Reply'

        if longPart == '00000000000000':        #short reply
            shortBin = dc.hex2bin(shortPart)
            parityBin = dc.hex2bin(parity)
            DF = shortBin[0:5]
            line = line + " : Msg " + decodeDFShort(DF, shortBin, parityBin)
        else:                    #long reply
            shortBin = dc.hex2bin(shortPart)
            longBin = dc.hex2bin(longPart)
            dataBin = longBin + shortBin
            parityBin = dc.hex2bin(parity)
            line = line + " : Msg " + decodeDFLong(DF, dataBin, parityBin)
        line = line + getLocStatusATC(dc.hex2bin(loc))
    ##-------------------------------------------------##
    
    ##--------------ATC/RGS ATCRBS REPLY---------------##
    if (ty == '06'):
        line = line + " : " + 'ATC/RGS ATCRBS Reply'

        dataBin = dc.hex2bin((longPart + shortPart))
        line = line + " : Msg " + decodeATCRBS(dataBin)
        line = line + getLocStatusATC(dc.hex2bin(loc))
    ##-------------------------------------------------##

    if (ty == '07'):
        line = line + " : " + 'ATC Mode S Inter'
    
    if (ty == '08'):
        line = line + " : " + 'ATC ATCRBS Inter'
    
    ######################################################

    #last 5 bytes, timestamp
    
    i = int(time,16)      #base 16 not 16 chars
    tt = i*(25.0 / 1000000000)
    tts = str(tt)
    line = line + '  Time: ' + tts[0:9]
    line = line + " . \n"
    return line



   

def decodeDFShort(DF, data, add_par):
    """ Returns data for short messages given DF code"""


    if (DF == "00000"):
        # Short Air-Air Surveillance (ACAS)
        return ": Short Air to Air VS : " + str(int(data[5:6], 2)) + " CC " + str(int(data[6:7], 2)) + " : SL " + str(int(data[8:11],2)) + " : RI " + str(int(data[13:17], 2)) + " : AC " + str(int(data[19:32], 2))
    elif (DF == "00001"):
        # TODO
        return ": DF1 "
    elif (DF == "00010"):
        # TODO
        return ": DF2 "
    elif (DF == "00011"):
        # TODO
        return ": DF3 "
    elif (DF == "00100"):
        # Surveillance, Altitude Reply
        return ": DF4 Surveillance, Altitude Reply  FS: " + str(int(data[5:8], 2)) + "  DR: " + str(int(data[8:13], 2)) + "  UM: " + str(int(data[13:19], 2)) + "  AC: " + str(dc.altitude(data[19:32])) 
    elif (DF == "00101"):
        # Surveillance, Identity Reply
        return ": DF5 Surveillance, Identity Reply  FS: " + str(int(data[5:8], 2)) + "  DR: " + str(int(data[8:13], 2)) + "  UM: " + str(int(data[13:19], 2)) + "  ID: " + dc.squawk(data[19:32]) 
    elif (DF == "00110"):
        # TODO
        return ": DF6"
    elif (DF == "00111"):
        # TODO
        return ": DF7"
    elif (DF == "01000"):
        # TODO
        return ": DF8"
    elif (DF == "01001"):
        # TODO
        return ": DF9"
    elif (DF == "01010"):
        # TODO
        return ": DF10"
    elif (DF == "01011"):
        # All-Call Reply 
        return ": DF11: Mode S All Call : CA " + str(int(data[5:8], 2)) + " : AA " + str(int(data[8:32], 2)) + " : PI " + add_par
    elif (DF == "01100"):
        # TODO
        return ": DF12"
    elif (DF == "01101"):
        # TODO
        return ": DF13"
    elif (DF == "01110"):
        # TODO
        return ": DF14"
    elif (DF == "01111"):
        # TODO
        return ": DF15"
    elif (DF == "10110"):
        # Reserved for Miliary Use
        return ": Long Air to Air ACAS"
    elif (DF == "10111"):
        # Comm-D (ELM)
        return ": 1090 Extended Squitter"

def decodeDFLong(DF, data, add_par):
    """ Returns data for long messages given DF code """
    if (DF == "00001"):
        # TODO
        return ": DF1"
    elif (DF == "00010"):
        # TODO
        return ": DF2"
    elif (DF == "00011"):
        # TODO
        return ": DF3"
    elif (DF == "00110"):
        # TODO
        return ": DF6"
    elif (DF == "00111"):
        # TODO
        return ": DF7"
    elif (DF == "01000"):
        # TODO
        return ": DF8"
    elif (DF == "01001"):
        # TODO
        return ": DF9"
    elif (DF == "01010"):
        # TODO
        return ": DF10"
    elif (DF == "01100"):
        # TODO
        return ": DF12"
    elif (DF == "01101"):
        # TODO
        return ": DF13"
    elif (DF == "01110"):
        # TODO
        return ": DF14"
    elif (DF == "01111"):
        # TODO
        return ": DF15"
    elif (DF == "10000"):
        # Long Air-Air Surveillance (TCAS)
        return ": DF16 Long Air-Air Surveillance (TCAS) : VS " + str(int(data[5:6])) + " : SL " + str(int(data[8:11])) + " : RI " + str(int(data[13:17])) + " : AC " + str(int(data[19:32])) + " : MV " + str(int(data[32:88]))
    elif (DF == "10001"):
        # Extended Squitter
        return ": DF17 Extended Squitter : CA " + str(int(data[5:8])) + " : AA " + str(int(data[8:32])) + " : ME " + str(int(data[32:88])) + " : PI " + add_par
    elif (DF == "10010"):
        # Extended Squitter/Non-Transponder
        return ": DF18 CF " + str(int(data[5:8])) + ": AA " + str(int(data[8:32])) + " : ME " + str(int(data[32:88])) + " : PI " + add_par
    elif (DF == "10011"):
        # Miliarty Application
        return ": DF19 Military Extended Squitter AF : " + str(int(data[5:8])) + " : Military Application: " + str(int(data[8:112])) + " ME: " + str(int(data[32:88]))
    elif (DF == "10100"):
        # Comm-B, Altitude Reply
        return ": DF20 Comm-B, Altitude Reply  FS: " + str(int(data[5:8])) + "  DR: " + str(int(data[8:13])) + "  UM: " + str(int(data[13:19])) + "  AC: " + str(dc.altitude(data[19:32])) + " MB: " + str(int(data[32:88])) 
    elif (DF == "10101"):
        # Comm-B, Identity Reply
        return ": DF21 Comm-B, Identity Reply  FS: " + str(int(data[5:8])) + "  DR: " + str(int(data[8:13])) + " UM: " + str(int(data[13:19])) + " ID: " + dc.squawk(data[19:32]) + " MB: " + str(int(data[32:88]))
    elif (DF == "10110"):
        # Reserved for Miliary Use
        return ": Reserved for Military Use"
    elif (DF == "10111"):
        return ": DF23 "
    return ": ERROR, Couldn't Interpret Data"

def decodeATCRBS(dataBin):
        generator = int(dataBin[0:4], 2)          # 0 = start of Byte 2
        generator = chr(generator + 65)           # Convert to ASCII
        SPI = dataBin[10:11]                      # 10 = Byte 3, bit 5
        X = dataBin[11:12]                        # 11 = Byte 3, bit 4
        # C1A1 = dataBin[12:16]
        return  "  SPI: " + SPI + " Generator: "+ generator + "  X: " + X

def getLocStatusUUT(data):
    line = ""
    if (data[0:1] == 1):                       # Bit 7
        line += " : Top Receiver"
    else :
        line += " : Bottom Receiver"
    return line

def getLocStatusATC(data):
    generator_int = int(data[1:4], 2)            # get generator code number
    generator_char = chr(generator_int + 65)     # convert code to ascii letter
    return " : Generator " + generator_char





##############################################################################
################# MAIN     ##################################################
##############################################################################
def main(f_in_name):
# Get the Command Line Arguments
    

    f_out_name = f_in_name[:-4] + "out.log"
    fin = open(f_in_name, 'r')
    fout = open(f_out_name, 'w')

    k = 0
    line_decode = ''
    for line in fin:
        
        if (k == 0):               #skip first line
            k = k + 1
        else:
            line_decode = data_log_decode(line)
            fout.write(line_decode)

    fin.close()
    fout.close()


if __name__ == "__main__":
    

    f_in_name = "FAR43_G_UF21.log"
    
    main(f_in_name)

    
