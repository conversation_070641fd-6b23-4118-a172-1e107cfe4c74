''' Instrument Handler for NI PXIe-6363 Multi-IO Card '''

import time, nidaqmx

class NI6363MultiIO():
    def __init__(self, ate_rm):
        try:
            self.NI6363 = None
            self.resourceManager = ate_rm
            # Ensures we can find card even if installed in wrong slot
            for device in list(nidaqmx.system.system.System.local().devices):
                if device.product_type == "PXIe-6363":
                    self.NI6363 = nidaqmx.system.device.Device(device.name)
            if self.NI6363 == None:
                self.resourceManager.logMessage(3, "Couldn't find PXIe-6363")
            else:
                self.resourceManager.logMessage(0, "Resource Opened")
        except:
            self.resourceManager.logMessage(3, "Resource Failed to Open")
            raise
    
    def ident(self):
        model = self.NI6363.product_type
        name = self.NI6363.name
        self.resourceManager.logMessage(1, "Model: {} Name: {}".format(str(model), str(name)))
        return (model, name)
    
    def reset(self):
        self.resourceManager.logMessage(2, "Resetting Device...")
        self.NI6363.reset_device()
        self.resourceManager.logMessage(2, "Device Reset")

    def selfTest(self):
        self.resourceManager.logMessage(1, "Performing Self Test...")
        self.NI6363.self_test_device()
        self.resourceManager.logMessage(1, "Self Test completed")

    # Basic Analog I/O Operations

    ''' Read analog voltage in Volts on one or more input channels '''
    def readVoltage(self, channel, terminalConfig=-1, minVal=-5.0, maxVal=5.0):
        with nidaqmx.Task() as readTask:
            readTask.ai_channels.add_ai_voltage_chan("{}/ai{}".format(self.NI6363.name, channel), terminal_config=terminalConfig, min_val=minVal, max_val=maxVal)
            data = readTask.read()
        self.resourceManager.logMessage(1, "Analog voltage read")
        return data

    ''' Read waveform frequency in Hertz on one or more channels '''
    def readWaveform(self, channel, minVal=1, maxVal=100):
        with nidaqmx.Task() as readTask:
            readTask.ai_channels.add_ai_freq_voltage_chan("{}/ai{}".format(self.NI6363.name, channel), min_val=minVal, max_val=maxVal)
            data = readTask.read()
        self.resourceManager.logMessage(1, "Waveform frequency read")
        return data

    ''' Send one or more voltage samples to one or more channels '''
    def writeVoltage(self, channel, data, minVal=-10.0, maxVal=10.0):
        with nidaqmx.Task() as writeTask:
            writeTask.ao_channels.add_ao_voltage_chan("{}/ao{}".format(self.NI6363.name, channel), min_val=minVal, max_val=maxVal)
            samples = writeTask.write(data)
        self.resourceManager.logMessage(1, "Sent " + str(samples) + " voltage samples")
        return samples

    ''' Send one or more waveform samples to one or more channels '''
    def writeWaveform(self, channel, data, frequency, amplitude, offset=0.0):
        with nidaqmx.Task() as writeTask:
            writeTask.ao_channels.add_ao_func_gen_chan("{}/ao{}".format(self.NI6363.name, channel), freq=frequency, amplitude=amplitude, offset=offset)
            samples = writeTask.write(data)
        self.resourceManager.logMessage(1, "Sent " + str(samples) + " waveform samples")
        return samples
