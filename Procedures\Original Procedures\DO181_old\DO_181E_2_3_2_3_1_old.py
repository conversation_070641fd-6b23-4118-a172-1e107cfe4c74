# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Reply Pulse Characteristic, Section *******.1
             
             Step1:ATCRBS Reply Pulse Spacing and Width: Interrogate with a standard Mode A interrogation
			 Uas a 15-pulse reply group (7777 with SPI).  Measure the time between first and last framing
			 pulses.  The time shall be 20.30 +/- 0.10 microseconds.  Measure the width of the first and
			 last pulses.  This width shall be 0.45 +/- 0.10 microseconds.  Observe that all code pulses are of
			 equal width and stable in position with the first pulse. 
             
             Step2:ATCRBS Reply Delay and Jitter: Interrogate with a standard Mode A interrogation.
             Measure the average delay between the leading edge of P3 and the leading edge of the first reply pulse at
             the 50% amplitued pointes and the extreme positions of the leading edge of the first reply pulse at
             signal levels of MTL + 3dB, -50dBm, and -21dBm
              
             
INPUTS:      RM, AT<PERSON>, Scope, PathLoss
OUTPUTS:     'PulseWidth' -- Width of Mode A Reply Pulse (0.45usec)
             'ModeAMsgWidth' -- Width of ModeA Message (20.3usec)
             'ReplyDelays' -- Array of Reply Delays at 3 Power Levels

             Note: Testsand will set the Digital BOB to Aux In 1/2 for Trig 1/2 respectively.
                   The RF BOB is set to Prim Bot (default)
                   The Scope is set to Trigger on Chan 2 and measure on Chan3, in addition,
                   Chan 3 is set to 50 Ohms and Inverted.


HISTORY:

04/13/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review     
08/06/2020   AKS    Added comments per review 
03/01/2021   MRS    Updates for New Handlers                           
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import B4500CPwrMeter
from TXDLib.Handlers.D3054Scope import D3054Scope


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def modeX_Setup(scope_obj,timescale,threshold,trigger):
    """ Basic Scope setup for Mode A Pulse. 
   Chan 2: Trigger, Chan3: Data ... RFBOB must be set up apriori."""
    
    scope_obj.Reset()
    #Display Chan 2 and 3 (only use Chan 2,3 for this test)
    scope_obj.chanDisplay(1,0)
    scope_obj.chanDisplay(2,1)
    scope_obj.chanDisplay(3,1)
    scope_obj.chanDisplay(4,0)
    #Set the Scale
    scope_obj.voltDiv(1, 100, "mV")
    scope_obj.voltDiv(2, 100, "mV")
    scope_obj.voltDiv(3, 20, "mV")
    scope_obj.voltDiv(4, 20, "mV")
    #Digitize all chanels
    #scope_obj.Digitize()
    #Invert Chan 3 and 4
    scope_obj.invertChan(3,1)
    scope_obj.invertChan(4,1)
    #Set Impdance 3 and 4 to 50 Ohms
    scope_obj.setChanImpdance(3,5)
    scope_obj.setChanImpdance(4,5)
    #Set TimeScale and Trigger Level
    scope_obj.timeScale(timescale, "us")
    scope_obj.trigSource(trigger)
    scope_obj.trigType("EDGE")
    scope_obj.setEdgeTrigger(trigger, threshold, "mV")   #chan 2 is trigger
    scope_obj.trigRun()

def measurePulseChar(scope_obj, edgePPosition, source = 1):
    """Function that measures PWidth, Rise, and fall for a given Pulse rising edge position.
    Fall time is taken by moving the cursor edgePPosition + PWidth. """

    #Measure Chan 3
    scope_obj.setMeasureSource(source)
    result = []

    scope_obj.setTimePosition(edgePPosition)
    result.append(scope_obj.measPWidth())
    scope_obj.timeScale(.5, "us")
    time.sleep(.1)
    result.append(scope_obj.measRiseTime())
    scope_obj.setTimePosition(edgePPosition + result[0])
    time.sleep(.1)
    result.append(scope_obj.measFallTime())
    time.sleep(.1)

    return result  

def trigDetect(scope_obj):
    """ Function that triggers a given waveform and returns all detected edge positions based
    on trigger position. """

    #Trigger Source Chan is set in setup (modex_setup)
    if scope_obj.trigSingle(2):
        return 1
    return 0


##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_3_1(rm,atc,scope,PathLoss):
    """ DO-181E, Reply Pulse Characteristics: Sect *******.1 """
    
    rm.logMessage(2,"*** DO-181E, Reply Pulse Characteristics: Sect *******.1 ***\r\n")
    
    #Initialize Results and Power Levels --  values output to TestStand
    PulseWidth = 0.0                         #Width of Mode A Reply Pulse (0.45usec)
    ModeAMsgWidth = 0.0                      #Width of ModeA Message (20.3)
    ReplyDelays = [0.0,0.0,0.0]              #Reply Delays at 3 Power Levels
    #Initialize Power Levels
    PowerLevels = [-73.0, -50.0, -21.0]

    #Adjust Power Levels by RF Path Loss
    PowerLevels[0] = PowerLevels[0] + PathLoss
    PowerLevels[1] = PowerLevels[1] + PathLoss
    PowerLevels[2] = PowerLevels[2] + PathLoss

      
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(5)
    
    #STEP 1: Get Mode A Pulse Parameters       
    #Set Up the OScope for Pulse Measurements   
    sf = 1.0e6                 #scale factor (usec)
    tmescale = 10.0            #scope time scale (usec/div)
    threshold = 50.0           #threshold (mV)
    trigger = 2
    modeX_Setup(scope,tmescale,threshold,trigger)
    time.sleep(5)
    #Get Mode A Pulse Parameters    
    if trigDetect(scope) == 1:
        # Threshold is in Volts, essentially a y-scale line that if a rising edge passes it is then registered. IE 20mV
        PEdges,NEdges = scope.digiEdgePos(10/1000.0,source = 3)
        scope.plotWave(3)               #comment this out when running from TestStand        
       
        #Number of Positive/Negative Edges
        rm.logMessage(0,("PEdges: %d,%s " % (len(PEdges),str(PEdges))))    
        rm.logMessage(0,("NEdges: %d,%s" % (len(NEdges),str(NEdges))))
        if len(PEdges) == 0:
            rm.logMessage(3,"Test_2_3_2_3_1 - Error, No Edges Detected")
        else:
            #Loop thru Positive Edges and gather pulse data
            for edge_pos in PEdges:
                #print ("EdgePos: ",edge_pos)
                pulse_char = measurePulseChar(scope, edge_pos,3)
                rm.logMessage(0,"Pulse duration: " + str(pulse_char[0]))
                
            # Uncomment for module built in plot
            scope.timeScale(tmescale,"us")
            scope.setTimePosition(PEdges[0])
        
            
            #Evaluate Pulses for Results
            nn = len(PEdges)
            if (nn > 10):      #We have Mode A Reply
                ModeAMsgWidth = (PEdges[nn-1]- PEdges[0]) * sf
                rm.logMessage(0,("*** Framing PulseWidth ***"+str(ModeAMsgWidth)))
                PulseWidth = (NEdges[nn-1] - PEdges[nn-1]) * sf - 0.3  #using last framing pulse, 0.3 is 6dBm Power point.
                rm.logMessage(0,("*** ModeA Pulse Width ***" + str(PulseWidth)))

    
    atc.waitforstatus()            
    rm.logMessage(0,"Test_2_3_2_3_1 - Begin Power Loop")    

    
    #STEP 2: Get Reply Delays at three Power Levels 
    k=0      
    for Pwr in PowerLevels:
        #Set the Power Level
        cmd = ':ATC:XPDR:POW ' + str(Pwr)
        rm.logMessage(0,("PowerLevel: " + str(Pwr)))
        atc.gwrite(cmd)
        time.sleep(15)
    
        atc.waitforstatus()            
        
        #Get Reply Delay
        val = atc.getReplyDelay(2)
        rm.logMessage(0,("VAL: " + str(val)))
        
        # fix for erroneous reply delay
        count = 0
        while val == 20.0 and count < 10:
            val = atc.getReplyDelay(2)
            count = count + 1

        
        if atc.is_number(val):
            ReplyDelays[k] = val
        k=k+1
        
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    rm.logMessage(0,"Test_2_3_2_3_1 : Done, MsgWidth: " + str(ModeAMsgWidth) + ", Widths: " + str(PulseWidth) + ", Delays: " + str(ReplyDelays))   
    rm.logMessage(2,"Done, closing session")

    return [ModeAMsgWidth] + [PulseWidth] + ReplyDelays

#########################################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

     #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

    scope_obj = D3054Scope(rm)
    scope_obj.Reset()
    
    res = Test_2_3_2_3_1(rm,atc_obj,scope_obj,12.0)
    
    
    scope_obj.close()
    atc_obj.close()

