# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Pulse Decoder Characterics, Section *******

			 Step7: Short Pulse Rejection ATCRBS Type Interrogations
             For each interrogation type (ModeA, ModeC/S), set P1 duration
             to 0.25usec and verify that < 10% replies are returned.
             Repeat for P3 at 0.25usec.
             Repeat the two tests above at Power Levels of MTL+3,-60 and
             -45 dBm.
             
             
INPUTS:      RM, ATC, PathLoss
OUTPUTS:     ModeA_Replies - array of %replies for P1,P3 duration, repeated for each of three power levels.
             ModeCS_Replies - array of %replies for P1,P3 duration, repeated for each of three power levels.

HISTORY:

04/23/2020   MRS    Initial Release.
05/11/2020   MRS    Cleanup
03/03/2021   MRS    Updates for new Handlers and Lobster.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def vary_P1(atc,rm,duration):
    cmd = ":ATC:XPDR:PUL:P1WIDTH " + str(duration)
    print("P1Width Cmd: ",cmd)
    rm.logMessage(0,"Test_2_3_2_5_Step7" + cmd)   
    atc.gwrite(cmd)
    time.sleep(15)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(1)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    
    print("Reply Rate: ",replyrate)
    
    rmax = max(replyrate)
    
    return rmax


def vary_P3(atc,rm,duration):
    cmd = ":ATC:XPDR:PUL:P3WIDTH " + str(duration)
    print("P3Width Cmd: ",cmd)
    rm.logMessage(0,"Test_2_3_2_5_Step7" + cmd)   
    atc.gwrite(cmd)
    time.sleep(15)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(1)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    
    print("Reply Rate: ",replyrate)
    
    rmax = max(replyrate)
    
    return rmax


##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_5_Step7(rm, atc,PathLoss):
    
    rm.logMessage(2,"*** DO-181E, Pulse Decoder Characterics: Sect *******_Step7 ***")
        
    #Results read by TestStand
    Power_Levels = ['-73.0', '-60.0', '-45.0']
    ModeA_Replies =  [0.0,0.0,0.0,0.0,0.0,0.0]    # array of %replies for P1,P3 duration, repeated for each of three power levels.
    ModeCS_Replies = [0.0,0.0,0.0,0.0,0.0,0.0]   # array of %replies for P1,P3 duration, repeated for each of three power levels.
    
    #Adjust Power Levels by PathLoss
    Power_Levels[0]  = str(float(Power_Levels[0]) + PathLoss)
    Power_Levels[1]  = str(float(Power_Levels[1]) + PathLoss)
    Power_Levels[2]  = str(float(Power_Levels[2]) + PathLoss)

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Loop thru the three power levels
    k=0    
    for P in Power_Levels:
    
        #Set Up Transponder -- ModeA
        atc.transponderModeA()
        atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     
        cmd = ':ATC:XPDR:POW ' + str(P)
        atc.gwrite(cmd)
        rm.logMessage(0,"Test_2_3_2_5_Step7a ModeA" + cmd)   
        
        #Turn on RF
        atc.gwrite(":ATC:XPDR:RF ON")
        time.sleep(15)
        atc.waitforstatus()
        
        #Vary P1
        ModeA_Replies[0+k] = vary_P1(atc,rm,0.25)    
        atc.gwrite(":ATC:XPDR:PUL:P1WIDTH 0.8")    #restore P1
        time.sleep(1)
        #Vary P3
        ModeA_Replies[1+k] = vary_P3(atc,rm,0.25)    
        atc.gwrite(":ATC:XPDR:PUL:P3WIDTH 0.8")    #restore P3
        time.sleep(1)
              
        #Turn Off RF
        atc.gwrite(":ATC:XPDR:RF OFF")
        rm.logMessage(0,"Test_2_3_2_5_Step7a - Done")    
    
        #Set Up Transponder -- ModeC/S
        atc.transponderModeCS()
        cmd = ':ATC:XPDR:POW ' + str(P)
        atc.gwrite(cmd)
        print(cmd)
        rm.logMessage(0,"Test_2_3_2_5_Step7b ModeC/S" + cmd)   
        
        #Turn on RF
        atc.gwrite(":ATC:XPDR:RF ON")
        time.sleep(15)
        atc.waitforstatus()
        
        #Vary P1
        ModeCS_Replies[0+k] = vary_P1(atc,rm,0.25)    
        atc.gwrite(":ATC:XPDR:PUL:P1WIDTH 0.8")    #restore P1
        time.sleep(1)
        #Vary P3
        ModeCS_Replies[1+k] = vary_P3(atc,rm,0.25)    
        atc.gwrite(":ATC:XPDR:PUL:P3WIDTH 0.8")    #restore P3
        time.sleep(1)
            
        #Turn Off RF
        atc.gwrite(":ATC:XPDR:RF OFF")
        rm.logMessage(0,"Test_2_3_2_5_Step7b - Done")
        
        #increment index
        k=k+1


    rm.logMessage(0,"Test_2_3_2_5_Step7 - Done")    
    
    rm.logMessage(2,"Done, closing session")

    
    return ModeA_Replies + ModeCS_Replies
##########################################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

     
    res = Test_2_3_2_5_Step7(rm,atc_obj,12.0)
    
    atc_obj.close()

