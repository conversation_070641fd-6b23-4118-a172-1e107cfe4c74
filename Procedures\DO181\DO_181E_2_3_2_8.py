# -*- coding: utf-8 -*-
"""

@author: <PERSON><PERSON>2068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Undesired Replies, Section *******
             
			 With no interrogations count the number of replies for a minimum of
			 one minute.
             
             
INPUTS:      RM, ATC, PathLoss
OUTPUTS:     ReplyRates - accumulates reply rates, if any, for ATCRBS and ModeS (top and bottom)
             'do181_2_3_2_8.log'

HISTORY:

04/27/2020   MRS    Initial Release.
05/11/2020   MRS    Cleanup.
03/03/2021   MRS    Updates for new Handlers and Lobster.
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG


##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_8(rm,atc,PathLoss):

    rm.logMessage(2,"*** DO-181E, Undesired Replies: Sect ******* ***")   
   
    #Results read by TestStand
    ReplyRates = [0.0,0.0,0.0,0.0]

    
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
	
    #Set Up Transponder -- ModeA/S
    atc.transponderModeAS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     
    atc.gwrite(":ATC:XPDR:POW -100.0")
    rm.logMessage(0,"Test_2_3_2_8 ModeA/S - Begin Timing Loop")   
        
    #Turn on RF power, but set power level really low
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    atc.waitforstatus()

    #start data logging
    atc.data_log_start()
    
    #Loop for one minute making Reply readings every 5 seconds
    r1=0.0
    r2=0.0
    r3=0.0
    r4=0.0
    for t1 in range(12):
 
        atc.waitforstatus()
        
        replyrate = atc.getPercentReply(2)
        # fix for erroneous reply rate
        count = 0
        while replyrate[1] == -1.0 and count < 10:
            replyrate = atc.getPercentReply(2)
            count = count + 1
    		
		#accumulate any reply data
        r1 = r1 + replyrate[0]
        r2 = r2 + replyrate[1]
        r3 = r3 + replyrate[2]
        r4 = r4 + replyrate[3]
            
        time.sleep(5)
        msg = "Indx: " + str(t1*5)
        rm.logMessage(0,"Test_2_3_2_8" + msg)    

    
    #Accumulated reply rates
    ReplyRates = [r1,r2,r3,r4]
    
    #stop recording and download data, log file generated for verification.
    atc.data_log_stop("do181_2_3_2_8.log")
    time.sleep(1)

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    rm.logMessage(0,"Test_2_3_2_8 - Done")    


    rm.logMessage(2,"Done, closing session")
    
    return ReplyRates
##########################################################################################

#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

     
    res = Test_2_3_2_8(rm,atc_obj,12.0)
    
    atc_obj.close()

