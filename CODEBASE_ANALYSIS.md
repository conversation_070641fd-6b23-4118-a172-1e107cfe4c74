# TXD (Transponder/DME) Qualification Test System - Codebase Analysis

## Executive Summary

The ProcedureQual repository contains a comprehensive automated test system for qualifying aircraft transponder and DME (Distance Measuring Equipment) units according to various aviation standards including DO-181E, DO-189, DO-282, DO-385, and FAR-43. This system is developed by Honeywell's CNS (Communication, Navigation, Surveillance) Qualification Test Group and provides automated testing capabilities for avionics equipment qualification.

## System Architecture Overview

The system follows a modular architecture with two main components:

### 1. **Handlers** (Instrument Control Layer)
- **Purpose**: Provides low-level instrument control and communication interfaces
- **Location**: `./Handlers/` directory
- **Functionality**: Abstracts hardware interfaces for test equipment including power meters, signal generators, spectrum analyzers, scopes, and specialized aviation test equipment

### 2. **Procedures** (Test Implementation Layer)
- **Purpose**: Implements specific test procedures according to aviation standards
- **Location**: `./Procedures/` directory
- **Functionality**: Contains test scripts that orchestrate multiple instruments to perform qualification tests

## Core System Components

### Resource Management Framework

#### ATE Resource Manager (`ate_rm.py`)
- **Primary Class**: `ate_rm()`
- **Purpose**: Central resource manager for all test equipment
- **Key Features**:
  - VISA resource management using PyVISA
  - Integration with Honeywell's Lobster4 Observer system for logging
  - Centralized instrument registry
  - Automatic cleanup and resource management

```python
class ate_rm():
    def __init__(self):
        self.rm = pyvisa.ResourceManager()
        self.tvl = ObserverClientWrapper()  # Honeywell logging system
        self.instruments = {}
```

### Test Equipment Handlers

#### 1. **ATC5000NG** - Aviation Test Controller
- **File**: `Handlers/ATC5000NG.py`
- **Purpose**: Controls Honeywell ATC5000NG aviation test equipment
- **Communication**: Socket-based TCP/IP (*************:2001)
- **Capabilities**:
  - Transponder mode simulation (Mode A, Mode C, Mode S)
  - DME interrogation and response
  - Aircraft position simulation
  - Reply rate measurement
  - Pulse timing analysis

#### 2. **B4500CPwrMeter** - Boonton Power Meter
- **File**: `Handlers/B4500CPwrMeter.py`
- **Purpose**: RF power measurement and pulse analysis
- **Communication**: VISA TCP/IP (************)
- **Capabilities**:
  - Dual-channel power measurement
  - Pulse detection and analysis
  - Marker-based measurements
  - Time-domain analysis
  - Peak detection algorithms

#### 3. **RGS2000NG** - Radar Target Generator
- **File**: `Handlers/RGS2000NG.py`
- **Purpose**: Simulates radar targets and intruders for TCAS testing
- **Capabilities**:
  - Scenario-based target simulation
  - Multiple intruder generation
  - TCAS test scenarios

#### 4. **ARINC_Client** - ARINC 429 Interface
- **File**: `Handlers/ARINC_Client.py`
- **Purpose**: ARINC 429 bus communication for avionics data
- **Communication**: TCP socket to ARINC server (127.0.0.1:20633)
- **Capabilities**:
  - DME channel control
  - TCAS mode control
  - Data encoding/decoding
  - Bus monitoring

#### 5. **Additional Instruments**
- **N9010BSpecAn**: Spectrum analyzer control
- **N5172BSigGen**: Signal generator control
- **NIDMM**: Digital multimeter
- **NI6363MultiIO**: National Instruments multifunction I/O
- **Pickering**: Switching matrix control
- **RFBOB/DigitalBOB**: RF and digital breakout boxes

### Test Procedures by Standard

#### DO-181E (Mode S Transponder MOPS)
- **Location**: `Procedures/DO181/`
- **Test Categories**:
  - **Receiver Characteristics** (*******): Sensitivity, frequency response
  - **Pulse Decoder Characteristics** (*******): Pulse timing tolerances
  - **Diversity Operation** (*******0): Dual-channel operation
  - **Reply Transmission** (*******2): Reply timing and format

#### DO-189 (DME MOPS)
- **Location**: `Procedures/DO189/`
- **Test Categories**:
  - DME receiver sensitivity
  - Frequency accuracy
  - Reply timing
  - Signal processing

#### DO-282 (UAT MOPS)
- **Location**: `Procedures/DO282/`
- **Test Categories**:
  - UAT receiver performance
  - Forward Error Correction (FEC)
  - Message processing

#### DO-385 (TCAS MOPS)
- **Location**: `Procedures/DO385/`
- **Test Categories**:
  - Radiated output power
  - Receiver selectivity
  - Intruder processing

#### FAR-43 (Transponder Performance)
- **Location**: `Procedures/FAR43/`
- **Test Categories**:
  - Reply frequency accuracy
  - Power output
  - Sensitivity
  - Mode S addressing
  - Squitter operation

## Key System Features

### 1. **Automated Test Execution**
- Scripted test procedures following aviation standards
- Automated data collection and analysis
- Pass/fail determination with configurable limits
- Comprehensive logging and traceability

### 2. **Multi-Standard Support**
- Supports multiple aviation standards simultaneously
- Modular test procedure design
- Configurable test parameters
- Standard-specific reporting

### 3. **Real-Time Monitoring**
- Built-in Test (BIT) monitoring system
- Real-time voltage and temperature monitoring
- FPGA-based data acquisition
- UDP-based telemetry

### 4. **Data Management**
- CSV-based data logging
- Automated file naming with timestamps
- Configurable data directories
- Test result archival

## Technical Implementation Details

### Communication Protocols
- **VISA**: Standard instrument communication
- **TCP/IP Sockets**: Custom instrument protocols
- **ARINC 429**: Aviation bus communication
- **UDP**: Real-time telemetry

### Programming Languages
- **Primary**: Python 3.7+
- **Supporting**: Go (monitoring applications)
- **Libraries**: PyVISA, NumPy, Matplotlib, SciPy

### Network Configuration
- **ATC5000NG**: *************:2001
- **Power Meter**: ************
- **BIT System**: ************:5121
- **ARINC Server**: 127.0.0.1:20633

### File Structure
```
ProcedureQual/
├── Handlers/                    # Instrument control layer
│   ├── ate_rm.py               # Resource manager
│   ├── ATC5000NG.py           # Aviation test controller
│   ├── B4500CPwrMeter.py      # Power meter
│   ├── ARINC_Client.py        # ARINC 429 interface
│   └── [other instruments]
├── Procedures/                  # Test procedures
│   ├── DO181/                  # Mode S transponder tests
│   ├── DO189/                  # DME tests
│   ├── DO282/                  # UAT tests
│   ├── DO385/                  # TCAS tests
│   ├── FAR43/                  # Transponder performance
│   ├── BIT.py                  # Built-in test
│   ├── Calibration.py          # Calibration procedures
│   └── [utility modules]
```

## Data Flow Architecture

### Test Execution Flow
1. **Initialization**: Resource manager creates instrument connections
2. **Configuration**: Test-specific instrument setup
3. **Execution**: Automated test sequence execution
4. **Measurement**: Data collection from multiple instruments
5. **Analysis**: Real-time data processing and limit checking
6. **Reporting**: Results logging and file generation
7. **Cleanup**: Resource deallocation and instrument reset

### Measurement Chain
```
UUT (Unit Under Test) → RF Switching → Test Equipment → Data Acquisition → Analysis → Reporting
```

## Integration Points

### Honeywell Lobster4 Integration
- Observer client for centralized logging
- Test execution monitoring
- Result database integration
- Traceability and audit trails

### TestStand Integration
- Designed for National Instruments TestStand
- Modular test sequence support
- Parameter passing and result collection
- Automated report generation

## Quality Assurance Features

### Error Handling
- Comprehensive exception handling
- Instrument communication timeouts
- Automatic retry mechanisms
- Graceful degradation

### Validation
- Instrument self-test capabilities
- Calibration verification
- Data integrity checks
- Limit validation

### Traceability
- Detailed logging at multiple levels
- Timestamp-based file naming
- Test configuration recording
- Result archival

## Maintenance and Support

### Version Control
- Original and archived versions maintained
- Backward compatibility considerations
- Change tracking and documentation

### Documentation
- Inline code documentation
- Test procedure specifications
- Instrument interface documentation
- System configuration guides

## Future Considerations

### Scalability
- Modular architecture supports new instruments
- Standard-agnostic procedure framework
- Configurable test parameters
- Extensible data formats

### Technology Updates
- Modern Python library integration
- Enhanced error handling
- Improved user interfaces
- Cloud-based data management

## Detailed Component Analysis

### Handler Implementation Details

#### Power Meter Control (B4500CPwrMeter.py)
The Boonton 4500C Power Meter handler provides sophisticated RF power measurement capabilities:

**Key Methods:**
- `fetch1_pwr()` / `fetch2_pwr()`: Retrieve power measurements with pulse parameters
- `CH1_Marker_Power()` / `CH2_Marker_Power()`: Marker-based power readings
- `findpeaks()`: Automated pulse detection using SciPy algorithms
- `plotpeaks()`: Visualization of detected pulses
- `getnumpulses()`: Pulse counting with configurable thresholds

**Measurement Capabilities:**
- Pulse peak power, cycle average, on-time average
- IEEE top/bottom measurements
- Overshoot detection
- Rise/fall time analysis
- Pulse width and frequency measurements

#### ATC5000NG Communication Protocol
The aviation test controller uses a custom socket-based protocol:

**Connection Management:**
```python
HOST = '*************'
PORT = 2001
self.atc = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
self.atc.settimeout(1)  # 1-second timeout
```

**Command Structure:**
- Commands terminated with `\r`
- Response parsing with error checking
- Automatic retry mechanisms for failed communications
- Status monitoring with `waitforstatus()` method

**Transponder Modes:**
- Mode A: Basic transponder operation
- Mode C/S: Altitude reporting
- Mode S: Extended squitter and addressing
- DME: Distance measuring equipment simulation

#### ARINC 429 Data Bus Interface
The ARINC_Client provides aviation data bus communication:

**Data Encoding/Decoding:**
- 32-bit word structure with label, SDI, data, and SSM fields
- Binary-coded decimal (BCD) encoding for aviation data
- Automatic parity calculation
- Label bit reversal for ARINC 429 compliance

**DME Channel Control:**
```python
def writeChannel(self, channel):
    # Convert channel to BCD format
    d1 = int(channel * .1) % 10    # 10's place
    d2 = int(channel) % 10         # 1's place
    d3 = int(channel * 10) % 10    # .1's place
    d4 = 1 if (int(channel * 100) % 10) == 5 else 0
```

### Procedure Implementation Patterns

#### Standard Test Structure
All test procedures follow a consistent pattern:

1. **Initialization Phase:**
   - Resource manager setup
   - Instrument configuration
   - Test parameter validation

2. **Execution Phase:**
   - Systematic parameter sweeps
   - Automated measurements
   - Real-time data validation

3. **Analysis Phase:**
   - Statistical analysis
   - Limit checking
   - Pass/fail determination

4. **Cleanup Phase:**
   - Instrument reset
   - Resource deallocation
   - Data archival

#### Example: DO-181E Sensitivity Test
```python
def Test_2_3_2_1_Step1(rm, atc, PathLoss):
    # Test frequencies: 1029.8, 1030.0, 1030.2 MHz
    Frequency = ['1029.80', '1030.00', '1030.20']
    Pwr_Levels = [0.0, 0.0, 0.0]

    for f in Frequency:
        # Set frequency and initial power
        atc.gwrite(':ATC:XPDR:FREQ ' + f)

        # Decrease power until reply rate < 90%
        while replyrate > 89.0:
            Power_Level -= 1.0
            atc.gwrite(':ATC:XPDR:POW ' + str(Power_Level))
            replyrate = atc.getPercentReply(2)[1]
```

### Built-In Test (BIT) System

#### Real-Time Monitoring Architecture
The BIT system provides continuous health monitoring:

**Data Acquisition:**
- UDP-based telemetry (************:5121)
- FPGA-based analog-to-digital conversion
- Base64 encoded data transmission
- Real-time voltage and temperature monitoring

**Signal Processing:**
- Two's complement conversion for signed values
- Polynomial calibration curves (4th order)
- Multi-point calibration coefficients
- Temperature compensation

**Monitored Parameters:**
- Power supply voltages (+50V, +30V, +5V, +3.3V, +1.8V, +1.0V, -5V, -280V)
- Temperature sensors (PA, capacitor, MEZALOK)
- LO power levels (TX, RX, DME)
- FPGA internal voltages (VCCINT, VCCAUX, VCCBRAM)

### Calibration and Compression Testing

#### RF Calibration Procedures
The calibration system supports comprehensive RF characterization:

**Transmit Path Calibration:**
- Frequency sweep from 1025-1150 MHz
- Power sweep with variable attenuators
- Dual-channel (E1/E2) measurements
- Phase control for each pulse

**Compression Analysis:**
- P1dB compression point determination
- Linear gain region identification
- Automatic compression detection
- Statistical analysis of power curves

**Switch Matrix Control:**
- Antenna selection (top/bottom)
- Monitor path routing
- Calibration bracket control
- Mode switch configuration

### Error Handling and Robustness

#### Communication Error Recovery
- Socket timeout handling with automatic reconnection
- Instrument communication retry mechanisms
- Data validation with error flagging
- Graceful degradation for partial failures

#### Data Integrity
- Checksum validation for critical measurements
- Range checking for all parameters
- Automatic outlier detection
- Measurement repeatability verification

### Integration with Test Frameworks

#### TestStand Compatibility
- Modular function design for easy integration
- Standardized parameter passing
- Consistent return value formats
- Error code propagation

#### Logging and Traceability
- Multi-level logging (info, warning, error)
- Timestamp-based file organization
- Test configuration recording
- Automatic result archival

## API Reference and Usage Examples

### Core Handler APIs

#### ATE Resource Manager
```python
# Initialize resource manager
ate = ate_rm.ate_rm()

# Log messages with severity levels
ate.logMessage(1, "Info message")    # Info
ate.logMessage(2, "Warning message") # Warning
ate.logMessage(3, "Error message")   # Error

# Add instruments to registry
ate.addInstrument("PowerMeter", power_meter_instance)

# Cleanup resources
ate.cleanup()
```

#### Power Meter Operations
```python
# Initialize power meter
bpm = B4500CPwrMeter(ate)

# Configure for pulse measurements
bpm.setCalculateMode("PULSE")
bpm.setTriggerMode("NORMAL")
bpm.setTriggerSource("TRIG1")

# Perform measurements
markers = bpm.CH1_Marker_Power()  # Returns [marker1, marker2]
power_data = bpm.fetch1_pwr()     # Full power analysis

# Automated pulse detection
num_pulses = bpm.findpeaks("-20")  # Threshold in dBm
bpm.plotpeaks("-20", "Test Results")  # Visualize results

# Cleanup
bpm.close()
```

#### ATC5000NG Control
```python
# Initialize ATC controller
atc = ATC5000NG(ate)

# Configure transponder mode
atc.transponderModeA()
atc.init_own_aircraft_pos()

# Set test parameters
atc.gwrite(":ATC:XPDR:FREQ 1030.0")
atc.gwrite(":ATC:XPDR:POW -60")
atc.gwrite(":ATC:XPDR:RF ON")

# Measure performance
reply_rate = atc.getPercentReply(2)
frequency = atc.getPulseFrequency(2)
delay = atc.getReplyDelay(2)

# Cleanup
atc.gwrite(":ATC:XPDR:RF OFF")
atc.close()
```

#### ARINC 429 Interface
```python
# Initialize ARINC client
arinc = ARINC_Client(ate, r"C:\Path\To\ARINC_Server")

# DME operations
arinc.writeChannel(108.5)  # Set DME channel
channel = arinc.getChannel_035()  # Read current channel
distance = arinc.getDistance_201()  # Read distance

# TCAS operations
intruders = arinc.TCAS_Read(b"READ,TCAS_OUT,INT")
all_data = arinc.TCAS_Read(b"READ,TCAS_OUT,ALL")

# Cleanup
arinc.close_ARINC_Client()
arinc.kill_ARINC_Server()
```

### Test Procedure Examples

#### Basic Sensitivity Test
```python
def run_sensitivity_test():
    # Setup
    rm = ate_rm()
    atc = ATC5000NG(rm)

    # Test parameters
    frequencies = ['1029.80', '1030.00', '1030.20']
    results = []

    for freq in frequencies:
        atc.gwrite(f':ATC:XPDR:FREQ {freq}')

        # Find minimum trigger level (MTL)
        power = -60.0
        while True:
            atc.gwrite(f':ATC:XPDR:POW {power}')
            time.sleep(1)

            reply_rate = atc.getPercentReply(2)[1]
            if reply_rate < 90.0:
                break
            power -= 1.0

        results.append(power)
        rm.logMessage(1, f"MTL at {freq} MHz: {power} dBm")

    # Cleanup
    atc.close()
    rm.cleanup()
    return results
```

#### Compression Test Example
```python
def run_compression_test():
    # Setup instruments
    ate = ate_rm()
    power_meter = B4500CPwrMeter(ate)

    # Configure power meter
    setupPwrMeter(ate)

    # Test parameters
    frequencies = range(1025, 1151, 1)  # 1025-1150 MHz
    attenuation_steps = numpy.arange(0, -30, -0.5)

    for freq in frequencies:
        power_data = []

        for atten in attenuation_steps:
            # Set transmitter parameters
            Calibration.generateCalPulse(ate, freq, atten)
            time.sleep(0.5)

            # Measure power
            power = power_meter.CH1_Marker_Power()[0]
            power_data.append(float(power))

        # Analyze compression
        p1db_result = WS_Analysis(freq, power_data, attenuation_steps)
        print(f"P1dB at {freq} MHz: {p1db_result}")

    # Cleanup
    power_meter.close()
    ate.cleanup()
```

### Configuration and Setup

#### Network Configuration
```python
# Default IP addresses (configurable)
INSTRUMENT_IPS = {
    'ATC5000NG': '*************:2001',
    'PowerMeter': '************',
    'BIT_System': '************:5121',
    'ARINC_Server': '127.0.0.1:20633'
}
```

#### File Paths and Directories
```python
# Standard test data directories
TEST_DATA_PATHS = {
    'power_curves': 'C:\\Test Data\\TXD RF\\TX Power Curves\\',
    'bit_coefficients': 'C:\\Honeywell\\TXD LRU Test SW\\BITCoefficients.toml',
    'screenshots': 'C:\\Honeywell\\Boonton ScreenShots\\',
    'arinc_server': 'C:\\Honeywell\\TXD_Qual\\TXDLib\\Handlers\\ARINC_Server\\'
}
```

### Best Practices and Guidelines

#### Error Handling Pattern
```python
def robust_measurement(instrument, command, retries=3):
    for attempt in range(retries):
        try:
            result = instrument.query(command)
            if validate_result(result):
                return result
        except Exception as e:
            if attempt == retries - 1:
                raise
            time.sleep(1)  # Wait before retry
    return None
```

#### Resource Management Pattern
```python
def test_with_cleanup():
    ate = None
    instruments = []

    try:
        ate = ate_rm()
        power_meter = B4500CPwrMeter(ate)
        atc = ATC5000NG(ate)
        instruments = [power_meter, atc]

        # Perform tests
        results = run_tests(ate, power_meter, atc)
        return results

    finally:
        # Guaranteed cleanup
        for instrument in instruments:
            try:
                instrument.close()
            except:
                pass
        if ate:
            ate.cleanup()
```

---

*This documentation provides a comprehensive overview of the TXD Qualification Test System codebase. For specific implementation details, refer to individual source files and their embedded documentation.*
