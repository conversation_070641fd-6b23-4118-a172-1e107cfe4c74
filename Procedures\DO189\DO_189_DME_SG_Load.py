# -*- coding: utf-8 -*-
"""
Created on Wed April 15 3:20:30 2021

@author: E282068
         M<PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription: This script loads the SigGen (N5172b) with the
             required files the DO-189 MOPs Tests
             
             Binary files required for these tests are loaded on the
             SigGen if they are not already there.

             NOTE: Binary files should be in the TXDLib/Handlers directory.
             
INPUTS:      None
OUTPUTS:     None

HISTORY:                             
03/10/2021   MRS   Initial Release.                                 
"""

#Required Libraries
#none

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import N5172BSigGen


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def load_siggen_waforms(rm,sg):
    """ Pre-Loads the SigGen with required Waveform files for all tests in
    DME sequences. """

    #check for waveforms loaded on the SigGen
    wfn = sg.getWaveforms()
    rm.logMessage(0,"Pre-Loaded Waveforms" + str(wfn) )
    #split string
    wfns = wfn.split(',')
    print(wfns)

    #Check if List already has file loaded, if not load file
    if '"2_2_10_1_DME_INTF_3600' not in wfns:
        #Download the WaveForm File
        res = sg.downloadwaveform('2_2_10_1_DME_INTF_3600') 
        rm.logMessage(0,"Download WaveForm: 2_2_10_1_DME_INTF_3600: " + str(res) )

    if '"2_2_10_2_DME_INTF_3600' not in wfns:
        #Download the WaveForm File
        res = sg.downloadwaveform('2_2_10_2_DME_INTF_3600') 
        rm.logMessage(0,"Download WaveForm: 2_2_10_2_DME_INTF_3600: " + str(res) )

    if '"2_2_10_3_X_DME_INTF_3600' not in wfns:
        #Download the WaveForm File
        res = sg.downloadwaveform('2_2_10_3_X_DME_INTF_3600') 
        rm.logMessage(0,"Download WaveForm: 2_2_10_3_X_DME_INTF_3600: " + str(res) )

    if '"2_2_10_3_Y_DME_INTF_3600' not in wfns:
        #Download the WaveForm File
        res = sg.downloadwaveform('2_2_10_3_Y_DME_INTF_3600') 
        rm.logMessage(0,"Download WaveForm: 2_2_10_3_Y_DME_INTF_3600: " + str(res) )

    if '"TACAN' not in wfns:
        #Download the WaveForm File
        res = sg.downloadwaveform('TACAN') 
        rm.logMessage(0,"Download WaveForm: TACAN: " + str(res) )

    if '"2212_DME_1400' not in wfns:
        #Download the WaveForm File
        res = sg.downloadwaveform('2212_DME_1400') 
        rm.logMessage(0,"Download WaveForm: 2212_DME_1400 " + str(res) )


 
############################################################################## 

if __name__ == "__main__":

    #Initialize Intruuments
    rm = ate_rm()

	
    #Initialize Signal Generator
    sg = N5172BSigGen(rm)
    sg.Reset()
    #Load Waveform into SG (if not pre-loaded)
    load_siggen_waforms(rm,sg) 
    
    
    #Close the ATC and Signal Generator. 
    sg.close()
    
