# -*- coding: utf-8 -*-
"""
Created on Wed Mar 11 14:31:43 2020

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS Qualification Test Group
         
Description:
    This class provides utility functions for the Keysight NI5172B Signal
    Generator
    
Inputs:  see class methods
Outputs: see class methods

History:

03/11/2020   MRS  Initial Release
04/29/2020  MR  Added basic error handling a option controls.
03/18/2021  MRS Added Paths for download files to SigGen
"""

import time
import subprocess
import clr
import sys
import os

class N5172BSigGen():
    def __init__(self, ate_rm):
        self.siggenIP = "TCPIP0::************::INSTR"
        self.resourceManager = ate_rm
        self.siggen = self.resourceManager.rm.open_resource(self.siggenIP)
        # self.tvl=ate_rm.tvl
        tvlTxt = "Resource Opened."
        self.basicTvlMsg(tvlTxt)

##################################################
# Basic Commands
##################################################
    def basicWrite(self, cmd):
        tvlTxt = "> %s" % cmd
        self.basicTvlMsg(tvlTxt)
        resp = self.siggen.write("%s" % cmd)
        return resp

    def basicQuery(self, cmd,logEnable=True):
        tvlTxt = "> %s" % cmd
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        resp = self.siggen.query("%s" % cmd).strip()
        tvlTxt = "< %s" % resp
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        return resp

    def basicTvlMsg(self, tvlTxt):
        self.resourceManager.logMessage(1, tvlTxt)

    def Ident(self):
        """ Returns Instrument Ident. """
        return self.basicQuery("*IDN?")

    def Reset(self):
        """ Resets the Instrument. """
        self.basicWrite("*RST")
        self.resourceManager.logMessage(1, "SigGen Resetting...")
        time.sleep(5)
        self.resourceManager.logMessage(1, "SigGen complete\n")
    
    def close(self):
        """ Closes the Session. """
        self.siggen.close()

##################################################
# Frequency, Amplitude, Power, RF  Commands
##################################################

    def setFrequency(self, Frequency):
        """ Set the frequency of the signal generator, input as string as <val><units>. """
        self.basicWrite("FREQuency:CW {}".format(str(Frequency)))

    def getFrequency(self):
        """ Get the SigGen Frequency. """
        return self.basicQuery("FREQuency:CW?")
    
    def setPower(self, Power):
        """ Set the Power Level of the signal generator, input as string as <val><units>. """
        self.basicWrite("POWer:AMPLitude {}".format(str(Power)))
        
    def getPower(self):
        """ Get the SigGen Power level. """
        return self.basicQuery("POW:AMPL?")
       
    def setRF(self, onoff):
        """ Turn ON/OFF RF Power, input as string 'ON' or 'OFF'. """
        if onoff.lower() in ['on', 'off']:
            self.basicWrite("OUTPut:STATe {}".format(str(onoff)))
        else:
            self.resourceManager.logMessage(3,"On/off state incorrect {}".format(onoff))

    def getRF(self):
        """ Get RF Power State. """
        return self.basicQuery("OUTPut:STATe?")

    def setModulationState(self, onoff):
        """ Get Modulaton State, input as string 'ON' or 'OFF' """
        if onoff.lower() in ['on', 'off']:
            self.basicWrite("OUTPut:MODulation:STATe {}".format(str(onoff)))
        else:
            self.resourceManager.logMessage(3, "On/off state incorrect {}".format(onoff))
    
    def getModulationState(self):
        """ Get Modulaton State. """
        return self.basicQuery("OUTPut:MODulation:STATe?")
        
    def getWaveforms(self):
        """ Gets a list of WFM1 Names loaded on the SigGen. """
        return self.basicQuery(':MMEM:CAT? "WFM1:"')
    
##################################################
# File Load, Save and Replay Commands
##################################################

    def downloadwaveform(self, fname):
        """ DownLoads a data to ARB Memory of the SigGen
            fname    - file name of the binary i/q data. """
        fpath = "C:/Honeywell/TXD_Qual/TXDLib/Handlers/"
        os.chdir(fpath)
        cmd_str = fpath + "Siggen " + fname + " " + self.siggenIP
        self.resourceManager.logMessage(1, "CMD Str: " +  cmd_str)
        result = subprocess.Popen(cmd_str, shell=False,
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE)        
        output, error = result.communicate()
        time.sleep(30)
        self.resourceManager.logMessage(3, "Output: " + str(output) + " Error: " + str(error))
        return self.basicQuery(':MMEM:CAT? "WFM1:"')

    def replaywaveform(self, fname, rate):
       """ This routine will play a previoulsy downloaded waveform with 
       file name 'fname' at the sample rate supplied in the 'rate' argument
       as a real number.  """
       
       cmd_str = ':RADio:ARB:WAVeform "WFM1:' + fname + '"'
       self.basicWrite(cmd_str)
       
       self.basicWrite(':RAD:ARB:SCL:RATE ' + str(rate))

       self.basicWrite(':RADio:ARB:STATe ON')
       self.basicWrite(':OUTPut:MODulation:STATe ON')
       self.basicWrite(':OUTPut:STATe ON')
